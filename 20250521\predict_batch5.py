"""
预测第五批测试数据的钢水温度
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any

# 添加项目目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from steel_temp_prediction.data_preprocessing import preprocess_data, clean_data
from steel_temp_prediction.feature_engineering import engineer_features
from steel_temp_prediction.utils import create_directory, save_model, load_model

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch5_prediction.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_batch5_data(file_path: str) -> pd.DataFrame:
    """
    加载第五批测试数据

    Args:
        file_path: 数据文件路径

    Returns:
        加载的数据DataFrame
    """
    logger.info(f"加载第五批测试数据: {file_path}")
    try:
        data = pd.read_excel(file_path)
        logger.info(f"数据加载成功，形状: {data.shape}")
        return data
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        raise

def load_trained_models(models_dir: str = "results") -> Dict[str, Any]:
    """
    加载训练好的模型

    Args:
        models_dir: 模型保存目录

    Returns:
        模型字典 {模型名称: 模型对象}
    """
    logger.info(f"从 {models_dir} 加载训练好的模型")
    models = {}

    # 加载顺序思维模型
    try:
        sequential_model_path = os.path.join(models_dir, "sequential_thinking_model.pkl")
        if os.path.exists(sequential_model_path):
            models["Sequential Thinking Model"] = load_model(sequential_model_path)
            logger.info("顺序思维模型加载成功")
    except Exception as e:
        logger.error(f"加载顺序思维模型失败: {e}", exc_info=True)

    # 加载基础模型
    base_model_names = ["xgboost", "lightgbm", "random_forest", "ridge", "lasso", "svr"]
    for name in base_model_names:
        try:
            model_path = os.path.join(models_dir, f"{name}_model.pkl")
            if os.path.exists(model_path):
                models[name] = load_model(model_path)
                logger.info(f"{name} 模型加载成功")
        except Exception as e:
            logger.error(f"加载 {name} 模型失败: {e}", exc_info=True)

    logger.info(f"共加载 {len(models)} 个模型")
    return models

def predict_batch5(batch5_data: pd.DataFrame, models: Dict[str, Any]) -> pd.DataFrame:
    """
    对第五批数据进行预测

    Args:
        batch5_data: 第五批测试数据
        models: 模型字典

    Returns:
        包含预测结果的DataFrame
    """
    logger.info("开始预处理第五批数据")

    # 清洗数据
    batch5_cleaned = clean_data(batch5_data.copy())
    logger.info(f"数据清洗完成，形状: {batch5_cleaned.shape}")

    # 工程化特征
    batch5_processed = batch5_cleaned.copy()

    logger.info("开始特征工程")
    try:
        # 使用与训练时相同的特征工程流程
        from steel_temp_prediction.feature_engineering import create_basic_features, create_metallurgical_features
        from steel_temp_prediction.feature_engineering import create_time_series_features, create_interaction_features
        from steel_temp_prediction.feature_engineering import create_polynomial_features, select_best_features

        # 创建基础特征
        batch5_processed = create_basic_features(batch5_processed)

        # 创建冶金特征
        batch5_processed = create_metallurgical_features(batch5_processed)

        # 创建时序特征
        batch5_processed = create_time_series_features(batch5_processed)

        # 创建交互特征
        batch5_processed = create_interaction_features(batch5_processed)

        # 创建多项式特征
        key_features = [
            '铁水温度', '铁水SI', '铁水C', '铁水废钢比', '单位铁水供氧量',
            '吹氧强度', '总处理时间min'
        ]
        key_features = [f for f in key_features if f in batch5_processed.columns]
        batch5_processed = create_polynomial_features(batch5_processed, degree=2, feature_names=key_features)

        logger.info("特征工程完成")
    except Exception as e:
        logger.error(f"特征工程失败: {e}", exc_info=True)
        raise

    # 准备预测特征
    logger.info("准备预测特征")
    try:
        # 移除非数值列
        numeric_cols = batch5_processed.select_dtypes(include=['float64', 'int64']).columns
        X_batch5 = batch5_processed[numeric_cols]

        # 保存原始数据，用于结果展示
        original_data = batch5_data.copy()

        logger.info(f"预测特征准备完成，特征数量: {X_batch5.shape[1]}")
    except Exception as e:
        logger.error(f"准备预测特征失败: {e}", exc_info=True)
        raise

    # 使用各个模型进行预测
    logger.info("开始使用各模型进行预测")
    predictions = {}

    # 处理预测特征中的 NaN 和 Inf 值
    X_batch5_cleaned = X_batch5.replace([np.inf, -np.inf], np.nan)
    X_batch5_cleaned = X_batch5_cleaned.fillna(X_batch5_cleaned.median())
    logger.info(f"处理预测特征中的 NaN 和 Inf 值后，形状: {X_batch5_cleaned.shape}")

    for name, model in models.items():
        try:
            # 确保特征与模型期望的特征匹配
            if hasattr(model, 'feature_names_'):
                # 对于有feature_names_属性的模型（如XGBoost）
                model_features = model.feature_names_
                common_features = [f for f in model_features if f in X_batch5_cleaned.columns]
                if len(common_features) < len(model_features):
                    logger.warning(f"{name} 模型期望 {len(model_features)} 个特征，但只找到 {len(common_features)} 个匹配特征")
                # 确保模型使用的特征列顺序与训练时一致
                pred = model.predict(X_batch5_cleaned[common_features])
            else:
                # 对于没有feature_names_属性的模型
                pred = model.predict(X_batch5_cleaned)

            predictions[name] = pred
            logger.info(f"{name} 模型预测完成，预测范围: {np.min(pred):.2f}°C - {np.max(pred):.2f}°C")
        except Exception as e:
            logger.error(f"{name} 模型预测失败: {e}", exc_info=True)

    # 创建结果DataFrame
    logger.info("整合预测结果")
    results = pd.DataFrame()

    # 添加原始数据的关键列
    if '炉号' in original_data.columns:
        results['炉号'] = original_data['炉号']
    if '钢种' in original_data.columns:
        results['钢种'] = original_data['钢种']
    if '铁水温度' in original_data.columns:
        results['铁水温度'] = original_data['铁水温度']

    # 添加各模型的预测结果
    for name, pred in predictions.items():
        results[f'{name}_预测温度'] = pred

    # 计算综合预测温度（如果有顺序思维模型，使用它的结果，否则使用所有模型的平均值）
    if "Sequential Thinking Model" in predictions:
        results['综合预测温度'] = predictions["Sequential Thinking Model"]
    else:
        results['综合预测温度'] = np.mean([pred for pred in predictions.values()], axis=0)

    # 处理结果中的 NaN 和 Inf 值，特别是预测温度列
    for col in results.columns:
        if '预测温度' in col or col == '综合预测温度':
            results[col] = results[col].replace([np.inf, -np.inf], np.nan)
            results[col] = results[col].fillna(results[col].median() if not results[col].median() is np.nan else 0)

    logger.info("预测完成")
    return results

def save_prediction_results(results: pd.DataFrame, output_path: str = "batch5_predictions.xlsx") -> None:
    """
    保存预测结果

    Args:
        results: 预测结果DataFrame
        output_path: 输出文件路径
    """
    logger.info(f"保存预测结果到: {output_path}")
    try:
        results.to_excel(output_path, index=False)
        logger.info("预测结果保存成功")
    except Exception as e:
        logger.error(f"保存预测结果失败: {e}")
        raise

def analyze_prediction_results(results: pd.DataFrame) -> None:
    """
    分析预测结果

    Args:
        results: 预测结果DataFrame
    """
    logger.info("分析预测结果")

    # 计算综合预测温度的统计信息
    mean_temp = results['综合预测温度'].mean()
    std_temp = results['综合预测温度'].std()
    min_temp = results['综合预测温度'].min()
    max_temp = results['综合预测温度'].max()

    logger.info(f"综合预测温度统计:")
    logger.info(f"  平均值: {mean_temp:.2f}°C")
    logger.info(f"  标准差: {std_temp:.2f}°C")
    logger.info(f"  最小值: {min_temp:.2f}°C")
    logger.info(f"  最大值: {max_temp:.2f}°C")

    # 如果有钢种信息，按钢种分析
    if '钢种' in results.columns:
        logger.info("按钢种分析预测温度:")
        for steel_type, group in results.groupby('钢种'):
            logger.info(f"  钢种 {steel_type}:")
            logger.info(f"    样本数: {len(group)}")
            logger.info(f"    平均预测温度: {group['综合预测温度'].mean():.2f}°C")
            logger.info(f"    标准差: {group['综合预测温度'].std():.2f}°C")

    # 创建预测温度分布图
    logger.info("创建预测温度分布图")
    # 过滤掉 NaN/Inf 值用于绘图
    temp_for_hist = results['综合预测温度'].dropna().replace([np.inf, -np.inf], np.nan).dropna()
    if not temp_for_hist.empty:
        plt.figure(figsize=(10, 6))
        plt.hist(temp_for_hist, bins=20, alpha=0.7)
        plt.axvline(mean_temp, color='r', linestyle='--', label=f'平均值: {mean_temp:.2f}°C')
        plt.title('第五批数据预测温度分布')
        plt.xlabel('预测温度 (°C)')
        plt.ylabel('频次')
        plt.legend()
        plt.grid(alpha=0.3)
        plt.savefig('batch5_temperature_distribution.png', dpi=300, bbox_inches='tight')
        logger.info("预测温度分布图已保存")
    else:
        logger.warning("综合预测温度列没有有效数据，跳过绘制分布图")

    # 如果有铁水温度，分析铁水温度与预测温度的关系
    if '铁水温度' in results.columns:
        logger.info("创建铁水温度与预测出钢温度关系图")
        # 过滤掉 NaN/Inf 值用于绘图
        plot_data = results[['铁水温度', '综合预测温度']].dropna().replace([np.inf, -np.inf], np.nan).dropna()
        if not plot_data.empty:
            plt.figure(figsize=(10, 6))
            plt.scatter(plot_data['铁水温度'], plot_data['综合预测温度'], alpha=0.5)
            plt.title('铁水温度与预测出钢温度关系')
            plt.xlabel('铁水温度 (°C)')
            plt.ylabel('预测出钢温度 (°C)')
            plt.grid(alpha=0.3)
            plt.savefig('batch5_hotmetal_vs_prediction.png', dpi=300, bbox_inches='tight')
            logger.info("铁水温度与预测温度关系图已保存")
        else:
            logger.warning("铁水温度或综合预测温度列没有有效数据，跳过绘制散点图")

    # 比较不同模型的预测结果
    model_columns = [col for col in results.columns if col.endswith('_预测温度')]
    if len(model_columns) > 1:
        logger.info("比较不同模型的预测结果:")
        for col in model_columns:
            model_name = col.replace('_预测温度', '')
            logger.info(f"  {model_name}:")
            logger.info(f"    平均值: {results[col].mean():.2f}°C")
            logger.info(f"    标准差: {results[col].std():.2f}°C")
            logger.info(f"    与综合预测的平均差异: {(results[col] - results['综合预测温度']).abs().mean():.2f}°C")

def main():
    """主函数"""
    logger.info("开始处理第五批测试数据")

    # 第五批数据文件路径
    batch5_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"

    # 检查文件是否存在
    if not os.path.exists(batch5_file):
        logger.error(f"文件不存在: {batch5_file}")
        print(f"错误: 找不到文件 {batch5_file}")
        return

    # 创建结果目录
    create_directory("results")

    # 加载第五批数据
    batch5_data = load_batch5_data(batch5_file)

    # 加载训练好的模型
    models = load_trained_models()

    # 预测第五批数据
    results = predict_batch5(batch5_data, models)

    # 定义输出文件路径
    output_path = "batch5_predictions.xlsx"

    # 保存预测结果
    save_prediction_results(results, output_path)

    # 处理结果中的 NaN 和 Inf 值，特别是预测温度列，确保绘图前数据干净
    for col in results.columns:
        if '预测温度' in col or col == '综合预测温度':
            results[col] = results[col].replace([np.inf, -np.inf], np.nan)
            results[col] = results[col].fillna(results[col].median() if not results[col].median() is np.nan else 0)
    logger.info("绘图前处理结果中的 NaN 和 Inf 值完成")

    # 可视化结果
    logger.info("开始可视化预测结果")

    logger.info("第五批数据处理完成")
    print("第五批数据预测完成，结果已保存到 batch5_predictions.xlsx")

if __name__ == "__main__":
    main()
