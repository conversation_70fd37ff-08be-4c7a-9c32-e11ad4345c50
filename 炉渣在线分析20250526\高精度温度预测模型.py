#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度钢水温度预测模型
基于FactSage热力学数据和三元相图理论
提高±20°C命中率和泛化性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize_scalar
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedTemperaturePredictor:
    """高精度温度预测器"""
    
    def __init__(self):
        # FactSage热力学数据（基于实际转炉条件修正）
        self.factsage_data = {
            # 反应热焓（kJ/mol，1600°C）
            'C_to_CO': -110.5,      # C + 1/2O2 → CO
            'C_to_CO2': -393.5,     # C + O2 → CO2
            'Si_to_SiO2': -910.7,   # Si + O2 → SiO2
            'Mn_to_MnO': -385.2,    # Mn + 1/2O2 → MnO
            'P_to_P2O5': -1640.1,   # 2P + 5/2O2 → P2O5
            'Fe_to_FeO': -272.0,    # Fe + 1/2O2 → FeO
            
            # 热容数据（kJ/mol·K）
            'steel_cp': 0.046,      # 钢水热容
            'slag_cp': 0.085,       # 炉渣热容
            'gas_cp': 0.029         # 气体热容
        }
        
        # 三元相图参数（CaO-SiO2-FeO系统）
        self.phase_diagram_params = {
            'liquidus_base': 1713,   # SiO2熔点
            'cao_effect': -2.8,      # CaO对液相线的影响
            'feo_effect': -1.5,      # FeO对液相线的影响
            'interaction_cao_sio2': 0.8,  # CaO-SiO2交互作用
            'interaction_feo_sio2': 0.6   # FeO-SiO2交互作用
        }
        
        # 动态热平衡参数
        self.thermal_dynamics = {
            'oxygen_flow_effect': 0.15,     # 供氧强度对温度的影响
            'lance_height_effect': 0.08,    # 枪位对热效率的影响
            'scrap_preheating': 0.12,       # 废钢预热效应
            'refractory_heat_loss': 0.025,  # 耐火材料热损失
            'radiation_loss_coeff': 2.5e-8, # 辐射损失系数
            'convection_loss_coeff': 0.018  # 对流损失系数
        }
        
        # 工艺参数影响系数
        self.process_effects = {
            'blow_time_nonlinear': True,     # 吹氧时间非线性效应
            'scrap_ratio_effect': True,      # 废钢比影响
            'flux_addition_effect': True,    # 造渣料加入影响
            'steel_grade_effect': True       # 钢种影响
        }
    
    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default
    
    def calculate_reaction_heat_factsage(self, composition, hot_metal_mass):
        """基于FactSage数据计算反应热"""
        # 分子量
        mw = {'C': 12.01, 'Si': 28.09, 'Mn': 54.94, 'P': 30.97, 'Fe': 55.85}
        
        # 计算各元素氧化的摩尔数
        c_moles = hot_metal_mass * composition['C'] * 0.88 / 100 / mw['C']  # 88%脱碳率
        si_moles = hot_metal_mass * composition['Si'] * 0.95 / 100 / mw['Si']
        mn_moles = hot_metal_mass * composition['Mn'] * 0.80 / 100 / mw['Mn']
        p_moles = hot_metal_mass * composition['P'] * 0.85 / 100 / mw['P']
        fe_moles = hot_metal_mass * 0.015 / mw['Fe']  # 1.5%铁损
        
        # CO/CO2比例（基于温度和氧势）
        co_ratio = self.calculate_co_ratio(composition)
        co2_ratio = 1 - co_ratio
        
        # 计算反应热（kJ）
        decarb_heat = -(c_moles * co_ratio * self.factsage_data['C_to_CO'] +
                       c_moles * co2_ratio * self.factsage_data['C_to_CO2'])
        
        si_heat = -si_moles * self.factsage_data['Si_to_SiO2']
        mn_heat = -mn_moles * self.factsage_data['Mn_to_MnO']
        p_heat = -p_moles * self.factsage_data['P_to_P2O5'] / 2  # 2P参与反应
        fe_heat = -fe_moles * self.factsage_data['Fe_to_FeO']
        
        total_heat = decarb_heat + si_heat + mn_heat + p_heat + fe_heat
        
        return {
            'total_heat': total_heat,
            'decarb_heat': decarb_heat,
            'si_heat': si_heat,
            'mn_heat': mn_heat,
            'p_heat': p_heat,
            'fe_heat': fe_heat,
            'co_ratio': co_ratio
        }
    
    def calculate_co_ratio(self, composition):
        """计算CO/CO2比例（基于热力学平衡）"""
        # 简化的CO/CO2平衡计算
        # 基于Boudouard反应：C + CO2 ⇌ 2CO
        # 高温下CO比例增加
        base_co_ratio = 0.7
        
        # 碳含量影响
        c_effect = min(0.2, composition['C'] / 20)  # 碳含量越高，CO比例越高
        
        # 温度影响（假设基础温度1600°C）
        temp_effect = 0.1  # 高温有利于CO生成
        
        co_ratio = base_co_ratio + c_effect + temp_effect
        return min(0.95, max(0.5, co_ratio))
    
    def calculate_liquidus_temperature(self, slag_composition):
        """基于三元相图计算液相线温度"""
        cao_pct = slag_composition.get('CaO', 45)
        sio2_pct = slag_composition.get('SiO2', 16)
        feo_pct = slag_composition.get('FeO', 20)
        
        # 归一化
        total = cao_pct + sio2_pct + feo_pct
        if total > 0:
            x_cao = cao_pct / total
            x_sio2 = sio2_pct / total
            x_feo = feo_pct / total
        else:
            x_cao, x_sio2, x_feo = 0.45, 0.16, 0.20
        
        # 基于三元相图的液相线温度计算
        liquidus_temp = (self.phase_diagram_params['liquidus_base'] +
                        self.phase_diagram_params['cao_effect'] * x_cao * 100 +
                        self.phase_diagram_params['feo_effect'] * x_feo * 100 +
                        self.phase_diagram_params['interaction_cao_sio2'] * x_cao * x_sio2 * 100 +
                        self.phase_diagram_params['interaction_feo_sio2'] * x_feo * x_sio2 * 100)
        
        return max(1400, min(liquidus_temp, 1650))
    
    def calculate_heat_losses(self, row, blow_time, steel_temp):
        """计算多种热损失"""
        hot_metal_mass = self.safe_convert(row['铁水'], 90)
        
        # 1. 辐射热损失（Stefan-Boltzmann定律）
        surface_area = (hot_metal_mass / 7.0) ** (2/3) * 10  # 估算表面积
        temp_k = steel_temp + 273.15
        radiation_loss = (self.thermal_dynamics['radiation_loss_coeff'] * 
                         surface_area * (temp_k**4 - 298**4) * blow_time / 1000)
        
        # 2. 对流热损失
        convection_loss = (self.thermal_dynamics['convection_loss_coeff'] * 
                          hot_metal_mass * (steel_temp - 25) * blow_time / 3600)
        
        # 3. 耐火材料传导损失
        refractory_loss = (self.thermal_dynamics['refractory_heat_loss'] * 
                          hot_metal_mass * blow_time / 60)
        
        # 4. 气体带走热损失
        gas_flow = self.safe_convert(row['气体流量流速平均'], 100000)
        gas_loss = gas_flow * self.factsage_data['gas_cp'] * (steel_temp - 25) * blow_time / 3600000
        
        total_loss = radiation_loss + convection_loss + refractory_loss + gas_loss
        
        return {
            'total_loss': total_loss,
            'radiation_loss': radiation_loss,
            'convection_loss': convection_loss,
            'refractory_loss': refractory_loss,
            'gas_loss': gas_loss
        }
    
    def calculate_process_corrections(self, row):
        """计算工艺参数修正"""
        corrections = {}
        
        # 1. 供氧强度修正
        oxygen_intensity = self.safe_convert(row['累氧实际'], 5000) / 1000  # 标准化
        corrections['oxygen_effect'] = oxygen_intensity * self.thermal_dynamics['oxygen_flow_effect']
        
        # 2. 枪位修正
        lance_angle = self.safe_convert(row['最大角度'], 15)
        corrections['lance_effect'] = (lance_angle - 15) * self.thermal_dynamics['lance_height_effect']
        
        # 3. 废钢比修正
        hot_metal = self.safe_convert(row['铁水'], 90)
        scrap = self.safe_convert(row['废钢'], 20)
        scrap_ratio = scrap / (hot_metal + scrap) if (hot_metal + scrap) > 0 else 0.2
        corrections['scrap_ratio_effect'] = (scrap_ratio - 0.2) * 200  # 温度修正
        
        # 4. 造渣料影响
        lime = self.safe_convert(row['石灰'], 4000)
        dolomite = self.safe_convert(row['白云石'], 700)
        flux_total = lime + dolomite
        corrections['flux_effect'] = (flux_total - 4700) / 100 * 2  # 每100kg影响2°C
        
        # 5. 钢种修正
        steel_grade = str(row.get('钢种', 'Q355D'))
        if 'Q235' in steel_grade:
            corrections['grade_effect'] = -10  # 低碳钢温度稍低
        elif 'Q355' in steel_grade:
            corrections['grade_effect'] = 0    # 标准
        elif 'Q420' in steel_grade:
            corrections['grade_effect'] = 5    # 高强钢温度稍高
        else:
            corrections['grade_effect'] = 0
        
        return corrections
    
    def predict_temperature_advanced(self, row, corrected_composition, slag_composition):
        """高精度温度预测"""
        # 基础数据
        hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)
        hot_metal_mass = self.safe_convert(row['铁水'], 90)
        scrap_mass = self.safe_convert(row['废钢'], 20)
        blow_time = self.safe_convert(row['吹氧时间s'], 600)
        
        # 1. 基于FactSage计算反应热
        reaction_heat_data = self.calculate_reaction_heat_factsage(corrected_composition, hot_metal_mass)
        total_reaction_heat = reaction_heat_data['total_heat']
        
        # 2. 废钢熔化和预热耗热
        scrap_melting_heat = scrap_mass * 1200  # kJ
        scrap_preheating = scrap_mass * 0.75 * (hot_metal_temp - 25)  # 预热到铁水温度
        
        # 3. 净反应热
        net_heat = total_reaction_heat - scrap_melting_heat - scrap_preheating
        
        # 4. 初步温升计算
        total_steel = hot_metal_mass + scrap_mass
        steel_heat_capacity = total_steel * self.factsage_data['steel_cp'] * 55.85  # 转换为kJ/K
        initial_temp_rise = net_heat / steel_heat_capacity
        
        # 5. 初步预测温度
        preliminary_temp = hot_metal_temp + initial_temp_rise
        
        # 6. 计算热损失
        heat_losses = self.calculate_heat_losses(row, blow_time, preliminary_temp)
        temp_loss_from_heat = heat_losses['total_loss'] / steel_heat_capacity
        
        # 7. 工艺参数修正
        process_corrections = self.calculate_process_corrections(row)
        total_process_correction = sum(process_corrections.values())
        
        # 8. 液相线温度约束
        liquidus_temp = self.calculate_liquidus_temperature(slag_composition)
        min_superheat = 50  # 最小过热度50°C
        
        # 9. 最终温度计算
        final_temp = preliminary_temp - temp_loss_from_heat + total_process_correction
        
        # 10. 合理性约束（不使用硬性上下限）
        if final_temp < liquidus_temp + min_superheat:
            final_temp = liquidus_temp + min_superheat + 20
        
        # 软约束：极端值处理
        if final_temp < 1500:
            final_temp = 1500 + (final_temp - 1500) * 0.5  # 软约束
        elif final_temp > 1850:
            final_temp = 1850 - (final_temp - 1850) * 0.3  # 软约束
        
        return {
            'predicted_temp': final_temp,
            'reaction_heat': total_reaction_heat,
            'heat_losses': heat_losses['total_loss'],
            'process_corrections': total_process_correction,
            'liquidus_temp': liquidus_temp,
            'superheat': final_temp - liquidus_temp,
            'co_ratio': reaction_heat_data['co_ratio']
        }

def main():
    """主函数：测试高精度温度预测"""
    print("=== 高精度钢水温度预测模型测试 ===")
    print("基于FactSage热力学数据和三元相图理论\n")
    
    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return
    
    # 初始化预测器
    temp_predictor = AdvancedTemperaturePredictor()
    
    # 测试前100条记录
    results = []
    print("开始高精度温度预测测试...")
    
    for idx in range(min(100, len(df))):
        row = df.iloc[idx]
        
        try:
            # 模拟炉渣成分（实际应用中从炉渣预测模型获取）
            slag_composition = {
                'CaO': 45.0, 'SiO2': 16.0, 'FeO': 20.0,
                'MgO': 8.0, 'MnO': 6.0, 'P2O5': 3.0
            }
            
            # 模拟修正后的铁水成分
            corrected_composition = {
                'C': 4.2, 'Si': temp_predictor.safe_convert(row['铁水SI'], 0.4),
                'Mn': temp_predictor.safe_convert(row['铁水MN'], 0.17),
                'P': temp_predictor.safe_convert(row['铁水P'], 0.13),
                'S': temp_predictor.safe_convert(row['铁水S'], 0.03)
            }
            
            # 高精度温度预测
            temp_result = temp_predictor.predict_temperature_advanced(
                row, corrected_composition, slag_composition
            )
            
            actual_temp = temp_predictor.safe_convert(row['钢水温度'])
            if actual_temp > 0:
                temp_error = abs(temp_result['predicted_temp'] - actual_temp)
                
                result = {
                    '炉号': row['炉号'],
                    '实际温度': actual_temp,
                    '预测温度': temp_result['predicted_temp'],
                    '温度偏差': temp_error,
                    '反应热': temp_result['reaction_heat'],
                    '热损失': temp_result['heat_losses'],
                    '工艺修正': temp_result['process_corrections'],
                    '液相线温度': temp_result['liquidus_temp'],
                    '过热度': temp_result['superheat'],
                    'CO比例': temp_result['co_ratio']
                }
                results.append(result)
        
        except Exception as e:
            print(f"处理第{idx}行数据时出错：{e}")
            continue
    
    # 分析结果
    if results:
        results_df = pd.DataFrame(results)
        
        print(f"\n=== 高精度温度预测结果分析 ===")
        print(f"测试样本数: {len(results_df)}")
        
        temp_errors = results_df['温度偏差']
        print(f"平均绝对误差: {temp_errors.mean():.1f}°C")
        print(f"标准偏差: {temp_errors.std():.1f}°C")
        print(f"±10°C精度: {(temp_errors <= 10).sum() / len(temp_errors) * 100:.1f}%")
        print(f"±15°C精度: {(temp_errors <= 15).sum() / len(temp_errors) * 100:.1f}%")
        print(f"±20°C精度: {(temp_errors <= 20).sum() / len(temp_errors) * 100:.1f}%")
        print(f"±30°C精度: {(temp_errors <= 30).sum() / len(temp_errors) * 100:.1f}%")
        
        print(f"\n温度预测范围: {results_df['预测温度'].min():.1f} - {results_df['预测温度'].max():.1f}°C")
        print(f"实际温度范围: {results_df['实际温度'].min():.1f} - {results_df['实际温度'].max():.1f}°C")
        
        # 保存测试结果
        results_df.to_excel('高精度温度预测测试结果.xlsx', index=False)
        print(f"\n测试结果已保存到: 高精度温度预测测试结果.xlsx")
    
    print("\n=== 高精度温度预测模型测试完成 ===")

if __name__ == "__main__":
    main()
