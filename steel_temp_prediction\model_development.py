"""
Model development module for the steel temperature prediction model.
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from sklearn.svm import SVR
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV, KFold
from sklearn.metrics import make_scorer
import xgboost as xgb
import lightgbm as lgb
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import logging
from typing import Dict, List, Tuple, Union, Any, Optional

# 新增导入 Optuna
import optuna
# 确保optuna日志级别不覆盖我们的设置 (可选，但有时有帮助)
optuna.logging.set_verbosity(optuna.logging.WARNING)

# 新增导入 CatBoostRegressor
from catboost import CatBoostRegressor

from steel_temp_prediction.config import MODEL_PARAMS, EVALUATION_METRICS, CV_FOLDS
from steel_temp_prediction.utils import calculate_metrics

# Set up logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
logger = logging.getLogger(__name__)

class SequentialThinkingModel:
    """
    Sequential Thinking Model for steel temperature prediction.

    This model combines multiple base models in a sequential manner,
    where each stage focuses on different aspects of the prediction task.
    """

    def __init__(self, base_models: List[Any] = None, meta_model: Any = None, 
                 include_original_features_in_meta: bool = True,
                 include_prediction_interactions: bool = False):
        """
        Initialize the Sequential Thinking Model.

        Args:
            base_models: List of base models for the first stage
            meta_model: Meta model for the second stage
            include_original_features_in_meta: Whether to include original features in meta-learner input.
            include_prediction_interactions: Whether to include pairwise products of base model predictions.
        """
        self.base_models = base_models or []
        self.meta_model = meta_model
        self.base_model_names = []
        self.feature_importances_ = None
        self.meta_feature_names_ = []
        self.include_original_features_in_meta = include_original_features_in_meta
        self.include_prediction_interactions = include_prediction_interactions

    def add_base_model(self, model: Any, name: str = None) -> None:
        """
        Add a base model to the ensemble.

        Args:
            model: Model to add
            name: Name of the model
        """
        self.base_models.append(model)
        self.base_model_names.append(name or f"Model_{len(self.base_models)}")
        logger.info(f"Added base model: {self.base_model_names[-1]}")

    def _create_meta_features(self, X_original: pd.DataFrame, base_predictions: np.ndarray) -> Tuple[pd.DataFrame, List[str]]:
        """
        Create features for the meta-model.

        Args:
            X_original: Original features.
            base_predictions: Predictions from base models (shape: n_samples, n_base_models).

        Returns:
            A DataFrame of meta-features and a list of their names.
        """
        meta_features_list = []
        meta_feature_names = []

        # Add base model predictions
        for i, name in enumerate(self.base_model_names):
            meta_features_list.append(pd.Series(base_predictions[:, i], name=f"pred_{name}", index=X_original.index))
            meta_feature_names.append(f"pred_{name}")
        
        # Optionally add pairwise products of base model predictions
        if self.include_prediction_interactions and len(self.base_models) > 1:
            from itertools import combinations
            for i, j in combinations(range(len(self.base_models)), 2):
                model1_name = self.base_model_names[i]
                model2_name = self.base_model_names[j]
                interaction_name = f"pred_interact_{model1_name}_x_{model2_name}"
                interaction_feature = pd.Series(base_predictions[:, i] * base_predictions[:, j], name=interaction_name, index=X_original.index)
                meta_features_list.append(interaction_feature)
                meta_feature_names.append(interaction_name)
                logger.info(f"Added prediction interaction feature: {interaction_name}")

        # Optionally add original features
        if self.include_original_features_in_meta:
            meta_features_list.append(X_original)
            meta_feature_names.extend(X_original.columns.tolist())

        if not meta_features_list:
            return pd.DataFrame(index=X_original.index), []

        meta_features_df = pd.concat(meta_features_list, axis=1)
        return meta_features_df, meta_feature_names

    def set_meta_model(self, model: Any) -> None:
        """
        Set the meta model.

        Args:
            model: Meta model
        """
        self.meta_model = model
        logger.info(f"Set meta model: {type(model).__name__}")

    def fit(self, X: pd.DataFrame, y: pd.Series,
           X_val: Optional[pd.DataFrame] = None,
           y_val: Optional[pd.Series] = None) -> 'SequentialThinkingModel':
        """
        Fit the Sequential Thinking Model.

        Args:
            X: Training features
            y: Training target
            X_val: Validation features (optional)
            y_val: Validation target (optional)

        Returns:
            Self
        """
        logger.info("Fitting Sequential Thinking Model...")
        X_original_train = X.copy()
        X_original_val = X_val.copy() if X_val is not None else None

        if not self.base_models:
            raise ValueError("No base models added. Use add_base_model() to add models.")

        if not self.meta_model:
            logger.info("No meta model set. Using XGBoost as default meta model.")
            self.meta_model = xgb.XGBRegressor(
                n_estimators=100,
                learning_rate=0.05,
                max_depth=3,
                subsample=0.8,
                colsample_bytree=0.8,
                objective='reg:squarederror',
                random_state=42
            )

        # Train base models
        base_predictions_train = np.zeros((X_original_train.shape[0], len(self.base_models)))

        for i, model in enumerate(self.base_models):
            logger.info(f"Training base model {i+1}/{len(self.base_models)}: {self.base_model_names[i]}")
            model.fit(X_original_train, y)
            base_predictions_train[:, i] = model.predict(X_original_train)

            # Log base model performance
            metrics = calculate_metrics(y, base_predictions_train[:, i])
            logger.info(f"Base model {self.base_model_names[i]} metrics on training data: "
                       f"MAE={metrics['mae']:.2f}, RMSE={metrics['rmse']:.2f}, "
                       f"R²={metrics['r2']:.3f}, Hit Rate (±20°C)={metrics['hit_rate_20']:.2f}%")

        # Create meta-features for training the meta-model
        meta_features_train_df, self.meta_feature_names_ = self._create_meta_features(X_original_train, base_predictions_train)
        logger.info(f"Created {meta_features_train_df.shape[1]} meta-features for training. Names (first 5): {self.meta_feature_names_[:5]}")

        # If validation data is provided, use it for meta model training
        if X_original_val is not None and y_val is not None:
            logger.info("Using validation data for meta model training")

            # Generate predictions on validation data
            base_predictions_val = np.zeros((X_original_val.shape[0], len(self.base_models)))
            for i, model in enumerate(self.base_models):
                base_predictions_val[:, i] = model.predict(X_original_val)

            meta_features_val_df, _ = self._create_meta_features(X_original_val, base_predictions_val)

            # Train meta model on validation predictions
            logger.info("Training meta model on validation data's meta-features.")
            self.meta_model.fit(meta_features_val_df, y_val)
        else:
            # Train meta model on training predictions' meta-features
            logger.info("Training meta model on training data's meta-features.")
            self.meta_model.fit(meta_features_train_df, y)

        # Calculate feature importances if available
        self._calculate_feature_importances(meta_features_train_df)

        return self

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """
        Make predictions with the Sequential Thinking Model.

        Args:
            X: Features

        Returns:
            Predictions
        """
        X_original_predict = X.copy()
        # Generate base model predictions
        base_predictions = np.zeros((X_original_predict.shape[0], len(self.base_models)))
        for i, model in enumerate(self.base_models):
            base_predictions[:, i] = model.predict(X_original_predict)

        # Create meta-features for prediction
        meta_features_predict_df, _ = self._create_meta_features(X_original_predict, base_predictions)

        # Make final predictions with meta model
        return self.meta_model.predict(meta_features_predict_df)

    def _calculate_feature_importances(self, meta_features_df: pd.DataFrame) -> None:
        """
        Calculate feature importances for the model based on the meta-learner.

        Args:
            meta_features_df: The DataFrame of meta-features used to train the meta-model.
        """
        n_meta_features = meta_features_df.shape[1]
        self.feature_importances_ = np.zeros(n_meta_features)

        # Get meta model feature importances if available
        if hasattr(self.meta_model, 'feature_importances_'):
            # Ensure importances align with the actual features fed to meta-model
            if len(self.meta_model.feature_importances_) == n_meta_features:
                self.feature_importances_ = self.meta_model.feature_importances_
            else:
                logger.warning(f"Meta-model feature_importances_ length ({len(self.meta_model.feature_importances_)}) "
                               f"does not match number of meta-features ({n_meta_features}). Skipping importance logging.")
                return
        else:
            logger.warning("Meta-model does not have feature_importances_. Cannot log feature importances.")

        # Log top feature importances using self.meta_feature_names_
        if self.feature_importances_ is not None and len(self.meta_feature_names_) == n_meta_features:
            # Create a Series for easy sorting and mapping
            importances_series = pd.Series(self.feature_importances_, index=self.meta_feature_names_)
            top_importances = importances_series.sort_values(ascending=False).head(10)
            
            logger.info("Top 10 meta-feature importances:")
            for name, importance_value in top_importances.items():
                logger.info(f"  {name}: {importance_value:.4f}")
        elif len(self.meta_feature_names_) != n_meta_features :
             logger.warning("Mismatch between meta_feature_names_ length and number of meta-features. Cannot log feature importances correctly.")

def create_base_models(
    X_train: Optional[pd.DataFrame] = None,
    y_train: Optional[pd.Series] = None,
    tune_configs: Optional[Dict[str, int]] = None  # model_name: n_iter
) -> List[Tuple[str, Any]]:
    """
    Create base models for the ensemble.

    Args:
        X_train: Training features, required if tuning.
        y_train: Training target, required if tuning.
        tune_configs: Dictionary specifying model types to tune and n_iter for RandomizedSearchCV.
                      e.g., {'xgboost': 20, 'lightgbm': 15}

    Returns:
        List of (name, model_instance) tuples
    """
    logger.info("Creating base models...")
    if tune_configs is None:
        tune_configs = {}

    base_models_tuples = []

    # XGBoost model
    xgb_params = MODEL_PARAMS.get('xgboost', {'random_state': 42}) # Ensure defaults if key missing
    if X_train is not None and y_train is not None and 'xgboost' in tune_configs:
        logger.info(f"Tuning XGBoost with n_iter={tune_configs['xgboost']}")
        xgb_params = optimize_hyperparameters(X_train, y_train, model_type='xgboost', n_iter=tune_configs['xgboost'])
    # 确保包含missing参数以处理缺失值和极端值    
    if 'missing' not in xgb_params:
        xgb_params['missing'] = np.nan
    xgb_model = xgb.XGBRegressor(**xgb_params)
    base_models_tuples.append(('xgboost', xgb_model))

    # LightGBM model
    lgb_params = MODEL_PARAMS.get('lightgbm', {'random_state': 42})
    if X_train is not None and y_train is not None and 'lightgbm' in tune_configs:
        logger.info(f"Tuning LightGBM with n_iter={tune_configs['lightgbm']}")
        lgb_params = optimize_hyperparameters(X_train, y_train, model_type='lightgbm', n_iter=tune_configs['lightgbm'])
    lgb_model = lgb.LGBMRegressor(**lgb_params)
    base_models_tuples.append(('lightgbm', lgb_model))

    # Random Forest model
    rf_params = MODEL_PARAMS.get('random_forest', {'random_state': 42})
    if X_train is not None and y_train is not None and 'random_forest' in tune_configs:
        logger.info(f"Tuning Random Forest with n_iter={tune_configs['random_forest']}")
        rf_params = optimize_hyperparameters(X_train, y_train, model_type='random_forest', n_iter=tune_configs['random_forest'])
    rf_model = RandomForestRegressor(**rf_params)
    base_models_tuples.append(('random_forest', rf_model))

    # CatBoost model
    cb_params = MODEL_PARAMS.get('catboost', {'random_seed': 42, 'verbose':0}) # 从config获取默认参数
    if X_train is not None and y_train is not None and 'catboost' in tune_configs:
        logger.info(f"Tuning CatBoost with n_trials={tune_configs['catboost']}") # Optuna 使用 n_trials
        cb_params = optimize_hyperparameters(X_train, y_train, model_type='catboost', n_iter=tune_configs['catboost'])
    # CatBoost 的 random_state 参数是 random_seed
    if 'random_state' in cb_params and 'random_seed' not in cb_params:
        cb_params['random_seed'] = cb_params.pop('random_state')
    if 'verbose' not in cb_params: # 确保verbose存在以避免CatBoost过多日志
        cb_params['verbose'] = 0 
    cb_model = CatBoostRegressor(**cb_params)
    base_models_tuples.append(('catboost', cb_model))

    # Neural Network model
    nn_params = MODEL_PARAMS.get('neural_network', {'random_state': 42, 'max_iter': 500}) # Example default
    # Tuning for MLPRegressor could be added to optimize_hyperparameters if needed
    nn_model = MLPRegressor(**nn_params)
    base_models_tuples.append(('neural_network', nn_model))

    # Linear models
    ridge_params = MODEL_PARAMS.get('ridge', {'alpha': 1.0, 'random_state': 42})
    ridge_model = Ridge(**ridge_params)
    base_models_tuples.append(('ridge', ridge_model))

    lasso_params = MODEL_PARAMS.get('lasso', {'alpha': 0.1, 'random_state': 42})
    lasso_model = Lasso(**lasso_params)
    base_models_tuples.append(('lasso', lasso_model))

    # SVR model
    svr_params = MODEL_PARAMS.get('svr', {'kernel': 'rbf', 'C': 100, 'gamma': 0.1, 'epsilon': 0.1})
    svr_model = SVR(**svr_params)
    base_models_tuples.append(('svr', svr_model))

    tuned_model_names = [name for name in tune_configs if X_train is not None and y_train is not None]
    logger.info(f"Created {len(base_models_tuples)} base models. Tuned models: {tuned_model_names if tuned_model_names else 'None'}")

    return base_models_tuples

def train_sequential_thinking_model(X_train: pd.DataFrame, y_train: pd.Series,
                                  X_val: Optional[pd.DataFrame] = None,
                                  y_val: Optional[pd.Series] = None,
                                  tune_base_model_n_iters: Optional[Dict[str, int]] = None,
                                  meta_learner_type: str = 'xgboost',
                                  include_original_features_in_meta: bool = True,
                                  include_prediction_interactions: bool = False,
                                  tune_meta_model_n_iter: Optional[int] = None,
                                  tune_meta_model_params: Optional[Dict[str, Any]] = None
                                  ) -> SequentialThinkingModel:
    """
    Train a Sequential Thinking Model.

    This function orchestrates the training of a two-stage sequential model.
    The first stage involves training multiple base models.
    The second stage trains a meta-model on the predictions of the base models
    and the original features.

    Feature Engineering Considerations:
    
    1. 炉渣特征工程 (Slag Feature Engineering):
        * 基础成分计算: 基于铁水/钢水成分变化和辅料加入计算各氧化物(SiO2, MnO, P2O5等)质量
        * 炉渣物理特性: 粘度、流动性、熔点等特性，与热传递效率直接相关
        * 炉渣相平衡特性: 利用FactSage计算液相百分比、凝固温度范围，预测熔渣-钢水界面状态
        * 炉渣-钢水反应动力学: 炉渣磷容量系数和脱磷效率指数，影响热量释放
        * 复杂结构网络模型: 网络形成体(SiO2,P2O5)与网络修饰体(CaO,MgO)的摩尔比，影响炉渣粘度
        * 炉渣熔化特性: 模拟炉渣熔化过程中的吸热和放热，与终点温度预测关键相关
    
    2. 工艺参数特征工程 (Process Parameter Engineering):
        * 动态供氧特征: 供氧强度时间曲线、峰值供氧率、累积供氧量等
        * 枪位特征增强: 基于枪位研究文献，计算喷射动量、表面冲击深度和搅拌效率
        * 时序工艺相关性: 各工艺参数变化率、滞后效应和累积影响
        * 底吹强度特征: 气体流量与搅拌强度指数、气泡流动区域估算
        * 氧气利用率估算: 基于理论氧耗计算实际氧气利用效率
        * 能量输入特征: 不同阶段的累积能量输入，与最终温升的相关性
    
    3. 交互特征构建 (Interaction Feature Construction):
        * 炉渣-工艺交互: 炉渣碱度与供氧强度的交互对终点温度的影响
        * 时间-工艺交互: 不同阶段工艺参数设置的差异对温度的影响
        * 铁水-炉渣交互: 铁水初始成分与炉渣形成过程的交互关系
        * 非线性特征变换: 对关键参数进行多项式变换，捕捉非线性关系
        * 热量平衡交互: 各放热反应与散热因素的交互对终点热平衡的影响

    Hyperparameter Tuning:
    -   Base models can be tuned by providing `tune_base_model_n_iters`.
    -   The meta-model can be tuned by providing `tune_meta_model_n_iter` and `tune_meta_model_params`.
        For the meta model, you can provide custom parameter distributions for tuning.

    Args:
        X_train: Training features
        y_train: Training target
        X_val: Validation features (optional, used for meta-model tuning and early stopping if applicable)
        y_val: Validation target (optional)
        tune_base_model_n_iters: Dict specifying base models to tune and n_iter for RandomizedSearchCV.
                                 e.g., {'xgboost': 20, 'lightgbm': 15}. If None, uses default params.
        meta_learner_type: Type of meta-learner to use ('xgboost', 'lightgbm', 'catboost'). Default is 'xgboost'.
        include_original_features_in_meta: Whether to include original features in meta-learner input.
        include_prediction_interactions: Whether to include pairwise products of base model predictions.
        tune_meta_model_n_iter: Number of iterations for Optuna for the meta-model.
                                If None, meta-model uses default parameters.
        tune_meta_model_params: Custom parameter distributions for meta-model tuning (passed to Optuna).
                                If provided, overrides the default parameter grid.

    Returns:
        Trained Sequential Thinking Model
    """
    logger.info("Training Sequential Thinking Model...")
    logger.info(f"Meta-learner type: {meta_learner_type}")
    logger.info(f"Include original features in meta: {include_original_features_in_meta}")
    logger.info(f"Include prediction interactions in meta: {include_prediction_interactions}")

    # Create base models (potentially tuned)
    logger.info("Step 1: Creating/Tuning Base Models")
    base_models_with_names = create_base_models(X_train, y_train, tune_base_model_n_iters)

    # Create Sequential Thinking Model instance with new meta-feature controls
    model = SequentialThinkingModel(
        include_original_features_in_meta=include_original_features_in_meta,
        include_prediction_interactions=include_prediction_interactions
    )

    # Add base models to the SequentialThinkingModel instance
    for name, base_model_instance in base_models_with_names:
        model.add_base_model(base_model_instance, name)

    # Set and potentially tune the meta model
    logger.info(f"Step 2: Setting/Tuning Meta Model ({meta_learner_type})")
    
    # Get default parameters for the chosen meta-learner type
    default_meta_params = MODEL_PARAMS.get(meta_learner_type, {}).copy()
    if not default_meta_params:
        logger.warning(f"No default parameters found for meta-learner type '{meta_learner_type}' in MODEL_PARAMS. Using empty dict.")
    # Ensure common parameters are set if not present in MODEL_PARAMS
    if meta_learner_type in ['xgboost', 'lightgbm', 'catboost'] and 'random_state' not in default_meta_params and 'random_seed' not in default_meta_params:
        default_meta_params['random_state' if meta_learner_type != 'catboost' else 'random_seed'] = 42
    if meta_learner_type == 'catboost' and 'verbose' not in default_meta_params:
        default_meta_params['verbose'] = 0
    if meta_learner_type == 'xgboost' and 'objective' not in default_meta_params:
        default_meta_params['objective'] = 'reg:squarederror'
    if meta_learner_type == 'lightgbm' and 'objective' not in default_meta_params:
        default_meta_params['objective'] = 'regression' # or regression_l1 for MAE

    meta_model_active_params = default_meta_params.copy()

    if tune_meta_model_n_iter is not None and tune_meta_model_n_iter > 0:
        logger.info(f"Tuning Meta Model ({meta_learner_type}) with n_trials={tune_meta_model_n_iter}")
        
        # Fit base models first to get predictions for meta-model tuning
        # These base models are fresh instances from create_base_models, possibly tuned there.
        temp_base_predictions_train = np.zeros((X_train.shape[0], len(model.base_models)))
        logger.info("Fitting temporary base models to generate predictions for meta-model tuning...")
        for i, bm_instance in enumerate(model.base_models): # Use bm_instance which is the actual model object
            logger.info(f"  Fitting temporary base model {model.base_model_names[i]}")
            bm_instance.fit(X_train, y_train) 
            temp_base_predictions_train[:, i] = bm_instance.predict(X_train)

        # Use the model's internal _create_meta_features to generate features for tuning
        # This ensures that the meta-features used for tuning are consistent with those used in the final fit/predict
        meta_features_for_tuning_df, meta_feature_names_for_tuning = model._create_meta_features(X_train, temp_base_predictions_train)
        y_for_meta_tuning = y_train
        logger.info(f"Generated {meta_features_for_tuning_df.shape[1]} meta-features for tuning the meta-learner.")

        if X_val is not None and y_val is not None:
            logger.info("Using validation data for meta model hyperparameter tuning.")
            temp_base_predictions_val = np.zeros((X_val.shape[0], len(model.base_models)))
            for i, bm_instance in enumerate(model.base_models):
                temp_base_predictions_val[:, i] = bm_instance.predict(X_val)
            meta_features_for_tuning_df, meta_feature_names_for_tuning = model._create_meta_features(X_val, temp_base_predictions_val)
            y_for_meta_tuning = y_val
            logger.info(f"Switched to validation data for meta-tuning. Generated {meta_features_for_tuning_df.shape[1]} meta-features.")
        else:
            logger.info("Using training data for meta model hyperparameter tuning (validation set not provided).")

        # Use custom param grid if provided, else Optuna will use its internal search space definitions
        param_grid_for_optuna = tune_meta_model_params # This can be None
        if param_grid_for_optuna:
            logger.info(f"Using custom parameter grid for meta model tuning: {param_grid_for_optuna}")

        best_meta_params = optimize_hyperparameters(
            meta_features_for_tuning_df, 
            y_for_meta_tuning, 
            model_type=meta_learner_type, 
            n_iter=tune_meta_model_n_iter,
            param_grid=param_grid_for_optuna # Pass this to Optuna, though it primarily uses its internal suggestions
        )
        meta_model_active_params.update(best_meta_params) 
        logger.info(f"Optimized Meta Model ({meta_learner_type}) parameters: {meta_model_active_params}")

    # Instantiate the meta-learner with determined parameters
    if meta_learner_type == 'xgboost':
        meta_model_instance = xgb.XGBRegressor(**meta_model_active_params)
    elif meta_learner_type == 'lightgbm':
        meta_model_instance = lgb.LGBMRegressor(**meta_model_active_params)
    elif meta_learner_type == 'catboost':
        # Ensure correct param name for CatBoost random state
        if 'random_state' in meta_model_active_params and 'random_seed' not in meta_model_active_params:
            meta_model_active_params['random_seed'] = meta_model_active_params.pop('random_state')
        if 'verbose' not in meta_model_active_params: # Ensure CatBoost verbose is set
             meta_model_active_params['verbose'] = 0
        meta_model_instance = CatBoostRegressor(**meta_model_active_params)
    else:
        logger.error(f"Unsupported meta-learner type: {meta_learner_type}. Defaulting to XGBoost.")
        meta_model_instance = xgb.XGBRegressor(**MODEL_PARAMS.get('xgboost', {'random_state': 42}))
        
    model.set_meta_model(meta_model_instance)

    # Train the full SequentialThinkingModel 
    # Base models (already added to `model`) will be fit again inside model.fit(), 
    # then the meta_model (now set) will be fit on meta-features generated by _create_meta_features.
    logger.info("Step 3: Fitting the full Sequential Thinking Model")
    model.fit(X_train, y_train, X_val, y_val)

    logger.info("Sequential Thinking Model training complete.")
    return model

def optimize_hyperparameters(X_train: pd.DataFrame, y_train: pd.Series,
                           model_type: str = 'xgboost',
                           n_iter: int = 20, # For Optuna, this will be n_trials
                           param_grid: Optional[Dict[str, Any]] = None) -> Dict[str, Any]: # param_grid is not directly used by Optuna in this setup but kept for signature consistency
    """
    Optimize hyperparameters for a given model type using Optuna.
    """
    logger.info(f"Optimizing hyperparameters for {model_type} using Optuna with n_trials={n_iter}...")

    def objective(trial: optuna.Trial) -> float:
        if model_type == 'xgboost':
            params = {
                'objective': 'reg:squarederror',
                'random_state': 42,
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000, step=50),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'gamma': trial.suggest_float('gamma', 0, 5),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 5),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 5),
                'missing': np.nan
            }
            model = xgb.XGBRegressor(**params)
        elif model_type == 'lightgbm':
            params = {
                'objective': 'regression_l1', # MAE as objective
                'metric': 'mae', # MAE for evaluation
                'random_state': 42,
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000, step=50),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'num_leaves': trial.suggest_int('num_leaves', 20, 150),
                'max_depth': trial.suggest_int('max_depth', -1, 12), # -1 for no limit in LightGBM
                'subsample': trial.suggest_float('subsample', 0.6, 1.0), # Alias for bagging_fraction
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0), # Alias for feature_fraction
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 5), # Alias for lambda_l1
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 5), # Alias for lambda_l2
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 50) # Added min_child_samples
            }
            model = lgb.LGBMRegressor(**params)
        elif model_type == 'random_forest':
            params = {
                'random_state': 42,
                'n_estimators': trial.suggest_int('n_estimators', 50, 800, step=50),
                'max_depth': trial.suggest_int('max_depth', 5, 50, step=5, log=False),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 20),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.5, 0.7, None]),
            }
            model = RandomForestRegressor(**params)
        elif model_type == 'gradient_boosting': # GradientBoostingRegressor
            params = {
                'random_state': 42,
                'n_estimators': trial.suggest_int('n_estimators', 50, 800, step=50),
                'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.2, log=True), # Wider range
                'max_depth': trial.suggest_int('max_depth', 2, 10), # Adjusted range
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 20),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.5, 0.7, None]),
                'loss': trial.suggest_categorical('loss', ['squared_error', 'absolute_error', 'huber', 'quantile'])
            }
            model = GradientBoostingRegressor(**params)
        elif model_type == 'extra_trees': # ExtraTreesRegressor
            params = {
                'random_state': 42,
                'n_estimators': trial.suggest_int('n_estimators', 50, 800, step=50),
                'max_depth': trial.suggest_int('max_depth', 5, 50, step=5, log=False), 
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 20),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.5, 0.7, None]),
            }
            model = ExtraTreesRegressor(**params)
        elif model_type == 'catboost': # 新增CatBoost分支
            params = {
                'iterations': trial.suggest_int('iterations', 100, 1500, step=100),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'depth': trial.suggest_int('depth', 4, 10),
                'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1e-8, 10.0, log=True),
                'border_count': trial.suggest_int('border_count', 32, 255),
                'random_seed': 42,
                'verbose': 0, # 保持 Optuna 运行时静默
                'loss_function': 'MAE', # 或者RMSE,取决于优化目标
                'eval_metric': 'MAE'
            }
            model = CatBoostRegressor(**params)
        else:
            logger.error(f"Model type {model_type} not supported for Optuna hyperparameter optimization.")
            raise ValueError(f"Unsupported model_type: {model_type}")

        # Use cross-validation to evaluate the model
        # We want to minimize MAE, so we return MAE directly (Optuna default is minimization)
        kf = KFold(n_splits=CV_FOLDS, shuffle=True, random_state=trial.number) # Use trial.number for different shuffles
        
        # Important: Optuna works by minimizing the returned value.
        # So, if scoring is 'neg_mean_absolute_error', it means a less negative (closer to 0) score is better.
        # If we directly calculate MAE, a smaller MAE is better.
        # For clarity, let's use 'neg_mean_absolute_error' and Optuna will maximize it.
        # Or, calculate MAE and tell Optuna to minimize. Let's use 'neg_mean_absolute_error' for consistency with sklearn.
        
        # scores = cross_val_score(model, X_train, y_train, cv=kf, scoring='neg_mean_absolute_error', n_jobs=-1)
        # score = np.mean(scores) # This will be negative, Optuna should maximize it.
        
        # Alternative: Calculate MAE and have Optuna minimize it
        mae_scores = []
        for train_idx, val_idx in kf.split(X_train):
            X_cv_train, X_cv_val = X_train.iloc[train_idx], X_train.iloc[val_idx]
            y_cv_train, y_cv_val = y_train.iloc[train_idx], y_train.iloc[val_idx]
            try:
                model.fit(X_cv_train, y_cv_train)
                preds = model.predict(X_cv_val)
                mae_scores.append(mean_absolute_error(y_cv_val, preds))
            except Exception as e_cv:
                logger.warning(f"Error during CV fold for {model_type} with params {params}: {e_cv}")
                return float('inf') # Return a very bad score if a fold fails
        
        if not mae_scores: # Should not happen if CV runs
             logger.error(f"No MAE scores collected for {model_type}, trial {trial.number}. Returning inf.")
             return float('inf')

        score = np.mean(mae_scores)
        return score # Optuna will minimize this MAE

    # Create study object. Direction 'minimize' as we are returning MAE.
    study = optuna.create_study(direction='minimize', sampler=optuna.samplers.TPESampler(seed=42)) # TPESampler is a good default
    
    # Run optimization
    try:
        study.optimize(objective, n_trials=n_iter, n_jobs=-1, # n_jobs=-1 for parallel trials if model training is thread-safe
                       # catch=(ValueError,) # Catch specific errors during trials if needed
                      )
    except optuna.exceptions.TrialPruned:
        logger.warning("An Optuna trial was pruned. This is normal if a pruner is configured or if a trial fails early.")
    except Exception as e_opt:
        logger.error(f"Exception during Optuna study.optimize for {model_type}: {e_opt}", exc_info=True)
        # Fallback to default parameters if optimization fails catastrophically
        logger.warning(f"Optuna optimization failed for {model_type}. Falling back to default parameters from MODEL_PARAMS.")
        return MODEL_PARAMS.get(model_type, {'random_state': 42})


    best_params = study.best_params
    logger.info(f"Optuna best MAE for {model_type} (CV mean): {study.best_value:.4f}")
    logger.info(f"Optuna best params for {model_type}: {best_params}")

    # Add back any fixed parameters that are part of the model's signature but not tuned
    # or ensure they are part of the search space with a fixed choice if necessary.
    if model_type == 'xgboost':
        best_params['objective'] = 'reg:squarederror'
        best_params['random_state'] = 42
        best_params['missing'] = np.nan # XGBoost specific
    elif model_type == 'lightgbm':
        best_params['objective'] = 'regression_l1' # Ensure objective is set
        best_params['metric'] = 'mae'
        best_params['random_state'] = 42
    elif model_type == 'catboost': # 新增CatBoost固定参数
        best_params['random_seed'] = 42
        best_params['verbose'] = 0
        best_params['loss_function'] = 'MAE' # 确保与优化目标一致
        best_params['eval_metric'] = 'MAE'
    elif model_type in ['random_forest', 'gradient_boosting', 'extra_trees']:
        best_params['random_state'] = 42
        if model_type == 'gradient_boosting' and 'loss' not in best_params: # Ensure loss is set if not tuned
            best_params['loss'] = 'squared_error' # Default for GBR

    return best_params

    def hit_rate_20(y_true, y_pred):
        return np.mean(np.abs(y_true - y_pred) <= 20) * 100
