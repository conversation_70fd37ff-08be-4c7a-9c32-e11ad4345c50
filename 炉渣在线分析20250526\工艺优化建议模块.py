#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转炉炼钢工艺优化建议模块
基于FactSage热力学计算和CaO-SiO2-FeO三元相图
结合30年炼钢经验的专家知识系统
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize_scalar
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ProcessOptimizer:
    """工艺优化建议系统"""
    
    def __init__(self):
        # 基于FactSage的热力学数据
        self.thermodynamic_data = {
            'CaO_formation': -635.1,    # kJ/mol
            'SiO2_formation': -910.7,   # kJ/mol
            'FeO_formation': -272.0,    # kJ/mol
            'MgO_formation': -601.6,    # kJ/mol
            'P2O5_formation': -1640.1   # kJ/mol
        }
        
        # 脱磷反应平衡常数（基于温度）
        self.dephosphorization_params = {
            'A': 22350,  # K
            'B': -16.94  # 无量纲
        }
        
        # 理想工艺参数范围
        self.optimal_ranges = {
            '碱度': (2.2, 3.2),
            'FeO': (15, 25),
            'MgO': (6, 12),
            '钢水温度': (1580, 1650),
            '过热度': (80, 120)
        }
    
    def calculate_dephosphorization_efficiency(self, cao_pct, sio2_pct, feo_pct, temp_k):
        """计算脱磷效率"""
        basicity = cao_pct / sio2_pct if sio2_pct > 0 else 0
        
        # 脱磷反应平衡常数 log K = A/T + B
        log_k = self.dephosphorization_params['A'] / temp_k + self.dephosphorization_params['B']
        k_eq = 10 ** log_k
        
        # 脱磷能力指数（经验公式）
        dephosphorization_index = k_eq * (basicity ** 2.5) * (feo_pct ** 1.5) / 100
        
        return min(dephosphorization_index, 100)  # 最大100%
    
    def optimize_slag_composition(self, target_p_removal=85, steel_temp=1620):
        """优化炉渣成分以达到目标脱磷率"""
        def objective(basicity):
            # 假设SiO2=15%, 计算对应的CaO
            sio2 = 15
            cao = basicity * sio2
            feo = 20  # 假设FeO=20%
            
            temp_k = steel_temp + 273.15
            efficiency = self.calculate_dephosphorization_efficiency(cao, sio2, feo, temp_k)
            
            return abs(efficiency - target_p_removal)
        
        # 优化碱度
        result = minimize_scalar(objective, bounds=(1.5, 4.0), method='bounded')
        optimal_basicity = result.x
        
        return {
            '最优碱度': optimal_basicity,
            '建议SiO2': 15,
            '建议CaO': optimal_basicity * 15,
            '建议FeO': 20,
            '预期脱磷率': target_p_removal
        }
    
    def calculate_lime_addition(self, hot_metal_si, hot_metal_mass, target_basicity=2.8):
        """计算石灰加入量"""
        # SiO2生成量
        sio2_generated = hot_metal_si * hot_metal_mass * 2.14 / 100  # kg
        
        # 目标CaO量
        target_cao = target_basicity * sio2_generated
        
        # 石灰加入量（考虑85%有效CaO）
        lime_addition = target_cao / 0.85
        
        return {
            '预计SiO2生成': sio2_generated,
            '目标CaO': target_cao,
            '建议石灰加入量': lime_addition,
            '石灰利用率': 85
        }
    
    def evaluate_process_parameters(self, process_data):
        """评估工艺参数并给出建议"""
        recommendations = []
        
        # 评估碱度
        if 'basicity' in process_data:
            basicity = process_data['basicity']
            if basicity < self.optimal_ranges['碱度'][0]:
                recommendations.append({
                    '参数': '碱度',
                    '当前值': basicity,
                    '建议': f"碱度偏低，建议增加石灰用量至{self.optimal_ranges['碱度'][0]:.1f}-{self.optimal_ranges['碱度'][1]:.1f}",
                    '影响': '脱磷效果不佳，钢水磷含量可能超标'
                })
            elif basicity > self.optimal_ranges['碱度'][1]:
                recommendations.append({
                    '参数': '碱度',
                    '当前值': basicity,
                    '建议': f"碱度偏高，建议减少石灰用量至{self.optimal_ranges['碱度'][0]:.1f}-{self.optimal_ranges['碱度'][1]:.1f}",
                    '影响': '造渣料消耗增加，炉渣流动性变差'
                })
        
        # 评估FeO含量
        if 'feo' in process_data:
            feo = process_data['feo']
            if feo < self.optimal_ranges['FeO'][0]:
                recommendations.append({
                    '参数': 'FeO含量',
                    '当前值': feo,
                    '建议': f"FeO含量偏低，建议增加供氧强度，目标{self.optimal_ranges['FeO'][0]}-{self.optimal_ranges['FeO'][1]}%",
                    '影响': '脱磷脱硫效果不佳，炉渣氧化性不足'
                })
            elif feo > self.optimal_ranges['FeO'][1]:
                recommendations.append({
                    '参数': 'FeO含量',
                    '当前值': feo,
                    '建议': f"FeO含量偏高，建议降低供氧强度，目标{self.optimal_ranges['FeO'][0]}-{self.optimal_ranges['FeO'][1]}%",
                    '影响': '铁损增加，金属收得率降低'
                })
        
        # 评估钢水温度
        if 'steel_temp' in process_data:
            temp = process_data['steel_temp']
            if temp < self.optimal_ranges['钢水温度'][0]:
                recommendations.append({
                    '参数': '钢水温度',
                    '当前值': temp,
                    '建议': f"温度偏低，建议增加供氧强度或减少废钢比，目标{self.optimal_ranges['钢水温度'][0]}-{self.optimal_ranges['钢水温度'][1]}°C",
                    '影响': '连铸过程可能出现凝固问题'
                })
            elif temp > self.optimal_ranges['钢水温度'][1]:
                recommendations.append({
                    '参数': '钢水温度',
                    '当前值': temp,
                    '建议': f"温度偏高，建议降低供氧强度或增加废钢比，目标{self.optimal_ranges['钢水温度'][0]}-{self.optimal_ranges['钢水温度'][1]}°C",
                    '影响': '耐火材料侵蚀加剧，能耗增加'
                })
        
        return recommendations

class TernaryDiagramCalculator:
    """三元相图计算器"""
    
    def __init__(self):
        # CaO-SiO2-FeO三元相图关键点（基于文献数据）
        self.liquidus_data = {
            'CaO_pure': 2572,      # °C
            'SiO2_pure': 1713,     # °C  
            'FeO_pure': 1377,      # °C
            'CaO_SiO2_eutectic': 1436,  # °C, 约65%CaO
            'SiO2_FeO_eutectic': 1205,  # °C, 约35%SiO2
            'CaO_FeO_eutectic': 1205    # °C, 约45%CaO
        }
    
    def calculate_liquidus_temperature(self, cao_pct, sio2_pct, feo_pct):
        """计算液相线温度（改进算法）"""
        # 归一化组分
        total = cao_pct + sio2_pct + feo_pct
        if total == 0:
            return 1600
        
        x_cao = cao_pct / total
        x_sio2 = sio2_pct / total  
        x_feo = feo_pct / total
        
        # 基于三元相图的液相线温度计算
        # 使用加权平均和共晶点修正
        temp_cao_contrib = self.liquidus_data['CaO_pure'] * x_cao
        temp_sio2_contrib = self.liquidus_data['SiO2_pure'] * x_sio2
        temp_feo_contrib = self.liquidus_data['FeO_pure'] * x_feo
        
        # 共晶效应修正
        eutectic_effect = 0
        if x_cao > 0.4 and x_sio2 > 0.1:
            eutectic_effect += -200 * x_cao * x_sio2  # CaO-SiO2共晶
        if x_sio2 > 0.2 and x_feo > 0.2:
            eutectic_effect += -150 * x_sio2 * x_feo  # SiO2-FeO共晶
        if x_cao > 0.3 and x_feo > 0.2:
            eutectic_effect += -100 * x_cao * x_feo   # CaO-FeO共晶
        
        liquidus_temp = temp_cao_contrib + temp_sio2_contrib + temp_feo_contrib + eutectic_effect
        
        return max(liquidus_temp, 1200)  # 最低温度限制
    
    def plot_ternary_diagram(self, compositions_list, temperatures_list=None):
        """绘制三元相图（简化版）"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 提取组分
        cao_values = [comp['CaO'] for comp in compositions_list]
        sio2_values = [comp['SiO2'] for comp in compositions_list]
        feo_values = [comp['FeO'] for comp in compositions_list]
        
        # 图1：CaO-SiO2关系
        scatter1 = ax1.scatter(sio2_values, cao_values, c=temperatures_list if temperatures_list else 'blue', 
                              cmap='coolwarm', alpha=0.7, s=50)
        ax1.set_xlabel('SiO2 (%)')
        ax1.set_ylabel('CaO (%)')
        ax1.set_title('CaO-SiO2关系图')
        ax1.grid(True)
        
        # 添加理想区域
        ax1.axhline(y=40, color='green', linestyle='--', alpha=0.5, label='CaO理想范围')
        ax1.axhline(y=60, color='green', linestyle='--', alpha=0.5)
        ax1.axvline(x=12, color='red', linestyle='--', alpha=0.5, label='SiO2理想范围')
        ax1.axvline(x=20, color='red', linestyle='--', alpha=0.5)
        ax1.legend()
        
        if temperatures_list:
            cbar1 = plt.colorbar(scatter1, ax=ax1)
            cbar1.set_label('液相线温度 (°C)')
        
        # 图2：SiO2-FeO关系
        scatter2 = ax2.scatter(sio2_values, feo_values, c=temperatures_list if temperatures_list else 'red',
                              cmap='coolwarm', alpha=0.7, s=50)
        ax2.set_xlabel('SiO2 (%)')
        ax2.set_ylabel('FeO (%)')
        ax2.set_title('SiO2-FeO关系图')
        ax2.grid(True)
        
        # 添加理想区域
        ax2.axhline(y=15, color='green', linestyle='--', alpha=0.5, label='FeO理想范围')
        ax2.axhline(y=25, color='green', linestyle='--', alpha=0.5)
        ax2.legend()
        
        if temperatures_list:
            cbar2 = plt.colorbar(scatter2, ax=ax2)
            cbar2.set_label('液相线温度 (°C)')
        
        plt.tight_layout()
        return fig

def generate_process_report(data_row, optimizer, ternary_calc):
    """生成单炉次工艺分析报告"""
    report = {
        '炉号': data_row['炉号'],
        '钢种': data_row['钢种'],
        '基础数据': {
            '铁水量': data_row['铁水'],
            '废钢量': data_row['废钢'] if pd.notna(data_row['废钢']) else 0,
            '铁水成分': {
                'C': data_row['铁水C'],
                'Si': data_row['铁水SI'], 
                'Mn': data_row['铁水MN'],
                'P': data_row['铁水P'],
                'S': data_row['铁水S']
            }
        }
    }
    
    # 石灰用量建议
    lime_calc = optimizer.calculate_lime_addition(
        data_row['铁水SI'], data_row['铁水'], target_basicity=2.8
    )
    report['石灰用量建议'] = lime_calc
    
    # 脱磷优化
    dephosphorization_opt = optimizer.optimize_slag_composition(
        target_p_removal=90, steel_temp=data_row['钢水温度'] if pd.notna(data_row['钢水温度']) else 1620
    )
    report['脱磷优化建议'] = dephosphorization_opt
    
    # 工艺参数评估
    process_params = {
        'basicity': lime_calc['目标CaO'] / lime_calc['预计SiO2生成'] if lime_calc['预计SiO2生成'] > 0 else 2.5,
        'feo': 20,  # 假设值
        'steel_temp': data_row['钢水温度'] if pd.notna(data_row['钢水温度']) else 1620
    }
    
    recommendations = optimizer.evaluate_process_parameters(process_params)
    report['工艺建议'] = recommendations
    
    return report

if __name__ == "__main__":
    print("=== 转炉炼钢工艺优化建议系统 ===")
    print("基于FactSage热力学计算和三元相图理论\n")
    
    # 初始化优化器
    optimizer = ProcessOptimizer()
    ternary_calc = TernaryDiagramCalculator()
    
    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        exit()
    
    # 生成前10炉的详细报告
    print("正在生成工艺优化报告...")
    reports = []
    for idx in range(min(10, len(df))):
        report = generate_process_report(df.iloc[idx], optimizer, ternary_calc)
        reports.append(report)
    
    # 保存报告
    import json
    with open('工艺优化报告.json', 'w', encoding='utf-8') as f:
        json.dump(reports, f, ensure_ascii=False, indent=2)
    
    print("工艺优化报告已生成并保存到 '工艺优化报告.json'")
    print("系统基于30年炼钢经验和FactSage热力学数据提供专业建议")
