"""
阶段3：实施多目标优化 + 增加炉渣成分特征
目标：通过炉渣成分特征和多目标优化实现重大突破
预期提升：从75.8%提升到85-90%
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime

# 核心机器学习库
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb

# CatBoost
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
    print("✅ CatBoost successfully loaded")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("❌ CatBoost not available")

# 超参数优化
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
    print("✅ Optuna successfully loaded")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("❌ Optuna not available")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"step3_multi_obj_slag_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Step3MultiObjectiveSlagOptimizer:
    """阶段3：多目标优化 + 炉渣成分特征优化器"""

    def __init__(self):
        self.models = {}
        self.best_params = {}
        self.scalers = {}
        self.label_encoders = {}

        # 目标范围参数
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

        # 多目标优化权重（调整版）
        self.multi_objective_weights = {
            'accuracy': 0.5,      # 目标精度权重
            'stability': 0.15,    # 预测稳定性权重
            'generalization': 0.15, # 泛化能力权重
            'physics': 0.1,       # 物理一致性权重
            'slag_consistency': 0.1  # 炉渣一致性权重（新增）
        }

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def advanced_multi_objective_loss(self, y_true: np.ndarray, y_pred: np.ndarray,
                                    slag_features: np.ndarray = None) -> float:
        """高级多目标优化损失函数"""

        # 1. 精度损失（目标范围内的命中率）
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])
        if target_mask.sum() > 0:
            target_y_true = y_true[target_mask]
            target_y_pred = y_pred[target_mask]
            hit_rate = np.mean(np.abs(target_y_true - target_y_pred) <= self.target_tolerance)
            accuracy_loss = (1 - hit_rate) * 1000
        else:
            accuracy_loss = 1000

        # 2. 稳定性损失（预测方差）
        stability_loss = np.var(y_pred) / 100

        # 3. 泛化能力损失（基于预测分布）
        pred_std = np.std(y_pred)
        ideal_std = 25.0  # 理想标准差
        generalization_loss = abs(pred_std - ideal_std) * 2

        # 4. 物理一致性损失（预测值在合理范围内）
        physics_loss = np.mean((y_pred < 1500) | (y_pred > 1750)) * 500

        # 5. 炉渣一致性损失（新增）
        slag_consistency_loss = 0
        if slag_features is not None:
            # 基于炉渣特征的一致性检查
            slag_basicity = slag_features[:, 0] if slag_features.shape[1] > 0 else np.ones(len(y_pred)) * 2.8
            slag_feo = slag_features[:, 1] if slag_features.shape[1] > 1 else np.ones(len(y_pred)) * 15.0

            # 炉渣碱度与温度的一致性
            expected_temp_from_basicity = 1620 + (slag_basicity - 2.8) * 10
            basicity_consistency = np.mean(np.abs(y_pred - expected_temp_from_basicity)) / 10

            # FeO含量与温度的一致性
            expected_temp_from_feo = 1630 - (slag_feo - 15) * 2
            feo_consistency = np.mean(np.abs(y_pred - expected_temp_from_feo)) / 10

            slag_consistency_loss = (basicity_consistency + feo_consistency) / 2

        # 组合损失
        total_loss = (self.multi_objective_weights['accuracy'] * accuracy_loss +
                     self.multi_objective_weights['stability'] * stability_loss +
                     self.multi_objective_weights['generalization'] * generalization_loss +
                     self.multi_objective_weights['physics'] * physics_loss +
                     self.multi_objective_weights['slag_consistency'] * slag_consistency_loss)

        return total_loss

    def robust_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """稳健的数据清理"""
        logger.info("开始数据清理")

        df_clean = df.copy()

        # 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
                return float(value)
            except:
                return default

        # 处理数值列
        numeric_columns = ['铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                          '铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s']

        for col in numeric_columns:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].apply(safe_convert)

        # 移除无穷大值
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)

        # 基于物理约束的异常值检测
        constraints = {
            '铁水温度': (1250, 1500),
            '铁水C': (3.0, 5.5),
            '铁水SI': (0.1, 1.5),
            '铁水MN': (0.05, 1.0),
            '铁水P': (0.05, 0.3),
            '铁水': (60, 120),
            '废钢': (0, 50),
            '累氧实际': (3000, 7000),
            '吹氧时间s': (300, 1200)
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].clip(min_val, max_val)

        # 目标变量清理
        if '钢水温度' in df_clean.columns:
            df_clean['钢水温度'] = df_clean['钢水温度'].apply(safe_convert)
            df_clean = df_clean[(df_clean['钢水温度'] >= 1500) & (df_clean['钢水温度'] <= 1750)]

        logger.info(f"数据清理完成，保留{len(df_clean)}条记录")
        return df_clean

    def create_advanced_slag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建高级炉渣成分特征（关键突破点）"""
        logger.info("创建高级炉渣成分特征")

        df_slag = df.copy()

        # 基础炉渣成分估算（改进版）
        for idx, row in df_slag.iterrows():
            try:
                # 基础参数
                lime = self.safe_convert(row.get('石灰', 0))
                dolomite = self.safe_convert(row.get('白云石', 0))
                hot_metal_mass = self.safe_convert(row.get('铁水', 90))
                scrap_mass = self.safe_convert(row.get('废钢', 20))
                si_content = self.safe_convert(row.get('铁水SI', 0.4))
                mn_content = self.safe_convert(row.get('铁水MN', 0.17))
                p_content = self.safe_convert(row.get('铁水P', 0.1))
                c_content = self.safe_convert(row.get('铁水C', 4.2))
                oxygen_consumed = self.safe_convert(row.get('累氧实际', 4800))
                blow_time = self.safe_convert(row.get('吹氧时间s', 600))
                hot_metal_temp = self.safe_convert(row.get('铁水温度', 1350))

                # 1. 炉渣CaO含量估算（精确版）
                cao_from_lime = lime * 0.92  # 石灰中CaO含量约92%
                cao_from_dolomite = dolomite * 0.54  # 白云石中CaO含量约54%
                cao_from_flux = 2.0  # 其他助熔剂贡献
                total_cao = cao_from_lime + cao_from_dolomite + cao_from_flux
                df_slag.loc[idx, 'slag_cao'] = np.clip(total_cao, 35, 65)

                # 2. 炉渣SiO2含量估算（精确版）
                sio2_from_si_oxidation = si_content * hot_metal_mass * 0.01 * 2.14  # Si氧化生成SiO2
                sio2_from_refractory = 3.0  # 耐火材料溶损贡献
                sio2_from_scrap = scrap_mass * 0.02  # 废钢表面氧化皮
                total_sio2 = sio2_from_si_oxidation + sio2_from_refractory + sio2_from_scrap
                df_slag.loc[idx, 'slag_sio2'] = np.clip(total_sio2, 8, 25)

                # 3. 炉渣碱度（CaO/SiO2）
                basicity = total_cao / (total_sio2 + 1e-6)
                df_slag.loc[idx, 'slag_basicity'] = np.clip(basicity, 2.0, 4.5)

                # 4. 炉渣FeO含量估算（精确版）
                # FeO主要来源：铁水氧化、废钢氧化、铁损
                oxygen_for_fe_oxidation = oxygen_consumed * 0.15  # 15%氧气用于铁氧化
                feo_from_oxidation = oxygen_for_fe_oxidation / 1000 * 71.85 / 16  # FeO分子量/O原子量
                feo_from_scrap = scrap_mass * 0.05  # 废钢氧化贡献
                iron_loss_rate = 0.02  # 2%铁损
                feo_from_iron_loss = hot_metal_mass * iron_loss_rate * 1.29  # Fe->FeO
                total_feo = feo_from_oxidation + feo_from_scrap + feo_from_iron_loss
                df_slag.loc[idx, 'slag_feo'] = np.clip(total_feo, 8, 30)

                # 5. 炉渣MgO含量估算
                mgo_from_dolomite = dolomite * 0.42  # 白云石中MgO含量约42%
                mgo_from_refractory = 2.5  # 镁砖溶损
                total_mgo = mgo_from_dolomite + mgo_from_refractory
                df_slag.loc[idx, 'slag_mgo'] = np.clip(total_mgo, 4, 18)

                # 6. 炉渣P2O5含量估算
                p2o5_content = p_content * hot_metal_mass * 0.01 * 2.29  # P->P2O5转换
                df_slag.loc[idx, 'slag_p2o5'] = np.clip(p2o5_content, 0.8, 4.0)

                # 7. 炉渣MnO含量估算
                mno_content = mn_content * hot_metal_mass * 0.01 * 1.29  # Mn->MnO转换
                df_slag.loc[idx, 'slag_mno'] = np.clip(mno_content, 2, 8)

                # 8. 炉渣液相线温度估算（改进版）
                # 基于多元回归模型的液相线温度
                liquidus_temp = (1650 - basicity * 25 + total_feo * 3 - total_mgo * 4 +
                                p2o5_content * 15 - total_sio2 * 2)
                df_slag.loc[idx, 'slag_liquidus_temp'] = np.clip(liquidus_temp, 1420, 1680)

                # 9. 炉渣粘度指数（改进版）
                # 基于成分的粘度估算
                viscosity_index = (basicity * 0.4 + total_mgo * 0.3 - total_feo * 0.15 +
                                 total_sio2 * 0.1)
                df_slag.loc[idx, 'slag_viscosity_index'] = np.clip(viscosity_index, 0.8, 4.0)

                # 10. 炉渣脱磷能力指数（改进版）
                # 基于碱度、温度和FeO的脱磷能力
                temp_factor = (1650 - liquidus_temp) / 100
                dephosphorization_index = (basicity * 0.5 + temp_factor * 0.3 +
                                         (total_feo / 20) * 0.2)
                df_slag.loc[idx, 'slag_dephosphorization_index'] = np.clip(dephosphorization_index, 1.5, 5.0)

                # 11. 炉渣泡沫指数（改进版）
                # 基于FeO、碱度和温度的泡沫倾向
                foam_index = (total_feo * 0.15 + basicity * 0.25 +
                            (hot_metal_temp - 1300) * 0.01)
                df_slag.loc[idx, 'slag_foam_index'] = np.clip(foam_index, 1.5, 6.0)

                # 12. 炉渣热容指数
                # 基于成分的热容估算
                heat_capacity_index = (total_cao * 0.02 + total_sio2 * 0.03 +
                                     total_feo * 0.025 + total_mgo * 0.035)
                df_slag.loc[idx, 'slag_heat_capacity'] = np.clip(heat_capacity_index, 1.0, 3.0)

                # 13. 炉渣导热系数指数
                thermal_conductivity = (total_feo * 0.1 + total_mgo * 0.08 -
                                      total_sio2 * 0.05 + basicity * 0.1)
                df_slag.loc[idx, 'slag_thermal_conductivity'] = np.clip(thermal_conductivity, 0.5, 2.5)

                # 14. 炉渣化学活性指数
                # 基于成分活性的综合指数
                chemical_activity = (basicity * 0.3 + (total_feo / 15) * 0.4 +
                                   (total_mgo / 10) * 0.3)
                df_slag.loc[idx, 'slag_chemical_activity'] = np.clip(chemical_activity, 1.0, 4.0)

                # 15. 炉渣平衡指数
                # 各成分间的平衡性
                balance_index = 1 / (1 + abs(basicity - 3.0) + abs(total_feo - 15) +
                               abs(total_mgo - 8))
                df_slag.loc[idx, 'slag_balance_index'] = np.clip(balance_index, 0.1, 1.0)

            except Exception as e:
                logger.warning(f"计算第{idx}行炉渣特征时出错: {e}")
                # 设置默认值
                default_values = {
                    'slag_cao': 45.0, 'slag_sio2': 15.0, 'slag_basicity': 3.0,
                    'slag_feo': 15.0, 'slag_mgo': 8.0, 'slag_p2o5': 2.0,
                    'slag_mno': 4.0, 'slag_liquidus_temp': 1550, 'slag_viscosity_index': 2.0,
                    'slag_dephosphorization_index': 3.0, 'slag_foam_index': 3.0,
                    'slag_heat_capacity': 2.0, 'slag_thermal_conductivity': 1.5,
                    'slag_chemical_activity': 2.5, 'slag_balance_index': 0.5
                }
                for key, value in default_values.items():
                    df_slag.loc[idx, key] = value

        logger.info("高级炉渣成分特征创建完成")
        return df_slag

    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            if isinstance(value, str):
                value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
            return float(value)
        except:
            return default

    def create_comprehensive_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建综合特征（包含炉渣特征）"""
        logger.info("创建综合特征")

        # 首先创建炉渣特征
        df_features = self.create_advanced_slag_features(df)

        # 1. 基础工程特征
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # 2. 成分交互特征
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['C_MN_interaction'] = df_features['铁水C'] * df_features['铁水MN']
        df_features['SI_MN_interaction'] = df_features['铁水SI'] * df_features['铁水MN']
        df_features['C_P_interaction'] = df_features['铁水C'] * df_features['铁水P']

        # 3. 温度相关特征
        df_features['temp_carbon_ratio'] = df_features['铁水温度'] / (df_features['铁水C'] + 1e-6)
        df_features['temp_oxygen_ratio'] = df_features['铁水温度'] / (df_features['oxygen_intensity'] + 1e-6)
        df_features['temp_scrap_interaction'] = df_features['铁水温度'] * df_features['scrap_ratio']

        # 4. 炉渣-工艺交互特征（关键突破点）
        if 'slag_basicity' in df_features.columns:
            df_features['basicity_oxygen_interaction'] = df_features['slag_basicity'] * df_features['oxygen_intensity']
            df_features['feo_oxygen_interaction'] = df_features['slag_feo'] * df_features['oxygen_intensity']
            df_features['basicity_temp_interaction'] = df_features['slag_basicity'] * df_features['铁水温度']
            df_features['liquidus_temp_diff'] = df_features['铁水温度'] - df_features['slag_liquidus_temp']
            df_features['slag_temp_efficiency'] = df_features['slag_thermal_conductivity'] * df_features['铁水温度']
            df_features['slag_heat_balance'] = df_features['slag_heat_capacity'] * df_features['oxygen_intensity']
            df_features['slag_chemical_temp'] = df_features['slag_chemical_activity'] * df_features['铁水温度']

        # 5. 高阶特征
        df_features['carbon_squared'] = df_features['铁水C'] ** 2
        df_features['silicon_squared'] = df_features['铁水SI'] ** 2
        df_features['oxygen_squared'] = df_features['oxygen_intensity'] ** 2

        # 6. 比率特征
        df_features['C_to_SI_ratio'] = df_features['铁水C'] / (df_features['铁水SI'] + 1e-6)
        df_features['MN_to_P_ratio'] = df_features['铁水MN'] / (df_features['铁水P'] + 1e-6)
        df_features['lime_to_scrap_ratio'] = df_features['石灰'] / (df_features['废钢'] + 1e-6)

        # 7. 炉渣比率特征（新增）
        if 'slag_cao' in df_features.columns:
            df_features['cao_to_sio2_ratio'] = df_features['slag_cao'] / (df_features['slag_sio2'] + 1e-6)
            df_features['feo_to_mgo_ratio'] = df_features['slag_feo'] / (df_features['slag_mgo'] + 1e-6)
            df_features['slag_total_flux'] = df_features['slag_cao'] + df_features['slag_mgo']
            df_features['slag_acid_components'] = df_features['slag_sio2'] + df_features['slag_p2o5']

        # 8. 钢种分类特征
        if '钢种' in df_features.columns:
            def classify_steel_grade(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70', 'C72DA', 'SWRCH']):
                    return '高碳钢'
                elif any(x in steel_type for x in ['ER50', 'ML40CR', '40CR', '20CRMO']):
                    return '合金钢'
                elif any(x in steel_type for x in ['304', '316', '321', '430']):
                    return '不锈钢'
                else:
                    return '普通钢'

            df_features['steel_category'] = df_features['钢种'].apply(classify_steel_grade)

            def estimate_carbon_content(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70']):
                    return 'high_carbon'
                elif any(x in steel_type for x in ['20', '16MN']):
                    return 'low_carbon'
                else:
                    return 'medium_carbon'

            df_features['carbon_grade'] = df_features['钢种'].apply(estimate_carbon_content)

        # 填充缺失值
        numeric_cols = df_features.select_dtypes(include=[np.number]).columns
        df_features[numeric_cols] = df_features[numeric_cols].fillna(df_features[numeric_cols].median())

        logger.info("综合特征创建完成")
        return df_features

    def prepare_data_for_models(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List[str], np.ndarray]:
        """为模型准备数据（包含炉渣特征）"""
        logger.info("准备模型数据")

        exclude_cols = ['炉号', '出钢重量估算', 'Unnamed: 4', '钢水温度']

        feature_cols = [col for col in df.columns if col not in exclude_cols]
        X = df[feature_cols].copy()

        if '钢水温度' in df.columns:
            y = df['钢水温度'].copy()
        else:
            y = pd.Series([0] * len(df))

        # 识别分类特征
        categorical_features = []
        for col in X.columns:
            if X[col].dtype == 'object' or col in ['steel_category', 'carbon_grade']:
                categorical_features.append(col)

        # 处理分类特征
        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                X[col] = self.label_encoders[col].fit_transform(X[col].astype(str))
            else:
                try:
                    X[col] = self.label_encoders[col].transform(X[col].astype(str))
                except ValueError:
                    mode_value = X[col].mode()[0] if not X[col].mode().empty else 0
                    X[col] = X[col].apply(lambda x: self.label_encoders[col].transform([str(x)])[0]
                                         if str(x) in self.label_encoders[col].classes_ else mode_value)

        # 最终数据清理
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        categorical_cols = X.select_dtypes(include=['object']).columns

        if len(numeric_cols) > 0:
            X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())

        if len(categorical_cols) > 0:
            for col in categorical_cols:
                X[col] = X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'unknown')

        # 提取炉渣特征用于损失函数
        slag_feature_cols = [col for col in X.columns if 'slag_' in col]
        if slag_feature_cols:
            slag_features = X[slag_feature_cols].values
        else:
            slag_features = np.zeros((len(X), 2))  # 默认炉渣特征

        logger.info(f"数据准备完成：{X.shape[1]}个特征，{len(categorical_features)}个分类特征，{len(slag_feature_cols)}个炉渣特征")
        return X, y, categorical_features, slag_features

    def multi_objective_hyperparameter_optimization(self, X: pd.DataFrame, y: pd.Series,
                                                   categorical_features: List[str],
                                                   slag_features: np.ndarray) -> Dict[str, Any]:
        """多目标超参数优化"""
        logger.info("开始多目标超参数优化")

        if not OPTUNA_AVAILABLE:
            logger.warning("Optuna不可用，使用默认参数")
            return {}

        optimization_results = {}

        # 1. XGBoost多目标优化
        def optimize_xgboost_multi_objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 300, 1200),
                'max_depth': trial.suggest_int('max_depth', 5, 12),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.15, log=True),
                'subsample': trial.suggest_float('subsample', 0.8, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 5, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 5, log=True),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 8),
                'gamma': trial.suggest_float('gamma', 0, 3),
                'random_state': 42
            }

            model = xgb.XGBRegressor(**params)

            # 使用3折交叉验证
            tscv = TimeSeriesSplit(n_splits=3)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]
                slag_train_cv, slag_val_cv = slag_features[train_idx], slag_features[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.advanced_multi_objective_loss(y_val_cv.values, y_pred_cv, slag_val_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("多目标优化XGBoost超参数...")
        study_xgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=15),
            pruner=MedianPruner(n_startup_trials=8, n_warmup_steps=12)
        )
        study_xgb.optimize(optimize_xgboost_multi_objective, n_trials=60, timeout=2400)

        optimization_results['XGBoost_MultiObj'] = {
            'best_params': study_xgb.best_params,
            'best_score': study_xgb.best_value,
            'study': study_xgb
        }

        # 2. LightGBM多目标优化
        def optimize_lightgbm_multi_objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 300, 1200),
                'max_depth': trial.suggest_int('max_depth', 5, 12),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.15, log=True),
                'subsample': trial.suggest_float('subsample', 0.8, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.8, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 5, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 5, log=True),
                'min_child_samples': trial.suggest_int('min_child_samples', 10, 50),
                'num_leaves': trial.suggest_int('num_leaves', 30, 150),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.8, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.8, 1.0),
                'random_state': 42,
                'verbose': -1
            }

            model = lgb.LGBMRegressor(**params)

            tscv = TimeSeriesSplit(n_splits=3)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]
                slag_train_cv, slag_val_cv = slag_features[train_idx], slag_features[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.advanced_multi_objective_loss(y_val_cv.values, y_pred_cv, slag_val_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("多目标优化LightGBM超参数...")
        study_lgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=15),
            pruner=MedianPruner(n_startup_trials=8, n_warmup_steps=12)
        )
        study_lgb.optimize(optimize_lightgbm_multi_objective, n_trials=60, timeout=2400)

        optimization_results['LightGBM_MultiObj'] = {
            'best_params': study_lgb.best_params,
            'best_score': study_lgb.best_value,
            'study': study_lgb
        }

        # 3. CatBoost多目标优化
        if CATBOOST_AVAILABLE:
            def optimize_catboost_multi_objective(trial):
                params = {
                    'iterations': trial.suggest_int('iterations', 300, 1000),
                    'depth': trial.suggest_int('depth', 5, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.15, log=True),
                    'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1e-8, 10, log=True),
                    'border_count': trial.suggest_int('border_count', 128, 255),
                    'bagging_temperature': trial.suggest_float('bagging_temperature', 0, 3),
                    'random_strength': trial.suggest_float('random_strength', 0, 3),
                    'random_state': 42,
                    'verbose': False
                }

                cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
                model = cb.CatBoostRegressor(cat_features=cat_features_idx, **params)

                tscv = TimeSeriesSplit(n_splits=3)
                scores = []

                for train_idx, val_idx in tscv.split(X):
                    X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                    y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]
                    slag_train_cv, slag_val_cv = slag_features[train_idx], slag_features[val_idx]

                    model.fit(X_train_cv, y_train_cv)
                    y_pred_cv = model.predict(X_val_cv)

                    score = self.advanced_multi_objective_loss(y_val_cv.values, y_pred_cv, slag_val_cv)
                    scores.append(score)

                return np.mean(scores)

            logger.info("多目标优化CatBoost超参数...")
            study_cat = optuna.create_study(
                direction='minimize',
                sampler=TPESampler(seed=42, n_startup_trials=12),
                pruner=MedianPruner(n_startup_trials=6, n_warmup_steps=10)
            )
            study_cat.optimize(optimize_catboost_multi_objective, n_trials=50, timeout=2000)

            optimization_results['CatBoost_MultiObj'] = {
                'best_params': study_cat.best_params,
                'best_score': study_cat.best_value,
                'study': study_cat
            }

        logger.info("多目标超参数优化完成")
        return optimization_results

    def train_multi_objective_models(self, X: pd.DataFrame, y: pd.Series,
                                   categorical_features: List[str],
                                   optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """使用多目标优化后的超参数训练模型"""
        logger.info("使用多目标优化后的超参数训练模型")

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        models = {}

        # 1. 训练多目标优化后的XGBoost
        if 'XGBoost_MultiObj' in optimization_results:
            logger.info("训练多目标优化后的XGBoost模型...")
            best_params = optimization_results['XGBoost_MultiObj']['best_params']

            xgb_model = xgb.XGBRegressor(**best_params)
            xgb_model.fit(X_train, y_train)
            y_pred_xgb = xgb_model.predict(X_test)

            models['XGBoost_MultiObj'] = {
                'model': xgb_model,
                'mae': mean_absolute_error(y_test, y_pred_xgb),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_xgb)),
                'r2': r2_score(y_test, y_pred_xgb),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 15),
                'target_accuracy_10': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 10),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_xgb
            }

        # 2. 训练多目标优化后的LightGBM
        if 'LightGBM_MultiObj' in optimization_results:
            logger.info("训练多目标优化后的LightGBM模型...")
            best_params = optimization_results['LightGBM_MultiObj']['best_params']

            lgb_model = lgb.LGBMRegressor(**best_params)
            lgb_model.fit(X_train, y_train)
            y_pred_lgb = lgb_model.predict(X_test)

            models['LightGBM_MultiObj'] = {
                'model': lgb_model,
                'mae': mean_absolute_error(y_test, y_pred_lgb),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_lgb)),
                'r2': r2_score(y_test, y_pred_lgb),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 15),
                'target_accuracy_10': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 10),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_lgb
            }

        # 3. 训练多目标优化后的CatBoost
        if 'CatBoost_MultiObj' in optimization_results and CATBOOST_AVAILABLE:
            logger.info("训练多目标优化后的CatBoost模型...")
            best_params = optimization_results['CatBoost_MultiObj']['best_params']

            cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
            cat_model = cb.CatBoostRegressor(cat_features=cat_features_idx, **best_params)
            cat_model.fit(X_train, y_train)
            y_pred_cat = cat_model.predict(X_test)

            models['CatBoost_MultiObj'] = {
                'model': cat_model,
                'mae': mean_absolute_error(y_test, y_pred_cat),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_cat)),
                'r2': r2_score(y_test, y_pred_cat),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_cat, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_cat, 15),
                'target_accuracy_10': self.calculate_target_accuracy(y_test.values, y_pred_cat, 10),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_cat
            }

        # 4. 创建高级集成模型
        if len(models) >= 2:
            logger.info("创建高级集成模型...")

            # 基于多指标的权重分配
            weights = {}

            for name, result in models.items():
                accuracy_20 = result['target_accuracy_20']
                accuracy_15 = result['target_accuracy_15']
                mae = result['mae']
                r2 = result['r2']

                # 综合权重：多精度权重50% + MAE权重25% + R²权重25%
                weight = ((accuracy_20 + accuracy_15) / 200) * 0.5 + (1 / (mae / 15)) * 0.25 + r2 * 0.25
                weights[name] = weight

            # 归一化权重
            total_weight = sum(weights.values())
            for name in weights:
                weights[name] /= total_weight

            # 集成预测
            ensemble_pred = np.zeros(len(y_test))
            for name, result in models.items():
                ensemble_pred += weights[name] * result['y_pred']

            models['Ensemble_MultiObj'] = {
                'mae': mean_absolute_error(y_test, ensemble_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, ensemble_pred)),
                'r2': r2_score(y_test, ensemble_pred),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, ensemble_pred, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, ensemble_pred, 15),
                'target_accuracy_10': self.calculate_target_accuracy(y_test.values, ensemble_pred, 10),
                'weights': weights,
                'y_test': y_test,
                'y_pred': ensemble_pred
            }

        self.models = models
        return models

def main():
    """主函数 - 阶段3：多目标优化 + 炉渣成分特征"""
    logger.info("=== 阶段3：多目标优化 + 炉渣成分特征 ===")
    logger.info("目标：通过炉渣成分特征和多目标优化实现重大突破")
    logger.info("预期提升：从75.8%提升到85-90%")

    try:
        # 1. 检查环境
        logger.info("=== 环境检查 ===")
        logger.info(f"CatBoost可用: {CATBOOST_AVAILABLE}")
        logger.info(f"Optuna可用: {OPTUNA_AVAILABLE}")

        if not OPTUNA_AVAILABLE:
            logger.error("Optuna不可用，无法进行超参数优化")
            return

        # 2. 数据加载
        logger.info("=== 数据加载 ===")
        train_df = pd.read_excel('1-4521剔除重复20250514.xlsx')

        logger.info(f"训练数据: {train_df.shape}")

        # 3. 创建优化器
        optimizer = Step3MultiObjectiveSlagOptimizer()

        # 4. 数据清理
        logger.info("=== 数据清理 ===")
        train_cleaned = optimizer.robust_data_cleaning(train_df)

        logger.info(f"训练数据清理后: {train_cleaned.shape}")

        # 5. 综合特征工程（包含炉渣特征）
        logger.info("=== 综合特征工程（包含炉渣特征）===")
        train_features = optimizer.create_comprehensive_features(train_cleaned)

        # 6. 准备建模数据
        logger.info("=== 数据准备 ===")
        X_train, y_train, categorical_features, slag_features = optimizer.prepare_data_for_models(train_features)

        logger.info(f"最终特征数: {X_train.shape[1]}")
        logger.info(f"分类特征数: {len(categorical_features)}")
        logger.info(f"炉渣特征数: {slag_features.shape[1]}")
        logger.info(f"训练样本数: {len(X_train)}")

        # 7. 多目标超参数优化
        logger.info("=== 多目标超参数优化 ===")
        optimization_results = optimizer.multi_objective_hyperparameter_optimization(
            X_train, y_train, categorical_features, slag_features
        )

        if not optimization_results:
            logger.error("多目标超参数优化失败")
            return

        logger.info(f"成功优化{len(optimization_results)}个模型")

        # 8. 训练多目标优化后的模型
        logger.info("=== 训练多目标优化后的模型 ===")
        model_results = optimizer.train_multi_objective_models(
            X_train, y_train, categorical_features, optimization_results
        )

        if not model_results:
            logger.error("没有成功训练的模型")
            return

        logger.info(f"成功训练{len(model_results)}个模型")

        # 9. 结果评估
        logger.info("=== 结果评估 ===")

        # 显示各模型性能
        logger.info("各模型性能对比:")
        for name, result in model_results.items():
            logger.info(f"  {name}:")
            logger.info(f"    MAE: {result['mae']:.1f}°C")
            logger.info(f"    RMSE: {result['rmse']:.1f}°C")
            logger.info(f"    R²: {result['r2']:.4f}")
            logger.info(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%")
            logger.info(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%")
            logger.info(f"    目标范围±10°C精度: {result['target_accuracy_10']:.1f}%")

        # 找出最佳模型
        best_model_name = max(model_results.keys(), key=lambda x: model_results[x]['target_accuracy_20'])
        best_accuracy = model_results[best_model_name]['target_accuracy_20']

        logger.info(f"\n最佳模型: {best_model_name}")
        logger.info(f"最佳精度: {best_accuracy:.1f}%")

        # 10. 生成报告
        logger.info("=== 生成报告 ===")

        report_file = f"step3_multi_obj_slag_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("阶段3：多目标优化 + 炉渣成分特征报告\n")
            f.write("=" * 60 + "\n\n")

            f.write("🎯 目标: 通过炉渣成分特征和多目标优化实现重大突破\n")
            f.write("预期提升：从75.8%提升到85-90%\n\n")

            f.write("🔧 关键技术突破:\n")
            f.write("1. 高级炉渣成分特征（15个炉渣特征）\n")
            f.write("2. 炉渣-工艺交互特征\n")
            f.write("3. 多目标优化损失函数\n")
            f.write("4. 炉渣一致性约束\n")
            f.write("5. 高级集成策略\n\n")

            f.write("📊 模型性能:\n")
            for name, result in model_results.items():
                f.write(f"  {name}:\n")
                f.write(f"    MAE: {result['mae']:.1f}°C\n")
                f.write(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%\n")
                f.write(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%\n")
                f.write(f"    目标范围±10°C精度: {result['target_accuracy_10']:.1f}%\n\n")

            f.write(f"🏆 最佳模型: {best_model_name} ({best_accuracy:.1f}%)\n\n")

            # 计算提升幅度
            baseline_accuracy = 75.8  # 阶段2的最佳精度
            improvement = best_accuracy - baseline_accuracy

            f.write("📈 性能提升分析:\n")
            f.write(f"  阶段2最佳精度: {baseline_accuracy:.1f}%\n")
            f.write(f"  阶段3最佳精度: {best_accuracy:.1f}%\n")
            f.write(f"  绝对提升: +{improvement:.1f}%\n")
            f.write(f"  相对提升: +{improvement/baseline_accuracy*100:.1f}%\n\n")

            f.write("✅ 阶段3完成状态:\n")
            if best_accuracy >= 90:
                f.write("  🎉🎉🎉 超额完成目标！精度达到90%+！\n")
                f.write("  ✅ 已达到世界先进水平！\n")
            elif best_accuracy >= 85:
                f.write("  🎯🎯🎯 成功达到目标！精度达到85%+！\n")
                f.write("  ✅ 可以进入阶段4：最终优化冲击95%\n")
            elif improvement >= 5:
                f.write("  ⚡⚡⚡ 重大突破！显著提升！\n")
                f.write("  ✅ 炉渣特征发挥关键作用\n")
            else:
                f.write("  ⚠️ 提升有限，需要进一步优化\n")
                f.write("  🔧 建议检查炉渣特征计算\n")

        logger.info(f"报告已保存到: {report_file}")

        # 11. 最终总结
        logger.info("=== 阶段3总结 ===")
        logger.info(f"成功训练{len(model_results)}个多目标优化模型")
        logger.info(f"最佳模型精度: {best_accuracy:.1f}%")
        logger.info(f"相比阶段2提升: +{improvement:.1f}%")
        logger.info(f"炉渣特征数量: {slag_features.shape[1]}")

        if best_accuracy >= 90:
            logger.info("🎉🎉🎉 阶段3超额完成！已达到世界先进水平！🎉🎉🎉")
        elif best_accuracy >= 85:
            logger.info("🎯🎯🎯 阶段3成功完成！可以冲击95%目标！🎯🎯🎯")
        elif improvement >= 5:
            logger.info("⚡⚡⚡ 阶段3重大突破！炉渣特征发挥关键作用！⚡⚡⚡")
        else:
            logger.info("🔧 阶段3需要进一步优化炉渣特征")

        return model_results

    except Exception as e:
        logger.error(f"阶段3运行出错: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    main()
