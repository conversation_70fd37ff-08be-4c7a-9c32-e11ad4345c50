"""
训练钢水温度预测模型并保存。
该脚本使用第四批数据集进行模型训练，然后保存模型以便后续预测使用。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
import pickle
import time
import json
from typing import Dict, List, Tuple, Any, Optional

# 导入特征工程函数 和 模型开发相关
from sklearn.model_selection import train_test_split # 确保导入
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score # 确保导入
import joblib # 确保导入

# 尝试导入项目内的模块
try:
    from steel_temp_prediction.feature_engineering import engineer_all_features
    from steel_temp_prediction.model_development import SequentialThinkingModel, train_sequential_thinking_model # 确保导入 train_sequential_thinking_model
    # from steel_temp_prediction.utils import calculate_metrics, plot_feature_importance, format_time # 这些辅助函数如果需要，也应从utils导入
except ImportError:
    logger.warning("无法从 steel_temp_prediction 包导入，尝试直接导入当前目录的模块...")
    try:
        from feature_engineering import engineer_all_features
        from model_development import SequentialThinkingModel, train_sequential_thinking_model # 确保导入 train_sequential_thinking_model
        # from utils import calculate_metrics, plot_feature_importance, format_time
    except ImportError as e:
        logger.error(f"直接导入模块失败: {e}。请确保 feature_engineering.py 和 model_development.py 在Python路径中或与train_model.py在同一目录结构下。")
        sys.exit("关键模块导入失败，无法继续。")

# 确保模块可以被导入
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='model_training.log'
)
logger = logging.getLogger(__name__)
# 同时输出到控制台
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logger.addHandler(console)

# 目标列名
TARGET_COL = '钢水温度'
#  идентификационные столбцы, которые не должны использоваться как признаки
ID_COLS = ['冶炼唯一标识', '料次号', '炉号', '炉次', '装入制度', '钢种'] # 根据您的数据添加或修改

def load_training_data(file_path: str) -> pd.DataFrame:
    """
    加载训练数据。
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        加载的DataFrame
    """
    logger.info(f"加载训练数据: {file_path}")
    try:
        df = pd.read_excel(file_path)
        logger.info(f"成功加载数据，共 {len(df)} 行, {len(df.columns)} 列")
        logger.info(f"数据中的列名: {df.columns.tolist()}") # 打印列名以供调试
        
        # 检查是否包含目标变量
        if TARGET_COL not in df.columns:
            logger.error(f"训练数据中没有目标变量'{TARGET_COL}'，实际列名: {df.columns.tolist()}")
            raise ValueError(f"缺少目标变量'{TARGET_COL}'")
            
        return df
    except Exception as e:
        logger.error(f"加载数据时出错: {str(e)}")
        raise

def prepare_training_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    准备训练数据，包括清洗和预处理。
    
    Args:
        df: 原始DataFrame
        
    Returns:
        清洗后的DataFrame
    """
    logger.info("准备训练数据...")
    
    # 复制数据，避免修改原始数据
    df_clean = df.copy()
    
    # 1. 检查并删除重复项
    duplicates = df_clean.duplicated()
    if duplicates.any():
        logger.info(f"删除 {duplicates.sum()} 行重复数据")
        df_clean = df_clean.drop_duplicates()
    
    # 2. 检查并处理缺失值
    missing_values = df_clean.isnull().sum()
    if missing_values.any():
        logger.info("处理缺失值:")
        for col in missing_values[missing_values > 0].index:
            missing_count = missing_values[col]
            missing_percent = missing_count / len(df_clean) * 100
            logger.info(f"  列 '{col}' 有 {missing_count} 缺失值 ({missing_percent:.2f}%)")
            
            if df_clean[col].dtype in ['int64', 'float64']:
                # 对于数值型，可以考虑中位数、均值或更复杂的插补
                fill_value = df_clean[col].median()
                if pd.isna(fill_value): # 如果中位数也是NaN (可能整列都是NaN)
                    fill_value = 0 # Fallback to 0 or a global constant
                df_clean[col].fillna(fill_value, inplace=True)
                logger.info(f"    数值列 '{col}' 用中位数 {fill_value:.4f} 填充.")
            elif pd.api.types.is_datetime64_any_dtype(df_clean[col]):
                # 对于日期时间类型，可以考虑向前填充、向后填充或填充一个特定日期
                # df_clean[col].fillna(method='ffill', inplace=True) # 示例：向前填充
                # 这里我们暂时不填充日期，因为它们通常不会直接作为模型输入特征
                logger.info(f"    日期时间列 '{col}' 暂不填充，将在特征选择阶段移除。")
            else: # 包括对象类型
                # 对于对象/分类类型，可以考虑用众数或特定占位符
                mode_val = df_clean[col].mode()
                if not mode_val.empty:
                    df_clean[col].fillna(mode_val[0], inplace=True)
                    logger.info(f"    对象/分类列 '{col}' 用众数 '{mode_val[0]}' 填充.")
                else: # 如果众数也为空 (可能整列都是NaN)
                    df_clean[col].fillna("未知", inplace=True)
                    logger.info(f"    对象/分类列 '{col}' 用 '未知' 填充.")
        logger.info("缺失值处理完成。")
    else:
        logger.info("数据中无缺失值。")

    # 3. 检查异常值
    for col in df_clean.select_dtypes(include=['int64', 'float64']).columns:
        mean = df_clean[col].mean()
        std = df_clean[col].std()
        lower_bound = mean - 3 * std
        upper_bound = mean + 3 * std
        
        outliers = df_clean[(df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)]
        if len(outliers) > 0:
            logger.info(f"  列 '{col}' 有 {len(outliers)} 个异常值")
            # 记录但不处理异常值，交给特征工程步骤处理
    
    logger.info(f"数据准备完成，剩余 {len(df_clean)} 行")
    return df_clean

def add_features_and_clean(df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
    """
    进行特征工程并清洗数据。
    
    Args:
        df: 输入DataFrame
        
    Returns:
        X_train: 训练特征 (89个，与ACTUAL_MODEL_INPUT_FEATURES一致)
        y_train: 训练目标 ('出钢温度')
    """
    logger.info("开始特征工程和数据准备...")
    
    # 0. 首先从输入df中分离目标变量，因为engineer_all_features不应看到它
    if TARGET_COL not in df.columns:
        logger.error(f"错误: 目标变量 '{TARGET_COL}' 未在输入数据中找到!")
        raise ValueError(f"目标变量 '{TARGET_COL}' 缺失")
    y_train = df[TARGET_COL].copy()
    df_inputs_only = df.drop(columns=[TARGET_COL])

    logger.info(f"目标变量 '{TARGET_COL}' 已分离。输入特征送往 engineer_all_features: {df_inputs_only.shape[1]} 列")

    # 1. 进行特征工程 (应返回 ACTUAL_MODEL_INPUT_FEATURES 定义的89个特征)
    # 注意：传递给 engineer_all_features 的 df 不应包含目标变量
    X_train = engineer_all_features(
        df_inputs_only, # 使用去除了目标变量的df
        enable_slag=True,
        enable_process_params=True,
        enable_time_series=False,  # 假设没有时间序列
        enable_interactions=True,
        enable_physicochem_props=True,
        enable_advanced_slag=True,
        enable_lance_dynamics=True
    )
    
    logger.info(f"特征工程完成，返回特征数: {len(X_train.columns)}")
    if not all(X_train.columns == ACTUAL_MODEL_INPUT_FEATURES):
        logger.warning("警告: engineer_all_features 返回的特征列名/顺序与 ACTUAL_MODEL_INPUT_FEATURES 不完全一致。将尝试按 ACTUAL_MODEL_INPUT_FEATURES 对齐。")
        # 强制对齐到 ACTUAL_MODEL_INPUT_FEATURES
        temp_X_train = pd.DataFrame(index=X_train.index)
        for col_name in ACTUAL_MODEL_INPUT_FEATURES:
            if col_name in X_train.columns:
                temp_X_train[col_name] = X_train[col_name]
            else:
                logger.error(f"错误：期望的输入特征 '{col_name}\' 未由 engineer_all_features 生成！将填充0，但这可能表示严重问题。")
                temp_X_train[col_name] = 0
        X_train = temp_X_train

    if X_train.shape[1] != len(ACTUAL_MODEL_INPUT_FEATURES):
        logger.error(f"错误：特征工程后，特征数量为 {X_train.shape[1]}，但期望为 {len(ACTUAL_MODEL_INPUT_FEATURES)} (ACTUAL_MODEL_INPUT_FEATURES)。")
        # 可以选择在这里停止，或者继续，但模型可能无法正确训练

    # 2. 数据清洗 - 处理inf和极大值 (在89个特征上进行)
    logger.info("清洗特征数据 (X_train)，处理inf和异常值...")
    
    # 处理磷容量系数
    if 'feature_slag_phosphate_capacity' in X_train.columns:
        logger.info(f"对磷容量系数取对数，原始范围: "
                  f"{X_train['feature_slag_phosphate_capacity'].min():.2e}-{X_train['feature_slag_phosphate_capacity'].max():.2e}")
        # 先确保所有值都是正数，然后取对数
        X_train['feature_slag_phosphate_capacity'] = np.where(
            X_train['feature_slag_phosphate_capacity'] > 0,
            np.log10(X_train['feature_slag_phosphate_capacity']),
            0
        )
    
    # 处理其他可能的极大值特征
    for col in X_train.columns:
        try:
            if X_train[col].max() > 1e20:
                logger.info(f"对特征 {col} 取对数")
                X_train[col] = np.where(X_train[col] > 0, np.log10(X_train[col]), 0)
        except (TypeError, ValueError):
            logger.warning(f"无法处理特征 {col} 的极值，将删除该特征")
            X_train = X_train.drop(columns=[col])
    
    # 替换inf和-inf
    X_train = X_train.replace([np.inf, -np.inf], np.nan)
    
    # 使用中位数填充NaN值
    for col in X_train.columns:
        if X_train[col].isnull().any():
            median_val = X_train[col].median()
            if pd.isna(median_val): # 如果列全为NaN，中位数也是NaN
                median_val = 0 # Fallback to 0
                logger.warning(f"Column '{col}' is all NaN or median is NaN, filling with 0.")
            X_train[col].fillna(median_val, inplace=True)
            logger.info(f"Filled NaN in column '{col}' with median: {median_val}")

    # 处理极值
    for col in X_train.columns:
        median = X_train[col].median()
        std = X_train[col].std()
        upper_limit = median + 5 * std
        lower_limit = median - 5 * std
        X_train[col] = X_train[col].clip(lower_limit, upper_limit)
    
    # 检查是否有问题的特征
    col_stats = X_train.describe().transpose()[['min', 'max', 'mean', 'std']]
    problematic_cols = col_stats[(col_stats['max'] > 1e10) | (col_stats['min'] < -1e10) | 
                              col_stats['std'].isna() | np.isinf(col_stats['std'])]
    
    if not problematic_cols.empty:
        for idx, row in problematic_cols.iterrows():
            logger.warning(f"删除有问题的特征: {idx}")
            X_train = X_train.drop(columns=[idx])
    
    # 3. 如果模型期望通用特征名, 则进行重命名
    # 我们将X_train的列名 (ACTUAL_MODEL_INPUT_FEATURES) 重命名为 EXPECTED_GENERIC_INPUT_FEATURES_26
    if X_train.shape[1] == len(ACTUAL_MODEL_INPUT_FEATURES) and X_train.shape[1] == len(EXPECTED_GENERIC_INPUT_FEATURES_26):
        logger.info(f"X_train 列名将从 {len(ACTUAL_MODEL_INPUT_FEATURES)} 个实际名称映射到 {len(EXPECTED_GENERIC_INPUT_FEATURES_26)} 个通用输入特征名 (e.g., feature_0, feature_1 ...)")
        X_train.columns = EXPECTED_GENERIC_INPUT_FEATURES_26
        logger.info(f"X_train 列名已更新为通用名。前5个: {X_train.columns.tolist()[:5]}")
    else:
        logger.error(f"X_train 特征数量 ({X_train.shape[1]}) 与期望的 {len(EXPECTED_GENERIC_INPUT_FEATURES_26)} 个通用特征名数量不符。实际输入特征名 (ACTUAL_MODEL_INPUT_FEATURES) 数量为 {len(ACTUAL_MODEL_INPUT_FEATURES)}。跳过重命名。模型训练可能失败或行为异常。")

    logger.info(f"数据清洗和特征准备完成，最终训练特征数: {X_train.shape[1]}")
    return X_train, y_train

def train_model(X_train: pd.DataFrame, y_train: pd.Series, model_path: str = 'steel_temp_model.pkl') -> Any:
    """
    训练模型。
    
    Args:
        X_train: 训练特征
        y_train: 训练目标
        model_path: 模型保存路径
        
    Returns:
        训练好的模型
    """
    logger.info("开始模型训练...")
    start_time = time.time()

    # 确保 y_train 是一维的 Series
    if not isinstance(y_train, pd.Series):
        if isinstance(y_train, pd.DataFrame) and y_train.shape[1] == 1:
            y_train = y_train.iloc[:, 0]
            logger.info("y_train was a DataFrame, converted to Series.")
        else:
            logger.error(f"y_train is not a Series and cannot be converted. Shape: {y_train.shape}")
            raise ValueError("y_train must be a pandas Series for model training.")
            
    # 划分训练集和验证集 (80/20)
    # 注意：这里我们用的是已经经过特征工程的X_train
    # 如果原始的X_train包含了需要保留的原始特征用于拆分，则需要在特征工程前拆分
    logger.info(f"Splitting data into training and validation sets (80/20). X_train shape before split: {X_train.shape}")
    X_train_processed, X_val_processed, y_train_processed, y_val_processed = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    logger.info(f"Training set shape: X={X_train_processed.shape}, y={y_train_processed.shape}")
    logger.info(f"Validation set shape: X={X_val_processed.shape}, y={y_val_processed.shape}")

    # 定义基础模型调优配置
    tune_base_config = {
        'xgboost': 50,
        'lightgbm': 50,
        'catboost': 50
    }
    # 定义元学习器调优配置
    tune_meta_n_iter_config = 50
    meta_learner_choice = 'catboost'

    logger.info(f"开始训练 SequentialThinkingModel...")
    logger.info(f"Base models to tune: {tune_base_config}")
    logger.info(f"Meta learner to tune: {meta_learner_choice} with {tune_meta_n_iter_config} trials.")

    # 使用SequentialThinkingModel进行训练
    # 传递X_val, y_val 以便在SequentialThinkingModel内部使用
    trained_model = train_sequential_thinking_model(
        X_train_processed, y_train_processed,
        X_val=X_val_processed, y_val=y_val_processed,
        tune_base_model_n_iters=tune_base_config,
        meta_learner_type=meta_learner_choice,
        include_original_features_in_meta=True, # 推荐保持为True
        include_prediction_interactions=False, # 可以尝试True/False
        tune_meta_model_n_iter=tune_meta_n_iter_config
    )
    
    training_time = time.time() - start_time
    logger.info(f"模型训练完成，耗时: {training_time:.2f} 秒")

    # 保存模型
    logger.info(f"保存模型到: {model_path}")
    try:
        # 使用 joblib 保存模型
        joblib.dump(trained_model, model_path)
        logger.info("模型成功保存。")
    except Exception as e:
        logger.error(f"保存模型时出错: {str(e)}")
        # Fallback to pickle if joblib fails (though joblib is generally preferred for sklearn models)
        try:
            with open(model_path, 'wb') as f:
                pickle.dump(trained_model, f)
            logger.info(f"模型通过 pickle 成功保存到 {model_path}")
        except Exception as e_pickle:
            logger.error(f"使用 pickle 保存模型也失败: {e_pickle}")
            raise # Re-raise the original error or a new one

    return trained_model

def main():
    """
    主函数，处理模型训练流程。
    """
    logger.info("开始模型训练流程...")
    
    project_root_dir = os.path.dirname(os.path.abspath(__file__))
    # 更改训练数据文件路径为项目根目录
    # train_data_file = os.path.join(project_root_dir, "steel_temp_prediction", "1-4521剔除重复20250514.xlsx") # 旧注释
    # train_data_file = os.path.join(os.path.dirname(project_root_dir), "1-4521剔除重复20250514.xlsx") # 之前的错误尝试
    train_data_file = os.path.join(project_root_dir, "1-4521剔除重复20250514.xlsx") # 正确的路径假设脚本在项目根目录的直接子目录中，或者数据在项目根目录
    logger.info(f"训练数据文件路径: {train_data_file}")

    if not os.path.exists(train_data_file):
        logger.error(f"训练数据文件未找到: {train_data_file}")
        # 尝试直接使用文件名，如果脚本本身就在包含数据文件的目录中
        alt_train_data_file = "1-4521剔除重复20250514.xlsx"
        if os.path.exists(alt_train_data_file):
            train_data_file = alt_train_data_file
            logger.info(f"在当前工作目录中找到训练数据: {train_data_file}")
        else:
            logger.error(f"在备选路径 ('{alt_train_data_file}') 也未找到训练数据。请确保文件存在于工作区根目录或脚本所在目录。")
            return

    output_dir = os.path.join(project_root_dir, "results")
    os.makedirs(output_dir, exist_ok=True)
    model_path = os.path.join(output_dir, "sequential_thinking_model_v2.pkl") # 保存到 results 文件夹
    
    # 1. 加载和预处理数据
    df_clean = prepare_training_data(load_training_data(train_data_file))
    
    # 2. 特征工程
    logger.info("应用特征工程...")
    df_featured = engineer_all_features(
        df_clean,
        interactions_poly_config={'degree': 2, 'target_cols': []} # 修正参数，目标列暂为空
    )
    logger.info(f"特征工程完成。处理后特征数量 (包括非输入列): {len(df_featured.columns)}")
    logger.info(f"特征工程后列名示例: {df_featured.columns[:20].tolist()}...") # 打印部分列名

    # 3. 准备 X 和 y，并确定最终的实际特征名
    if TARGET_COL not in df_featured.columns:
        logger.error(f"目标列 '{TARGET_COL}' 在特征工程后的DataFrame中丢失！")
        # 尝试从原始df_clean中恢复，但这可能不理想
        if TARGET_COL in df_clean.columns:
            df_featured[TARGET_COL] = df_clean[TARGET_COL]
            logger.warning(f"已从原始数据恢复目标列 '{TARGET_COL}'。")
        else:
            raise ValueError(f"特征工程后目标列 '{TARGET_COL}' 彻底丢失。")

    y = df_featured[TARGET_COL]

    # 确定需要排除的列
    datetime_cols = df_featured.select_dtypes(include=['datetime64[ns]']).columns.tolist()
    object_cols = df_featured.select_dtypes(include=['object']).columns.tolist()
    
    cols_to_drop_from_features = [TARGET_COL]
    for col_list in [ID_COLS, datetime_cols, object_cols]:
        for col_name in col_list:
            if col_name in df_featured.columns and col_name not in cols_to_drop_from_features:
                cols_to_drop_from_features.append(col_name)
                
    logger.info(f"将从特征集中移除以下列: {cols_to_drop_from_features}")
    
    X = df_featured.drop(columns=cols_to_drop_from_features, errors='ignore')
    
    # 处理特征中的无穷大值和NaN值 (在转换为通用名称之前)
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    if X.isnull().any().any():
        logger.info("训练集特征X中存在NaN值，将用中位数填充...")
        for col in X.columns[X.isnull().any()]:
            median_val = X[col].median()
            if pd.isna(median_val): median_val = 0 # Fallback
            X[col].fillna(median_val, inplace=True)
            logger.info(f"  列 '{col}' 用中位数 {median_val:.4f} 填充 (NaN处理)。")
    
    final_actual_feature_names = X.columns.tolist()
    logger.info(f"最终用于训练的实际特征数量: {len(final_actual_feature_names)}")
    logger.info(f"最终实际特征名 (前20个): {final_actual_feature_names[:20]}")

    # 保存实际特征名列表
    features_list_path = os.path.join(output_dir, "final_model_feature_names.json")
    try:
        with open(features_list_path, 'w', encoding='utf-8') as f:
            json.dump(final_actual_feature_names, f, ensure_ascii=False, indent=4)
        logger.info(f"实际特征名列表已保存到: {features_list_path}")
    except Exception as e:
        logger.error(f"保存特征名列表失败: {e}")

    # 将X的列名重命名为通用特征名 feature_0, feature_1, ...
    X.columns = [f'feature_{i}' for i in range(len(final_actual_feature_names))]
    logger.info(f"特征已重命名为通用名称 (如 feature_0, feature_1, ...)。用于训练的通用特征数量: {len(X.columns)}")

    # 4. 划分训练集和测试集 (使用通用特征名的X)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    logger.info(f"训练集形状: {X_train.shape}, 测试集形状: {X_test.shape}")

    # 5. 训练SequentialThinkingModel
    logger.info(f"Main: 开始训练 SequentialThinkingModel...")
    
    # 使用与 train_model 函数中一致的调优配置
    tune_base_final_config = {
        'xgboost': 50,
        'lightgbm': 50,
        'catboost': 50
    }
    tune_meta_final_n_iter_config = 50
    meta_learner_final_choice = 'catboost'
    include_interactions_final = False # 与 train_model 中的设置保持一致

    logger.info(f"Main: Base models to tune: {tune_base_final_config}")
    logger.info(f"Main: Meta learner to tune: {meta_learner_final_choice} with {tune_meta_final_n_iter_config} trials.")
    logger.info(f"Main: Include prediction interactions: {include_interactions_final}")

    # 调用 train_sequential_thinking_model 函数进行模型训练
    model = train_sequential_thinking_model(
        X_train, 
        y_train,
        X_val=X_test, 
        y_val=y_test,
        tune_base_model_n_iters=tune_base_final_config,
        meta_learner_type=meta_learner_final_choice,
        include_original_features_in_meta=True, 
        include_prediction_interactions=include_interactions_final,  
        tune_meta_model_n_iter=tune_meta_final_n_iter_config,     
        tune_meta_model_params=None
    )

    logger.info("SequentialThinkingModel 训练完成。")
    
    # 6. 评估模型
    logger.info("评估模型性能 (在测试集上，使用通用特征名)...")
    # 预测时，X_test已经具有通用特征名
    predictions = model.predict(X_test)
    
    mae = mean_absolute_error(y_test, predictions)
    rmse = np.sqrt(mean_squared_error(y_test, predictions))
    r2 = r2_score(y_test, predictions)
    
    hit_rate_10 = np.mean(np.abs(y_test - predictions) <= 10) * 100
    hit_rate_15 = np.mean(np.abs(y_test - predictions) <= 15) * 100
    hit_rate_20 = np.mean(np.abs(y_test - predictions) <= 20) * 100

    logger.info(f"测试集评估结果:")
    logger.info(f"  MAE: {mae:.4f}")
    logger.info(f"  RMSE: {rmse:.4f}")
    logger.info(f"  R2 Score: {r2:.4f}")
    logger.info(f"  命中率 (±10°C): {hit_rate_10:.2f}%")
    logger.info(f"  命中率 (±15°C): {hit_rate_15:.2f}%")
    logger.info(f"  命中率 (±20°C): {hit_rate_20:.2f}%")

    # 保存模型
    try:
        joblib.dump(model, model_path)
        logger.info(f"模型已保存到: {model_path}")
    except Exception as e:
        logger.error(f"保存模型失败: {e}")
        # 尝试使用pickle作为备选
        try:
            with open(model_path.replace('.pkl', '_pickle.pkl'), 'wb') as f_pickle:
                pickle.dump(model, f_pickle)
            logger.info(f"模型已使用pickle备选方案保存到: {model_path.replace('.pkl', '_pickle.pkl')}")
        except Exception as e_pickle:
            logger.error(f"使用pickle保存模型也失败: {e_pickle}")

    # 绘制预测结果 vs 实际结果
    plt.figure(figsize=(10, 6))
    plt.scatter(y_test, predictions, alpha=0.5)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
    plt.xlabel("实际钢水温度")
    plt.ylabel("预测钢水温度")
    plt.title(f"模型预测 vs 实际 (测试集) - {len(X.columns)} 特征")
    plot_path = os.path.join(output_dir, "training_results_v2.png")
    try:
        plt.savefig(plot_path)
        logger.info(f"训练结果图已保存到: {plot_path}")
    except Exception as e_plot:
        logger.error(f"保存训练结果图失败: {e_plot}")
    plt.close()

if __name__ == "__main__":
    main() 