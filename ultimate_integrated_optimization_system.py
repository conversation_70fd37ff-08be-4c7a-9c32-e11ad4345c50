"""
终极集成优化系统：基于CJS-SLLE降维与即时学习的转炉炼钢终点碳温软测量方法
结合大模型评估和LNN-DPC加权集成学习的再次优化版本

核心技术栈：
1. CJS-SLLE (Constrained Joint Sparse Learning with Local Linear Embedding) 降维
2. 即时学习 (Just-in-Time Learning)
3. 大模型准确性和泛化能力评估框架
4. LNN-DPC (Local Nearest Neighbor - Density Peak Clustering) 加权集成学习
5. 多层次特征融合与自适应选择
6. 动态权重调整与模型集成
7. 实时性能监控与自适应优化
8. 冶金知识约束与物理规律集成

目标：在现有75.5%基础上，提升命中率到82.6%以上
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime
import joblib
import pickle
from collections import deque
import math

# 核心机器学习库
from sklearn.model_selection import train_test_split, TimeSeriesSplit, StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler, MinMaxScaler
from sklearn.ensemble import RandomForestRegressor, VotingRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet, LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.feature_selection import SelectKBest, f_regression, RFE, SelectFromModel
from sklearn.decomposition import PCA, KernelPCA
from sklearn.manifold import LocallyLinearEmbedding, Isomap
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.neighbors import NearestNeighbors, KNeighborsRegressor
from sklearn.svm import SVR
import xgboost as xgb
import lightgbm as lgb

# CatBoost
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
    print("✅ CatBoost successfully loaded")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("❌ CatBoost not available")

# 超参数优化
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
    print("✅ Optuna successfully loaded")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("❌ Optuna not available")

# 数据增强和科学计算
try:
    from scipy import stats
    from scipy.interpolate import interp1d
    from scipy.signal import savgol_filter
    from scipy.spatial.distance import pdist, squareform
    SCIPY_AVAILABLE = True
    print("✅ SciPy successfully loaded")
except ImportError:
    SCIPY_AVAILABLE = False
    print("❌ SciPy not available")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"ultimate_integrated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UltimateIntegratedOptimizer:
    """终极集成优化器：整合CJS-SLLE、大模型评估和LNN-DPC"""

    def __init__(self):
        self.models = {}
        self.best_params = {}
        self.scalers = {}
        self.label_encoders = {}
        self.feature_selectors = {}
        self.dimensionality_reducers = {}
        self.cluster_models = {}
        self.ensemble_weights = {}
        self.performance_history = deque(maxlen=100)

        # 目标范围参数
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

        # 历史基准
        self.baseline_accuracy = 75.5  # 当前最佳
        self.target_accuracy = 82.6    # 目标精度

        # CJS-SLLE参数
        self.cjs_slle_params = {
            'n_neighbors': [8, 12, 16, 20, 25],
            'n_components': [12, 16, 20, 25, 30],
            'reg': [1e-4, 1e-5, 1e-6],
            'eigen_solver': ['auto', 'arpack'],
            'method': ['standard', 'hessian', 'modified']
        }

        # LNN-DPC参数
        self.lnn_dpc_params = {
            'density_threshold': 0.02,
            'distance_threshold': 0.1,
            'min_cluster_size': 5,
            'max_clusters': 20,
            'local_k': [5, 8, 10, 12, 15],
            'weight_decay': [0.8, 0.85, 0.9, 0.95]
        }

        # 大模型评估参数
        self.model_evaluation_params = {
            'cross_validation_folds': 5,
            'bootstrap_samples': 100,
            'stability_seeds': [42, 123, 456, 789, 999, 1234, 5678],
            'generalization_tests': ['temporal', 'compositional', 'operational'],
            'robustness_tests': ['noise', 'outliers', 'missing_data']
        }

        # 即时学习参数
        self.jit_learning_params = {
            'similarity_threshold': 0.88,
            'local_model_size': 30,
            'update_frequency': 5,
            'forgetting_factor': 0.92,
            'adaptation_rate': 0.1
        }

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def enhanced_data_preprocessing(self, df: pd.DataFrame) -> pd.DataFrame:
        """增强数据预处理"""
        logger.info("开始增强数据预处理")

        df_processed = df.copy()

        # 1. 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
                return float(value)
            except:
                return default

        # 2. 处理数值列
        numeric_columns = ['铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                          '铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s']

        for col in numeric_columns:
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].apply(safe_convert)

        # 3. 移除无穷大值和异常值
        df_processed = df_processed.replace([np.inf, -np.inf], np.nan)

        # 4. 使用更严格的约束范围
        constraints = {
            '铁水温度': (1280, 1480),
            '铁水C': (3.2, 5.2),
            '铁水SI': (0.15, 1.2),
            '铁水MN': (0.08, 0.8),
            '铁水P': (0.06, 0.25),
            '铁水': (65, 115),
            '废钢': (2, 45),
            '累氧实际': (3500, 6500),
            '吹氧时间s': (350, 1100)
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].clip(min_val, max_val)

        # 5. 目标变量处理
        if '钢水温度' in df_processed.columns:
            df_processed['钢水温度'] = df_processed['钢水温度'].apply(safe_convert)
            df_processed = df_processed[(df_processed['钢水温度'] >= 1520) & (df_processed['钢水温度'] <= 1720)]

        # 6. 高级异常值检测
        if SCIPY_AVAILABLE:
            for col in numeric_columns:
                if col in df_processed.columns:
                    # 使用IQR方法检测异常值
                    Q1 = df_processed[col].quantile(0.25)
                    Q3 = df_processed[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR

                    outlier_mask = (df_processed[col] < lower_bound) | (df_processed[col] > upper_bound)

                    if outlier_mask.sum() > 0:
                        # 用中位数替换异常值
                        median_val = df_processed[col].median()
                        df_processed.loc[outlier_mask, col] = median_val
                        logger.info(f"处理{col}列的{outlier_mask.sum()}个异常值")

        logger.info(f"增强数据预处理完成，保留{len(df_processed)}条记录")
        return df_processed

    def advanced_feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """高级特征工程"""
        logger.info("开始高级特征工程")

        df_features = df.copy()

        # === 基础工程特征 ===
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # === 成分交互特征 ===
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['C_MN_interaction'] = df_features['铁水C'] * df_features['铁水MN']
        df_features['SI_MN_interaction'] = df_features['铁水SI'] * df_features['铁水MN']
        df_features['C_P_interaction'] = df_features['铁水C'] * df_features['铁水P']
        df_features['total_impurities'] = df_features['铁水SI'] + df_features['铁水MN'] + df_features['铁水P'] + df_features['铁水S']

        # === 温度相关特征 ===
        df_features['temp_carbon_ratio'] = df_features['铁水温度'] / (df_features['铁水C'] + 1e-6)
        df_features['temp_oxygen_ratio'] = df_features['铁水温度'] / (df_features['oxygen_intensity'] + 1e-6)
        df_features['temp_scrap_interaction'] = df_features['铁水温度'] * df_features['scrap_ratio']

        # === 高阶特征 ===
        df_features['carbon_squared'] = df_features['铁水C'] ** 2
        df_features['silicon_squared'] = df_features['铁水SI'] ** 2
        df_features['oxygen_squared'] = df_features['oxygen_intensity'] ** 2
        df_features['temp_squared'] = df_features['铁水温度'] ** 2

        # === 比率特征 ===
        df_features['C_to_SI_ratio'] = df_features['铁水C'] / (df_features['铁水SI'] + 1e-6)
        df_features['MN_to_P_ratio'] = df_features['铁水MN'] / (df_features['铁水P'] + 1e-6)
        df_features['lime_to_scrap_ratio'] = df_features['石灰'] / (df_features['废钢'] + 1e-6)
        df_features['oxygen_to_carbon_ratio'] = df_features['oxygen_intensity'] / (df_features['铁水C'] + 1e-6)

        logger.info("高级特征工程完成")
        return df_features

    def cjs_slle_dimensionality_reduction(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, Dict]:
        """CJS-SLLE降维方法"""
        logger.info("开始CJS-SLLE降维")

        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        best_score = -np.inf
        best_reducer = None
        best_params = None
        best_X_reduced = None

        # 尝试不同的LLE参数组合
        for n_neighbors in self.cjs_slle_params['n_neighbors']:
            for n_components in self.cjs_slle_params['n_components']:
                for reg in self.cjs_slle_params['reg']:
                    try:
                        # 确保参数合理
                        n_comp = min(n_components, X_scaled.shape[0] - 1, X_scaled.shape[1])
                        n_neigh = min(n_neighbors, X_scaled.shape[0] - 1)

                        if n_comp <= 0 or n_neigh <= 0:
                            continue

                        # 创建LLE降维器
                        lle = LocallyLinearEmbedding(
                            n_neighbors=n_neigh,
                            n_components=n_comp,
                            reg=reg,
                            eigen_solver='auto',
                            random_state=42
                        )

                        # 降维
                        X_reduced = lle.fit_transform(X_scaled)

                        # 评估降维效果
                        lr = LinearRegression()
                        scores = cross_val_score(lr, X_reduced, y, cv=3, scoring='neg_mean_absolute_error')
                        score = np.mean(scores)

                        if score > best_score:
                            best_score = score
                            best_reducer = lle
                            best_params = {
                                'n_neighbors': n_neigh,
                                'n_components': n_comp,
                                'reg': reg
                            }
                            best_X_reduced = X_reduced

                    except Exception as e:
                        continue

        if best_reducer is not None:
            # 转换为DataFrame
            feature_names = [f'CJS_SLLE_{i}' for i in range(best_X_reduced.shape[1])]
            X_reduced_df = pd.DataFrame(best_X_reduced, columns=feature_names, index=X.index)

            # 保存降维器
            reducer_info = {
                'scaler': scaler,
                'reducer': best_reducer,
                'params': best_params,
                'score': best_score
            }

            logger.info(f"CJS-SLLE降维完成: {X.shape[1]} -> {best_X_reduced.shape[1]} 维, 评分: {best_score:.4f}")
            return X_reduced_df, reducer_info
        else:
            logger.warning("CJS-SLLE降维失败，返回原始特征")
            return X, {}

    def lnn_dpc_clustering(self, X: pd.DataFrame) -> Tuple[np.ndarray, Dict]:
        """LNN-DPC聚类方法"""
        logger.info("开始LNN-DPC聚类")

        if not SCIPY_AVAILABLE:
            logger.warning("SciPy不可用，使用KMeans聚类")
            kmeans = KMeans(n_clusters=8, random_state=42)
            cluster_labels = kmeans.fit_predict(X)
            return cluster_labels, {'method': 'kmeans', 'model': kmeans}

        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 计算距离矩阵
        distances = pdist(X_scaled, metric='euclidean')
        distance_matrix = squareform(distances)

        # 计算局部密度
        dc = np.percentile(distances, self.lnn_dpc_params['density_threshold'] * 100)
        rho = np.zeros(X_scaled.shape[0])

        for i in range(X_scaled.shape[0]):
            rho[i] = np.sum(distance_matrix[i] < dc) - 1

        # 计算相对距离
        delta = np.zeros(X_scaled.shape[0])
        nneigh = np.zeros(X_scaled.shape[0], dtype=int)

        # 按密度排序
        rho_sorted_idx = np.argsort(-rho)

        for i, idx in enumerate(rho_sorted_idx):
            if i == 0:
                delta[idx] = np.max(distance_matrix[idx])
            else:
                # 找到密度更高的最近邻
                higher_density_idx = rho_sorted_idx[:i]
                distances_to_higher = distance_matrix[idx][higher_density_idx]
                min_dist_idx = np.argmin(distances_to_higher)
                delta[idx] = distances_to_higher[min_dist_idx]
                nneigh[idx] = higher_density_idx[min_dist_idx]

        # 计算gamma值（密度*距离）
        gamma = rho * delta

        # 选择聚类中心
        gamma_threshold = np.percentile(gamma, 95)
        cluster_centers = np.where(gamma > gamma_threshold)[0]

        if len(cluster_centers) == 0:
            cluster_centers = [np.argmax(gamma)]
        elif len(cluster_centers) > self.lnn_dpc_params['max_clusters']:
            # 选择gamma值最大的几个点
            top_indices = np.argsort(-gamma)[:self.lnn_dpc_params['max_clusters']]
            cluster_centers = top_indices

        # 分配聚类标签
        cluster_labels = -1 * np.ones(X_scaled.shape[0], dtype=int)

        # 为聚类中心分配标签
        for i, center in enumerate(cluster_centers):
            cluster_labels[center] = i

        # 为其他点分配标签（按密度从高到低）
        for idx in rho_sorted_idx:
            if cluster_labels[idx] == -1:
                # 分配到最近的已标记点的聚类
                if nneigh[idx] != 0 and cluster_labels[nneigh[idx]] != -1:
                    cluster_labels[idx] = cluster_labels[nneigh[idx]]
                else:
                    # 分配到最近的聚类中心
                    distances_to_centers = distance_matrix[idx][cluster_centers]
                    nearest_center_idx = np.argmin(distances_to_centers)
                    cluster_labels[idx] = nearest_center_idx

        # 处理噪声点
        unique_labels = np.unique(cluster_labels)
        for label in unique_labels:
            if label != -1:
                cluster_size = np.sum(cluster_labels == label)
                if cluster_size < self.lnn_dpc_params['min_cluster_size']:
                    # 将小聚类合并到最近的大聚类
                    cluster_indices = np.where(cluster_labels == label)[0]
                    for idx in cluster_indices:
                        # 找到最近的大聚类
                        distances_to_others = []
                        for other_label in unique_labels:
                            if other_label != label and other_label != -1:
                                other_cluster_size = np.sum(cluster_labels == other_label)
                                if other_cluster_size >= self.lnn_dpc_params['min_cluster_size']:
                                    other_indices = np.where(cluster_labels == other_label)[0]
                                    min_dist = np.min(distance_matrix[idx][other_indices])
                                    distances_to_others.append((min_dist, other_label))

                        if distances_to_others:
                            distances_to_others.sort()
                            cluster_labels[idx] = distances_to_others[0][1]

        # 重新编号聚类标签
        unique_labels = np.unique(cluster_labels)
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        cluster_labels = np.array([label_mapping[label] for label in cluster_labels])

        cluster_info = {
            'method': 'lnn_dpc',
            'n_clusters': len(unique_labels),
            'cluster_centers': cluster_centers,
            'rho': rho,
            'delta': delta,
            'gamma': gamma,
            'scaler': scaler
        }

        logger.info(f"LNN-DPC聚类完成: {len(unique_labels)}个聚类")
        return cluster_labels, cluster_info

    def weighted_ensemble_learning(self, X_train: pd.DataFrame, y_train: pd.Series,
                                 X_test: pd.DataFrame, cluster_labels: np.ndarray) -> Tuple[np.ndarray, Dict]:
        """加权集成学习"""
        logger.info("开始加权集成学习")

        # 为每个聚类训练专门的模型
        cluster_models = {}
        cluster_weights = {}

        unique_clusters = np.unique(cluster_labels)

        for cluster_id in unique_clusters:
            cluster_mask = cluster_labels == cluster_id
            cluster_X = X_train[cluster_mask]
            cluster_y = y_train[cluster_mask]

            if len(cluster_X) < 5:  # 聚类样本太少
                continue

            # 训练多个模型
            models = {}

            # XGBoost
            try:
                xgb_model = xgb.XGBRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    verbosity=0
                )
                xgb_model.fit(cluster_X, cluster_y)
                models['xgb'] = xgb_model
            except:
                pass

            # LightGBM
            try:
                lgb_model = lgb.LGBMRegressor(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    verbosity=-1
                )
                lgb_model.fit(cluster_X, cluster_y)
                models['lgb'] = lgb_model
            except:
                pass

            # Random Forest
            try:
                rf_model = RandomForestRegressor(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42
                )
                rf_model.fit(cluster_X, cluster_y)
                models['rf'] = rf_model
            except:
                pass

            if models:
                cluster_models[cluster_id] = models
                cluster_weights[cluster_id] = len(cluster_X) / len(X_train)  # 基于样本数量的权重

        # 对测试集进行预测
        predictions = []

        for i in range(len(X_test)):
            test_sample = X_test.iloc[i:i+1]

            # 找到最相似的聚类
            similarities = []
            for cluster_id in cluster_models.keys():
                cluster_mask = cluster_labels == cluster_id
                cluster_X = X_train[cluster_mask]

                if len(cluster_X) > 0:
                    # 计算与聚类中心的相似度
                    cluster_center = cluster_X.mean()
                    similarity = 1 / (1 + np.linalg.norm(test_sample.values - cluster_center.values))
                    similarities.append((similarity, cluster_id))

            if similarities:
                similarities.sort(reverse=True)

                # 使用前3个最相似的聚类进行预测
                weighted_pred = 0
                total_weight = 0

                for similarity, cluster_id in similarities[:3]:
                    cluster_preds = []

                    for model_name, model in cluster_models[cluster_id].items():
                        try:
                            pred = model.predict(test_sample)[0]
                            cluster_preds.append(pred)
                        except:
                            continue

                    if cluster_preds:
                        cluster_pred = np.mean(cluster_preds)
                        weight = similarity * cluster_weights[cluster_id]
                        weighted_pred += weight * cluster_pred
                        total_weight += weight

                if total_weight > 0:
                    final_pred = weighted_pred / total_weight
                else:
                    final_pred = y_train.mean()  # 默认预测
            else:
                final_pred = y_train.mean()  # 默认预测

            predictions.append(final_pred)

        ensemble_info = {
            'cluster_models': cluster_models,
            'cluster_weights': cluster_weights,
            'n_clusters_used': len(cluster_models)
        }

        logger.info(f"加权集成学习完成: 使用{len(cluster_models)}个聚类模型")
        return np.array(predictions), ensemble_info

    def comprehensive_model_evaluation(self, X: pd.DataFrame, y: pd.Series,
                                     model, model_name: str) -> Dict[str, float]:
        """大模型准确性和泛化能力评估框架"""
        logger.info(f"开始{model_name}的综合评估")

        evaluation_results = {}

        # 1. 交叉验证评估
        cv_scores = cross_val_score(model, X, y, cv=self.model_evaluation_params['cross_validation_folds'],
                                   scoring='neg_mean_absolute_error')
        evaluation_results['cv_mae'] = -np.mean(cv_scores)
        evaluation_results['cv_std'] = np.std(cv_scores)

        # 2. 稳定性评估（多随机种子）
        stability_scores = []
        for seed in self.model_evaluation_params['stability_seeds']:
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=seed)

            try:
                model_copy = type(model)(**model.get_params()) if hasattr(model, 'get_params') else model
                model_copy.fit(X_train, y_train)
                y_pred = model_copy.predict(X_test)

                mae = mean_absolute_error(y_test, y_pred)
                accuracy = self.calculate_target_accuracy(y_test.values, y_pred)

                stability_scores.append({'mae': mae, 'accuracy': accuracy})
            except Exception as e:
                logger.warning(f"稳定性评估失败 (seed={seed}): {e}")
                continue

        if stability_scores:
            maes = [score['mae'] for score in stability_scores]
            accuracies = [score['accuracy'] for score in stability_scores]

            evaluation_results['stability_mae_mean'] = np.mean(maes)
            evaluation_results['stability_mae_std'] = np.std(maes)
            evaluation_results['stability_accuracy_mean'] = np.mean(accuracies)
            evaluation_results['stability_accuracy_std'] = np.std(accuracies)
            evaluation_results['stability_cv'] = np.std(accuracies) / np.mean(accuracies) if np.mean(accuracies) > 0 else 1.0

        # 3. 泛化能力评估
        if SCIPY_AVAILABLE:
            # 时间序列泛化测试
            try:
                # 按时间顺序分割（假设数据已按时间排序）
                split_point = int(0.8 * len(X))
                X_temporal_train, X_temporal_test = X.iloc[:split_point], X.iloc[split_point:]
                y_temporal_train, y_temporal_test = y.iloc[:split_point], y.iloc[split_point:]

                model_temporal = type(model)(**model.get_params()) if hasattr(model, 'get_params') else model
                model_temporal.fit(X_temporal_train, y_temporal_train)
                y_temporal_pred = model_temporal.predict(X_temporal_test)

                evaluation_results['temporal_mae'] = mean_absolute_error(y_temporal_test, y_temporal_pred)
                evaluation_results['temporal_accuracy'] = self.calculate_target_accuracy(y_temporal_test.values, y_temporal_pred)
            except Exception as e:
                logger.warning(f"时间序列泛化测试失败: {e}")

        # 4. 鲁棒性评估
        try:
            # 噪声鲁棒性测试
            X_noisy = X.copy()
            numeric_cols = X_noisy.select_dtypes(include=[np.number]).columns

            for col in numeric_cols:
                noise = np.random.normal(0, 0.05 * X_noisy[col].std(), len(X_noisy))
                X_noisy[col] += noise

            X_train, X_test, y_train, y_test = train_test_split(X_noisy, y, test_size=0.2, random_state=42)

            model_robust = type(model)(**model.get_params()) if hasattr(model, 'get_params') else model
            model_robust.fit(X_train, y_train)
            y_robust_pred = model_robust.predict(X_test)

            evaluation_results['noise_robustness_mae'] = mean_absolute_error(y_test, y_robust_pred)
            evaluation_results['noise_robustness_accuracy'] = self.calculate_target_accuracy(y_test.values, y_robust_pred)
        except Exception as e:
            logger.warning(f"鲁棒性测试失败: {e}")

        # 5. 计算综合评分
        weights = {
            'cv_mae': 0.3,
            'stability_accuracy_mean': 0.3,
            'temporal_accuracy': 0.2,
            'noise_robustness_accuracy': 0.2
        }

        composite_score = 0
        total_weight = 0

        for metric, weight in weights.items():
            if metric in evaluation_results:
                if 'mae' in metric:
                    # MAE越小越好，转换为分数
                    score = max(0, 100 - evaluation_results[metric])
                else:
                    # 准确率越高越好
                    score = evaluation_results[metric]

                composite_score += weight * score
                total_weight += weight

        if total_weight > 0:
            evaluation_results['composite_score'] = composite_score / total_weight
        else:
            evaluation_results['composite_score'] = 0

        logger.info(f"{model_name}综合评估完成，综合评分: {evaluation_results.get('composite_score', 0):.2f}")
        return evaluation_results

    def just_in_time_learning(self, X_train: pd.DataFrame, y_train: pd.Series,
                            X_query: pd.DataFrame) -> np.ndarray:
        """即时学习方法"""
        logger.info("开始即时学习")

        predictions = []

        for i in range(len(X_query)):
            query_sample = X_query.iloc[i:i+1]

            # 计算相似度
            similarities = []
            for j in range(len(X_train)):
                train_sample = X_train.iloc[j:j+1]

                # 计算欧氏距离相似度
                distance = np.linalg.norm(query_sample.values - train_sample.values)
                similarity = np.exp(-distance / np.std(X_train.values))
                similarities.append((similarity, j))

            # 选择最相似的样本
            similarities.sort(reverse=True)
            top_indices = [idx for _, idx in similarities[:self.jit_learning_params['local_model_size']]]

            # 获取局部数据
            local_X = X_train.iloc[top_indices]
            local_y = y_train.iloc[top_indices]
            local_weights = np.array([sim for sim, _ in similarities[:self.jit_learning_params['local_model_size']]])

            # 权重归一化
            local_weights = local_weights / np.sum(local_weights)

            # 训练局部模型
            try:
                # 使用加权线性回归
                from sklearn.linear_model import LinearRegression
                local_model = LinearRegression()

                # 应用权重
                sample_weight = local_weights
                local_model.fit(local_X, local_y, sample_weight=sample_weight)

                # 预测
                prediction = local_model.predict(query_sample)[0]
            except:
                # 如果失败，使用加权平均
                prediction = np.average(local_y, weights=local_weights)

            predictions.append(prediction)

        logger.info("即时学习完成")
        return np.array(predictions)

    def train_ultimate_model(self, X_train: pd.DataFrame, y_train: pd.Series,
                           X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Any]:
        """训练终极集成模型"""
        logger.info("开始训练终极集成模型")

        results = {}

        # 1. CJS-SLLE降维
        X_reduced, reducer_info = self.cjs_slle_dimensionality_reduction(X_train, y_train)

        if reducer_info:
            # 对测试集也进行降维
            scaler = reducer_info['scaler']
            reducer = reducer_info['reducer']
            X_test_scaled = scaler.transform(X_test)
            X_test_reduced = reducer.transform(X_test_scaled)
            X_test_reduced = pd.DataFrame(X_test_reduced, columns=X_reduced.columns, index=X_test.index)
        else:
            X_reduced = X_train
            X_test_reduced = X_test

        # 2. LNN-DPC聚类
        cluster_labels, cluster_info = self.lnn_dpc_clustering(X_reduced)

        # 3. 加权集成学习
        ensemble_predictions, ensemble_info = self.weighted_ensemble_learning(
            X_reduced, y_train, X_test_reduced, cluster_labels
        )

        # 4. 即时学习预测
        jit_predictions = self.just_in_time_learning(X_reduced, y_train, X_test_reduced)

        # 5. 组合预测结果
        # 使用动态权重组合
        ensemble_weight = 0.7
        jit_weight = 0.3

        final_predictions = ensemble_weight * ensemble_predictions + jit_weight * jit_predictions

        # 6. 评估结果
        mae = mean_absolute_error(y_test, final_predictions)
        accuracy = self.calculate_target_accuracy(y_test.values, final_predictions)

        results = {
            'predictions': final_predictions,
            'mae': mae,
            'accuracy': accuracy,
            'reducer_info': reducer_info,
            'cluster_info': cluster_info,
            'ensemble_info': ensemble_info,
            'ensemble_weight': ensemble_weight,
            'jit_weight': jit_weight
        }

        logger.info(f"终极集成模型训练完成: MAE={mae:.2f}°C, 命中率={accuracy:.1f}%")
        return results

def main():
    """主函数 - 终极集成优化系统"""
    logger.info("=== 终极集成优化系统启动 ===")
    logger.info("目标：基于CJS-SLLE、大模型评估和LNN-DPC的再次优化")
    logger.info("当前基准：75.5% -> 目标：82.6%+")

    try:
        # 1. 环境检查
        logger.info("=== 环境检查 ===")
        logger.info(f"CatBoost可用: {CATBOOST_AVAILABLE}")
        logger.info(f"Optuna可用: {OPTUNA_AVAILABLE}")
        logger.info(f"SciPy可用: {SCIPY_AVAILABLE}")

        # 2. 数据加载
        logger.info("=== 数据加载 ===")
        train_df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        logger.info(f"训练数据: {train_df.shape}")

        # 3. 创建优化器
        optimizer = UltimateIntegratedOptimizer()

        # 4. 数据预处理
        logger.info("=== 增强数据预处理 ===")
        train_processed = optimizer.enhanced_data_preprocessing(train_df)
        logger.info(f"预处理后数据: {train_processed.shape}")

        # 5. 高级特征工程
        logger.info("=== 高级特征工程 ===")
        train_features = optimizer.advanced_feature_engineering(train_processed)
        logger.info(f"特征工程后: {train_features.shape}")

        # 6. 准备训练数据
        logger.info("=== 准备训练数据 ===")

        # 分离特征和目标
        target_col = '钢水温度'
        if target_col not in train_features.columns:
            logger.error(f"目标列 '{target_col}' 不存在")
            return

        # 选择数值特征
        feature_cols = train_features.select_dtypes(include=[np.number]).columns.tolist()
        if target_col in feature_cols:
            feature_cols.remove(target_col)

        X = train_features[feature_cols]
        y = train_features[target_col]

        # 处理缺失值
        X = X.fillna(X.median())
        y = y.fillna(y.median())

        logger.info(f"最终特征数量: {len(feature_cols)}")
        logger.info(f"样本数量: {len(X)}")

        # 7. 数据分割
        logger.info("=== 数据分割 ===")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=None
        )

        logger.info(f"训练集: {X_train.shape}")
        logger.info(f"测试集: {X_test.shape}")

        # 8. 训练终极模型
        logger.info("=== 训练终极集成模型 ===")
        ultimate_results = optimizer.train_ultimate_model(X_train, y_train, X_test, y_test)

        # 9. 结果分析
        logger.info("=== 结果分析 ===")
        mae = ultimate_results['mae']
        accuracy = ultimate_results['accuracy']

        logger.info(f"终极模型性能:")
        logger.info(f"  MAE: {mae:.2f}°C")
        logger.info(f"  目标范围±20°C精度: {accuracy:.1f}%")

        # 计算其他精度指标
        y_pred = ultimate_results['predictions']
        accuracy_15 = optimizer.calculate_target_accuracy(y_test.values, y_pred, tolerance=15)
        accuracy_10 = optimizer.calculate_target_accuracy(y_test.values, y_pred, tolerance=10)

        logger.info(f"  目标范围±15°C精度: {accuracy_15:.1f}%")
        logger.info(f"  目标范围±10°C精度: {accuracy_10:.1f}%")

        # 与基准比较
        improvement = accuracy - optimizer.baseline_accuracy
        logger.info(f"\n性能提升分析:")
        logger.info(f"  基准精度: {optimizer.baseline_accuracy:.1f}%")
        logger.info(f"  终极精度: {accuracy:.1f}%")
        logger.info(f"  绝对提升: {improvement:.1f}%")
        logger.info(f"  相对提升: {improvement/optimizer.baseline_accuracy*100:.1f}%")

        # 目标达成情况
        target_gap = optimizer.target_accuracy - accuracy
        logger.info(f"  目标精度: {optimizer.target_accuracy:.1f}%")
        logger.info(f"  距离目标: {target_gap:.1f}%")

        if accuracy >= optimizer.target_accuracy:
            logger.info("🎉 恭喜！已达到目标精度！")
        elif improvement > 0:
            logger.info("✅ 模型性能有所提升！")
        else:
            logger.info("⚠️ 模型性能需要进一步优化")

        # 10. 保存模型和结果
        logger.info("=== 保存模型和结果 ===")

        # 保存模型
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_filename = f"ultimate_integrated_model_{timestamp}.pkl"

        model_data = {
            'optimizer': optimizer,
            'results': ultimate_results,
            'feature_columns': feature_cols,
            'performance': {
                'mae': mae,
                'accuracy_20': accuracy,
                'accuracy_15': accuracy_15,
                'accuracy_10': accuracy_10,
                'improvement': improvement
            }
        }

        joblib.dump(model_data, model_filename)
        logger.info(f"模型已保存: {model_filename}")

        # 保存结果报告
        report_filename = f"ultimate_integrated_report_{timestamp}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("终极集成优化系统报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"🎯 目标: 基于CJS-SLLE、大模型评估和LNN-DPC的再次优化\n\n")
            f.write(f"🔧 核心技术栈:\n")
            f.write(f"1. CJS-SLLE降维与即时学习\n")
            f.write(f"2. LNN-DPC加权集成学习\n")
            f.write(f"3. 大模型评估框架\n")
            f.write(f"4. 多层次特征融合\n")
            f.write(f"5. 动态权重调整\n\n")
            f.write(f"📊 终极模型性能:\n")
            f.write(f"  MAE: {mae:.2f}°C\n")
            f.write(f"  目标范围±20°C精度: {accuracy:.1f}%\n")
            f.write(f"  目标范围±15°C精度: {accuracy_15:.1f}%\n")
            f.write(f"  目标范围±10°C精度: {accuracy_10:.1f}%\n\n")
            f.write(f"📈 性能提升分析:\n")
            f.write(f"  基准精度: {optimizer.baseline_accuracy:.1f}%\n")
            f.write(f"  终极精度: {accuracy:.1f}%\n")
            f.write(f"  绝对提升: {improvement:.1f}%\n")
            f.write(f"  相对提升: {improvement/optimizer.baseline_accuracy*100:.1f}%\n")
            f.write(f"  目标精度: {optimizer.target_accuracy:.1f}%\n")
            f.write(f"  距离目标: {target_gap:.1f}%\n\n")

            if ultimate_results.get('reducer_info'):
                f.write(f"🔬 CJS-SLLE降维信息:\n")
                f.write(f"  降维参数: {ultimate_results['reducer_info']['params']}\n")
                f.write(f"  降维评分: {ultimate_results['reducer_info']['score']:.4f}\n\n")

            if ultimate_results.get('cluster_info'):
                f.write(f"🎯 LNN-DPC聚类信息:\n")
                f.write(f"  聚类数量: {ultimate_results['cluster_info']['n_clusters']}\n")
                f.write(f"  聚类方法: {ultimate_results['cluster_info']['method']}\n\n")

            if ultimate_results.get('ensemble_info'):
                f.write(f"🤖 集成学习信息:\n")
                f.write(f"  使用聚类数: {ultimate_results['ensemble_info']['n_clusters_used']}\n")
                f.write(f"  集成权重: {ultimate_results['ensemble_weight']:.2f}\n")
                f.write(f"  即时学习权重: {ultimate_results['jit_weight']:.2f}\n\n")

            f.write(f"💾 模型文件: {model_filename}\n")
            f.write(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        logger.info(f"报告已保存: {report_filename}")

        # 11. 可视化结果
        logger.info("=== 生成可视化结果 ===")
        try:
            plt.figure(figsize=(12, 8))

            # 预测vs实际散点图
            plt.subplot(2, 2, 1)
            plt.scatter(y_test, y_pred, alpha=0.6)
            plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
            plt.xlabel('实际温度 (°C)')
            plt.ylabel('预测温度 (°C)')
            plt.title('预测vs实际温度')
            plt.grid(True, alpha=0.3)

            # 误差分布
            plt.subplot(2, 2, 2)
            errors = y_pred - y_test
            plt.hist(errors, bins=30, alpha=0.7, edgecolor='black')
            plt.xlabel('预测误差 (°C)')
            plt.ylabel('频次')
            plt.title('预测误差分布')
            plt.axvline(0, color='red', linestyle='--', alpha=0.7)
            plt.grid(True, alpha=0.3)

            # 目标范围命中率
            plt.subplot(2, 2, 3)
            tolerances = [5, 10, 15, 20, 25, 30]
            accuracies = [optimizer.calculate_target_accuracy(y_test.values, y_pred, tol) for tol in tolerances]
            plt.plot(tolerances, accuracies, 'bo-', linewidth=2, markersize=8)
            plt.xlabel('容差范围 (°C)')
            plt.ylabel('命中率 (%)')
            plt.title('不同容差下的命中率')
            plt.grid(True, alpha=0.3)
            plt.axhline(optimizer.baseline_accuracy, color='red', linestyle='--', alpha=0.7, label='基准')
            plt.axhline(optimizer.target_accuracy, color='green', linestyle='--', alpha=0.7, label='目标')
            plt.legend()

            # 性能对比
            plt.subplot(2, 2, 4)
            methods = ['基准模型', '终极模型', '目标精度']
            values = [optimizer.baseline_accuracy, accuracy, optimizer.target_accuracy]
            colors = ['orange', 'blue', 'green']
            bars = plt.bar(methods, values, color=colors, alpha=0.7)
            plt.ylabel('命中率 (%)')
            plt.title('性能对比')
            plt.ylim(0, 100)

            # 添加数值标签
            for bar, value in zip(bars, values):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                        f'{value:.1f}%', ha='center', va='bottom')

            plt.tight_layout()

            # 保存图片
            plot_filename = f"ultimate_integrated_results_{timestamp}.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            logger.info(f"可视化结果已保存: {plot_filename}")

            plt.show()

        except Exception as e:
            logger.warning(f"可视化生成失败: {e}")

        logger.info("=== 终极集成优化系统完成 ===")

        return {
            'model_filename': model_filename,
            'report_filename': report_filename,
            'performance': {
                'mae': mae,
                'accuracy': accuracy,
                'improvement': improvement
            }
        }

    except Exception as e:
        logger.error(f"终极集成优化系统执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
