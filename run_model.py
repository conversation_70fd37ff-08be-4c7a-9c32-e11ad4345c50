"""
<PERSON><PERSON><PERSON> to run the steel temperature prediction model.
"""

import os
import sys
import logging

# Add the project directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main function
from steel_temp_prediction.main import main

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("steel_temp_prediction.log"),
            logging.StreamHandler()
        ]
    )
    
    # Run the main function
    main()
