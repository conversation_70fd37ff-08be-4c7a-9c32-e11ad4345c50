"""
使用训练好的最佳SVR模型和顺序思维集成模型对第五批测试数据进行预测
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch5_prediction_trained_models.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TrainedModelPredictor:
    """使用训练好的模型进行预测"""

    def __init__(self, models_dir: str = "results_with_slag"):
        self.models_dir = models_dir
        self.models = {}
        self.scaler = None
        self.label_encoder = None
        self.feature_names = []

        # 炉渣特征工程参数（与训练时保持一致）
        self.oxidation_coeffs = {
            'Si_to_SiO2': 2.14, 'Mn_to_MnO': 1.29,
            'P_to_P2O5': 2.29, 'Fe_to_FeO': 1.29
        }

        self.flux_compositions = {
            'lime': {'CaO': 0.88, 'MgO': 0.02, 'SiO2': 0.03},
            'dolomite': {'CaO': 0.32, 'MgO': 0.20, 'SiO2': 0.02},
            'limestone': {'CaO': 0.52, 'MgO': 0.02, 'SiO2': 0.04}
        }

        self.reaction_heats = {
            'C_to_CO': 10100, 'C_to_CO2': 32800,
            'Si_oxidation': 30800, 'Mn_oxidation': 7200,
            'P_oxidation': 24000, 'Fe_oxidation': 4800
        }

        self.steel_type_groups = {
            '低碳钢': ['Q235', 'Q345', 'Q355', 'Q245', 'Q420', 'Q460', 'A', 'B', 'C', 'D'],
            '中碳钢': ['20Mn2A', '35', '45', '50', '55', '60'],
            '高碳钢': ['65Mn', '70', '75', '80', '85', 'C72DA'],
            '合金钢': ['16Mn', '20CrMo', '40Cr', 'ER50', 'ER70', 'H08A', 'H08MnA']
        }

    def load_models_and_preprocessors(self):
        """加载训练好的模型和预处理器"""
        logger.info("加载训练好的模型和预处理器")

        # 加载SVR模型
        svr_path = os.path.join(self.models_dir, "svr_model.pkl")
        if os.path.exists(svr_path):
            with open(svr_path, 'rb') as f:
                self.models['SVR'] = pickle.load(f)
            logger.info("SVR模型加载成功")

        # 加载顺序思维集成模型
        sequential_path = os.path.join(self.models_dir, "sequential_thinking_model.pkl")
        if os.path.exists(sequential_path):
            with open(sequential_path, 'rb') as f:
                self.models['Sequential_Thinking'] = pickle.load(f)
            logger.info("顺序思维集成模型加载成功")

        # 加载预处理器
        scaler_path = os.path.join(self.models_dir, "scaler.pkl")
        with open(scaler_path, 'rb') as f:
            self.scaler = pickle.load(f)

        encoder_path = os.path.join(self.models_dir, "label_encoder.pkl")
        with open(encoder_path, 'rb') as f:
            self.label_encoder = pickle.load(f)

        # 加载特征名称
        features_path = os.path.join(self.models_dir, "feature_names.pkl")
        with open(features_path, 'rb') as f:
            self.feature_names = pickle.load(f)

        logger.info(f"加载完成，共{len(self.models)}个模型，{len(self.feature_names)}个特征")

    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default

    def preprocess_batch5_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理第五批数据，使其与训练数据格式一致"""
        logger.info("开始预处理第五批数据")

        # 统一列名
        column_mapping = {
            '炉子最大倾角': '最大角度',
            '平均流速M3/h': '气体流量流速平均',
            '最小流速': '最低流速',
            '最后2分钟加料': '最后2分钟',
            '总氧化碳(kg)': '气体总C'
        }

        df_processed = df.copy()
        for old_col, new_col in column_mapping.items():
            if old_col in df_processed.columns:
                df_processed.rename(columns={old_col: new_col}, inplace=True)
                logger.info(f"列名重命名: {old_col} -> {new_col}")

        # 数据清理（与训练时保持一致）
        # 处理铁水C异常值
        if '铁水C' in df_processed.columns:
            c_mask = df_processed['铁水C'] > 5.0
            df_processed.loc[c_mask, '铁水C'] = np.clip(df_processed.loc[c_mask, '铁水C'], 3.5, 5.0)

        # 填充缺失值
        numeric_columns = df_processed.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if df_processed[col].isnull().sum() > 0:
                # 使用训练数据的中位数填充
                default_values = {
                    '铁水': 90.0, '废钢': 20.0, '铁水C': 3.72, '铁水SI': 0.38,
                    '铁水S': 0.03, '铁水P': 0.12, '铁水MN': 0.17, '铁水温度': 1367.27,
                    '吹氧时间s': 800.0, '累氧实际': 4800.0, '石灰': 4607.0, '白云石': 896.5
                }
                fill_value = default_values.get(col, df_processed[col].median())
                df_processed[col].fillna(fill_value, inplace=True)

        logger.info(f"数据预处理完成，数据形状: {df_processed.shape}")
        return df_processed

    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建与训练时相同的特征"""
        logger.info("开始创建特征")

        df_features = df.copy()

        # 1. 钢种分组
        df_features['钢种分组'] = '其他'
        for group_name, steel_types in self.steel_type_groups.items():
            for steel_type in steel_types:
                mask = df_features['钢种'].str.contains(steel_type, na=False)
                df_features.loc[mask, '钢种分组'] = group_name

        # 2. 基础特征
        # 确保数值类型
        df_features['累氧实际'] = pd.to_numeric(df_features['累氧实际'], errors='coerce').fillna(4800)
        df_features['吹氧时间s'] = pd.to_numeric(df_features['吹氧时间s'], errors='coerce').fillna(800)
        df_features['废钢'] = pd.to_numeric(df_features['废钢'], errors='coerce').fillna(0)
        df_features['铁水'] = pd.to_numeric(df_features['铁水'], errors='coerce').fillna(90)

        # 氧气强度
        df_features['氧气强度'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)

        # 造渣料特征
        slag_materials = ['石灰', '白云石', '破碎料', '烧结返矿', '石灰石']
        available_materials = [col for col in slag_materials if col in df_features.columns]

        if available_materials:
            for material in available_materials:
                df_features[material] = pd.to_numeric(df_features[material], errors='coerce').fillna(0)

            df_features['总造渣料'] = df_features[available_materials].sum(axis=1)
            df_features['造渣料比'] = df_features['总造渣料'] / (df_features['铁水'] + 1e-6)

            for material in available_materials:
                df_features[f'{material}_比例'] = df_features[material] / (df_features['总造渣料'] + 1e-6)

        # 废钢比例
        df_features['废钢比'] = df_features['废钢'] / (df_features['铁水'] + 1e-6)

        # 单位铁水供氧量
        df_features['单位铁水供氧量'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)

        # 吹氧强度
        df_features['吹氧强度'] = df_features['累氧实际'] / (df_features['吹氧时间s'] + 1e-6) * 60

        # 3. 炉渣特征
        df_features = self.create_slag_features(df_features)

        # 4. 热平衡特征
        df_features = self.create_thermal_features(df_features)

        # 5. 交互特征
        df_features = self.create_interaction_features(df_features)

        logger.info(f"特征创建完成，最终特征数: {df_features.shape[1]}")
        return df_features

    def create_slag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建炉渣特征"""
        logger.info("创建炉渣特征")

        # 初始化炉渣特征列
        slag_features = [
            'slag_CaO_percent', 'slag_SiO2_percent', 'slag_FeO_percent',
            'slag_MgO_percent', 'slag_MnO_percent', 'slag_P2O5_percent',
            'slag_basicity', 'slag_rate', 'slag_iron_oxides_percent'
        ]

        for feature in slag_features:
            df[feature] = 0.0

        for idx, row in df.iterrows():
            try:
                hot_metal_mass = self.safe_convert(row['铁水'], 90) * 1000

                # 氧化产物计算
                si_oxidized = hot_metal_mass * self.safe_convert(row['铁水SI'], 0.4) * 0.95 / 100
                sio2_from_si = si_oxidized * self.oxidation_coeffs['Si_to_SiO2']

                mn_oxidized = hot_metal_mass * self.safe_convert(row['铁水MN'], 0.17) * 0.80 / 100
                mno_from_mn = mn_oxidized * self.oxidation_coeffs['Mn_to_MnO']

                p_oxidized = hot_metal_mass * self.safe_convert(row['铁水P'], 0.13) * 0.85 / 100
                p2o5_from_p = p_oxidized * self.oxidation_coeffs['P_to_P2O5']

                fe_loss = hot_metal_mass * 0.015
                feo_from_fe = fe_loss * self.oxidation_coeffs['Fe_to_FeO']

                # 造渣材料贡献
                lime_mass = self.safe_convert(row.get('石灰', 0))
                dolomite_mass = self.safe_convert(row.get('白云石', 0))
                limestone_mass = self.safe_convert(row.get('石灰石', 0))

                # CaO计算
                total_cao = (lime_mass * self.flux_compositions['lime']['CaO'] +
                           dolomite_mass * self.flux_compositions['dolomite']['CaO'] +
                           limestone_mass * self.flux_compositions['limestone']['CaO'])

                # MgO计算
                total_mgo = (lime_mass * self.flux_compositions['lime']['MgO'] +
                           dolomite_mass * self.flux_compositions['dolomite']['MgO'] +
                           limestone_mass * self.flux_compositions['limestone']['MgO'] +
                           hot_metal_mass * 0.15 / 1000)

                # SiO2计算
                total_sio2 = (sio2_from_si +
                            lime_mass * self.flux_compositions['lime']['SiO2'] +
                            dolomite_mass * self.flux_compositions['dolomite']['SiO2'] +
                            limestone_mass * self.flux_compositions['limestone']['SiO2'] +
                            hot_metal_mass * 0.08 / 1000)

                # 总炉渣量
                total_slag = total_cao + total_sio2 + feo_from_fe + total_mgo + mno_from_mn + p2o5_from_p

                if total_slag > 0:
                    df.loc[idx, 'slag_CaO_percent'] = total_cao / total_slag * 100
                    df.loc[idx, 'slag_SiO2_percent'] = total_sio2 / total_slag * 100
                    df.loc[idx, 'slag_FeO_percent'] = feo_from_fe / total_slag * 100
                    df.loc[idx, 'slag_MgO_percent'] = total_mgo / total_slag * 100
                    df.loc[idx, 'slag_MnO_percent'] = mno_from_mn / total_slag * 100
                    df.loc[idx, 'slag_P2O5_percent'] = p2o5_from_p / total_slag * 100
                    df.loc[idx, 'slag_basicity'] = total_cao / total_sio2 if total_sio2 > 0 else 2.8
                    df.loc[idx, 'slag_rate'] = total_slag / hot_metal_mass * 100
                    df.loc[idx, 'slag_iron_oxides_percent'] = feo_from_fe / total_slag * 100
                else:
                    # 默认值
                    df.loc[idx, 'slag_CaO_percent'] = 45.0
                    df.loc[idx, 'slag_SiO2_percent'] = 16.0
                    df.loc[idx, 'slag_FeO_percent'] = 20.0
                    df.loc[idx, 'slag_MgO_percent'] = 10.0
                    df.loc[idx, 'slag_MnO_percent'] = 7.0
                    df.loc[idx, 'slag_P2O5_percent'] = 2.0
                    df.loc[idx, 'slag_basicity'] = 2.8
                    df.loc[idx, 'slag_rate'] = 8.0
                    df.loc[idx, 'slag_iron_oxides_percent'] = 20.0

            except Exception as e:
                logger.warning(f"计算第{idx}行炉渣特征时出错: {e}")
                continue

        return df

    def create_thermal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建热平衡特征"""
        logger.info("创建热平衡特征")

        thermal_features = [
            'hot_metal_sensible_heat', 'scrap_heating_heat', 'total_oxidation_heat',
            'decarb_heat', 'si_oxidation_heat', 'mn_oxidation_heat', 'p_oxidation_heat',
            'net_heat_balance', 'theoretical_temp_rise', 'theoretical_end_temp'
        ]

        for feature in thermal_features:
            df[feature] = 0.0

        for idx, row in df.iterrows():
            try:
                hot_metal_mass = self.safe_convert(row['铁水'], 90)
                scrap_mass = self.safe_convert(row.get('废钢', 0), 20)
                hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)

                # 铁水显热
                cp_iron = 0.75
                df.loc[idx, 'hot_metal_sensible_heat'] = (
                    cp_iron * hot_metal_mass * 1000 * (hot_metal_temp - 1500)
                )

                # 废钢加热热量
                cp_steel = 0.65
                melting_heat = 1200
                df.loc[idx, 'scrap_heating_heat'] = (
                    scrap_mass * 1000 * (cp_steel * (1600 - 25) + melting_heat)
                )

                # 氧化反应热
                c_oxidized = hot_metal_mass * self.safe_convert(row['铁水C'], 4.2) * 0.88 / 100
                si_oxidized = hot_metal_mass * self.safe_convert(row['铁水SI'], 0.4) * 0.95 / 100
                mn_oxidized = hot_metal_mass * self.safe_convert(row['铁水MN'], 0.17) * 0.80 / 100
                p_oxidized = hot_metal_mass * self.safe_convert(row['铁水P'], 0.13) * 0.85 / 100

                co_ratio = 0.7
                co2_ratio = 0.3

                decarb_heat = (c_oxidized * co_ratio * self.reaction_heats['C_to_CO'] +
                              c_oxidized * co2_ratio * self.reaction_heats['C_to_CO2'])

                si_heat = si_oxidized * self.reaction_heats['Si_oxidation']
                mn_heat = mn_oxidized * self.reaction_heats['Mn_oxidation']
                p_heat = p_oxidized * self.reaction_heats['P_oxidation']

                df.loc[idx, 'total_oxidation_heat'] = decarb_heat + si_heat + mn_heat + p_heat
                df.loc[idx, 'decarb_heat'] = decarb_heat
                df.loc[idx, 'si_oxidation_heat'] = si_heat
                df.loc[idx, 'mn_oxidation_heat'] = mn_heat
                df.loc[idx, 'p_oxidation_heat'] = p_heat

                # 净热量平衡
                df.loc[idx, 'net_heat_balance'] = (
                    df.loc[idx, 'total_oxidation_heat'] - df.loc[idx, 'scrap_heating_heat']
                )

                # 理论温升
                total_steel = (hot_metal_mass + scrap_mass) * 1000
                if total_steel > 0:
                    df.loc[idx, 'theoretical_temp_rise'] = (
                        df.loc[idx, 'net_heat_balance'] / (total_steel * cp_iron)
                    )
                else:
                    df.loc[idx, 'theoretical_temp_rise'] = 0

                # 理论终点温度
                df.loc[idx, 'theoretical_end_temp'] = (
                    hot_metal_temp + df.loc[idx, 'theoretical_temp_rise']
                )

            except Exception as e:
                logger.warning(f"计算第{idx}行热平衡特征时出错: {e}")
                continue

        return df

    def create_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建交互特征"""
        logger.info("创建交互特征")

        interaction_pairs = [
            ('铁水C', '单位铁水供氧量'),
            ('铁水SI', 'slag_basicity'),
            ('废钢比', '铁水温度'),
            ('氧气强度', '总造渣料'),
            ('slag_FeO_percent', 'theoretical_temp_rise'),
            ('slag_CaO_percent', 'slag_SiO2_percent')
        ]

        for feature1, feature2 in interaction_pairs:
            if feature1 in df.columns and feature2 in df.columns:
                # 乘积交互
                df[f'{feature1}_x_{feature2}'] = df[feature1] * df[feature2]
                # 比值交互
                df[f'{feature1}_div_{feature2}'] = df[feature1] / (df[feature2] + 1e-6)

        return df

    def prepare_features_for_prediction(self, df: pd.DataFrame) -> np.ndarray:
        """准备用于预测的特征"""
        logger.info("准备预测特征")

        # 选择训练时使用的特征
        feature_cols = [col for col in self.feature_names if col in df.columns]
        missing_features = [col for col in self.feature_names if col not in df.columns]

        if missing_features:
            logger.warning(f"缺失特征: {missing_features}")
            # 为缺失特征添加默认值
            for col in missing_features:
                df[col] = 0.0

        # 按训练时的特征顺序选择
        X = df[self.feature_names].copy()

        # 处理分类特征
        categorical_cols = X.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            if col in X.columns:
                try:
                    X[col] = self.label_encoder.transform(X[col].astype(str))
                except:
                    # 如果遇到未见过的类别，使用最频繁的类别
                    X[col] = 0

        # 处理无穷大和NaN值
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(X.median())

        # 特征缩放
        X_scaled = self.scaler.transform(X)

        logger.info(f"特征准备完成，特征数: {X_scaled.shape[1]}, 样本数: {X_scaled.shape[0]}")
        return X_scaled

    def predict_with_models(self, X: np.ndarray) -> Dict[str, np.ndarray]:
        """使用加载的模型进行预测"""
        logger.info("开始模型预测")

        predictions = {}

        # SVR模型预测
        if 'SVR' in self.models:
            svr_pred = self.models['SVR'].predict(X)
            predictions['SVR'] = svr_pred
            logger.info(f"SVR预测完成，预测范围: {np.min(svr_pred):.1f}°C - {np.max(svr_pred):.1f}°C")

        # 顺序思维集成模型预测
        if 'Sequential_Thinking' in self.models:
            sequential_model = self.models['Sequential_Thinking']

            # 获取基础模型预测
            base_predictions = []
            for name, model_info in sequential_model['base_models'].items():
                base_model = model_info['model']
                base_pred = base_model.predict(X)
                base_predictions.append(base_pred)

            # 元学习器预测
            if base_predictions:
                meta_X = np.column_stack(base_predictions)
                sequential_pred = sequential_model['meta_learner'].predict(meta_X)
                predictions['Sequential_Thinking'] = sequential_pred
                logger.info(f"顺序思维模型预测完成，预测范围: {np.min(sequential_pred):.1f}°C - {np.max(sequential_pred):.1f}°C")

        return predictions

    def apply_material_correction(self, df: pd.DataFrame, predictions: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """应用最后2分钟添加材料的校正因子"""
        logger.info("应用最后2分钟添加材料的校正因子")

        corrected_predictions = {}

        if '最后2分钟' in df.columns:
            correction = df['最后2分钟'] * (-5/100)
            logger.info(f"校正范围: {correction.min():.2f}°C 到 {correction.max():.2f}°C")

            for name, pred in predictions.items():
                corrected_predictions[name] = pred + correction.values
                logger.info(f"应用校正到 {name} 模型")
        else:
            logger.warning("数据中没有'最后2分钟'列，无法应用校正因子")
            corrected_predictions = predictions

        return corrected_predictions

def create_prediction_results(batch5_data: pd.DataFrame, predictions: Dict[str, np.ndarray]) -> pd.DataFrame:
    """创建预测结果DataFrame"""
    logger.info("整合预测结果")

    results = pd.DataFrame()

    # 添加原始数据的关键列
    key_columns = ['炉号', '钢种', '铁水温度', '最后2分钟']
    for col in key_columns:
        if col in batch5_data.columns:
            results[col] = batch5_data[col]
        elif col == '最后2分钟' and '最后2分钟加料' in batch5_data.columns:
            results['最后2分钟添加量'] = batch5_data['最后2分钟加料']

    # 添加预测结果
    for name, pred in predictions.items():
        results[f'{name}_预测温度'] = pred

    # 选择最佳预测作为综合预测（SVR为最佳模型）
    if 'SVR' in predictions:
        results['综合预测温度'] = predictions['SVR']
    elif predictions:
        # 如果SVR不可用，使用第一个可用的预测
        first_model = list(predictions.keys())[0]
        results['综合预测温度'] = predictions[first_model]

    return results

def analyze_and_save_results(results: pd.DataFrame, output_path: str = "batch5_predictions_trained_models.xlsx"):
    """分析和保存预测结果"""
    logger.info("分析预测结果")

    # 保存结果
    results.to_excel(output_path, index=False)
    logger.info(f"预测结果已保存到: {output_path}")

    # 统计分析
    if '综合预测温度' in results.columns:
        mean_temp = results['综合预测温度'].mean()
        std_temp = results['综合预测温度'].std()
        min_temp = results['综合预测温度'].min()
        max_temp = results['综合预测温度'].max()

        logger.info(f"综合预测温度统计:")
        logger.info(f"  平均值: {mean_temp:.2f}°C")
        logger.info(f"  标准差: {std_temp:.2f}°C")
        logger.info(f"  最小值: {min_temp:.2f}°C")
        logger.info(f"  最大值: {max_temp:.2f}°C")

    # 按钢种分析
    if '钢种' in results.columns and '综合预测温度' in results.columns:
        logger.info("按钢种分析预测温度:")
        for steel_type, group in results.groupby('钢种'):
            logger.info(f"  钢种 {steel_type}:")
            logger.info(f"    样本数: {len(group)}")
            logger.info(f"    平均预测温度: {group['综合预测温度'].mean():.2f}°C")
            logger.info(f"    标准差: {group['综合预测温度'].std():.2f}°C")

    # 创建可视化
    create_visualizations(results)

def create_visualizations(results: pd.DataFrame):
    """创建可视化图表"""
    logger.info("创建可视化图表")

    # 预测温度分布图
    plt.figure(figsize=(12, 8))

    # 子图1: 温度分布直方图
    plt.subplot(2, 2, 1)
    if '综合预测温度' in results.columns:
        plt.hist(results['综合预测温度'], bins=20, alpha=0.7, color='skyblue')
        plt.axvline(results['综合预测温度'].mean(), color='r', linestyle='--',
                   label=f'平均值: {results["综合预测温度"].mean():.1f}°C')
        plt.title('综合预测温度分布')
        plt.xlabel('预测温度 (°C)')
        plt.ylabel('频次')
        plt.legend()
        plt.grid(alpha=0.3)

    # 子图2: 模型对比
    plt.subplot(2, 2, 2)
    model_cols = [col for col in results.columns if col.endswith('_预测温度')]
    if len(model_cols) > 1:
        for col in model_cols:
            model_name = col.replace('_预测温度', '')
            plt.hist(results[col], bins=15, alpha=0.5, label=model_name)
        plt.title('不同模型预测温度对比')
        plt.xlabel('预测温度 (°C)')
        plt.ylabel('频次')
        plt.legend()
        plt.grid(alpha=0.3)

    # 子图3: 铁水温度vs预测温度
    plt.subplot(2, 2, 3)
    if '铁水温度' in results.columns and '综合预测温度' in results.columns:
        plt.scatter(results['铁水温度'], results['综合预测温度'], alpha=0.6)
        plt.title('铁水温度 vs 预测出钢温度')
        plt.xlabel('铁水温度 (°C)')
        plt.ylabel('预测出钢温度 (°C)')
        plt.grid(alpha=0.3)

    # 子图4: 按钢种分析
    plt.subplot(2, 2, 4)
    if '钢种' in results.columns and '综合预测温度' in results.columns:
        steel_types = results['钢种'].value_counts().head(8).index
        steel_temps = [results[results['钢种'] == st]['综合预测温度'].mean()
                      for st in steel_types]
        plt.bar(range(len(steel_types)), steel_temps, color='lightcoral')
        plt.title('主要钢种平均预测温度')
        plt.xlabel('钢种')
        plt.ylabel('平均预测温度 (°C)')
        plt.xticks(range(len(steel_types)), steel_types, rotation=45)
        plt.grid(alpha=0.3)

    plt.tight_layout()
    plt.savefig('batch5_predictions_analysis_trained_models.png', dpi=300, bbox_inches='tight')
    plt.close()

    logger.info("可视化图表已保存")

def main():
    """主函数"""
    logger.info("=== 开始使用训练好的模型预测第五批数据 ===")

    # 检查文件
    batch5_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"
    if not os.path.exists(batch5_file):
        logger.error(f"文件不存在: {batch5_file}")
        return

    # 创建预测器
    predictor = TrainedModelPredictor()

    # 加载模型和预处理器
    predictor.load_models_and_preprocessors()

    # 加载第五批数据
    logger.info(f"加载第五批测试数据: {batch5_file}")
    batch5_data = pd.read_excel(batch5_file)
    logger.info(f"数据加载成功，形状: {batch5_data.shape}")

    # 预处理数据
    batch5_processed = predictor.preprocess_batch5_data(batch5_data)

    # 创建特征
    batch5_features = predictor.create_features(batch5_processed)

    # 准备预测特征
    X = predictor.prepare_features_for_prediction(batch5_features)

    # 模型预测
    predictions = predictor.predict_with_models(X)

    # 应用校正因子
    corrected_predictions = predictor.apply_material_correction(batch5_data, predictions)

    # 创建结果
    results = create_prediction_results(batch5_data, corrected_predictions)

    # 分析和保存结果
    analyze_and_save_results(results)

    logger.info("=== 第五批数据预测完成 ===")
    print("预测完成！结果已保存到 batch5_predictions_trained_models.xlsx")

if __name__ == "__main__":
    main()