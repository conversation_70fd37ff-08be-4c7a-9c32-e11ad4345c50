#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FactSage优化版第五批测试数据预测结果可视化
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

def create_visualization():
    """创建预测结果可视化图表"""
    
    # 读取预测结果
    try:
        df = pd.read_excel('FactSage优化版第五批测试数据温度预测结果.xlsx')
        print(f"成功读取预测结果，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('FactSage优化版第五批测试数据钢水温度预测结果分析', fontsize=16, fontweight='bold')
    
    # 1. 预测温度分布直方图
    axes[0, 0].hist(df['预测温度'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(df['预测温度'].mean(), color='red', linestyle='--', 
                      label=f'平均值: {df["预测温度"].mean():.1f}°C')
    axes[0, 0].set_xlabel('预测温度 (°C)')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].set_title('预测温度分布')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 供氧效率与预测温度关系
    scatter = axes[0, 1].scatter(df['供氧效率'], df['预测温度'], 
                                alpha=0.6, c=df['底吹传质增强'], cmap='viridis', s=30)
    axes[0, 1].set_xlabel('供氧效率')
    axes[0, 1].set_ylabel('预测温度 (°C)')
    axes[0, 1].set_title('供氧效率 vs 预测温度')
    axes[0, 1].grid(True, alpha=0.3)
    plt.colorbar(scatter, ax=axes[0, 1], label='底吹传质增强')
    
    # 3. 碱度与脱磷效率关系
    axes[0, 2].scatter(df['最佳碱度'], df['脱磷效率'], alpha=0.6, color='orange', s=30)
    axes[0, 2].set_xlabel('最佳碱度')
    axes[0, 2].set_ylabel('脱磷效率 (%)')
    axes[0, 2].set_title('最佳碱度 vs 脱磷效率')
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 活度系数分布
    activity_data = [df['C活度系数'], df['Si活度系数'], df['P活度系数']]
    labels = ['C活度系数', 'Si活度系数', 'P活度系数']
    axes[1, 0].boxplot(activity_data, labels=labels)
    axes[1, 0].set_ylabel('活度系数')
    axes[1, 0].set_title('Wagner活度系数分布')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 5. 温升与反应热关系
    axes[1, 1].scatter(df['反应热'], df['温升'], alpha=0.6, color='green', s=30)
    axes[1, 1].set_xlabel('反应热')
    axes[1, 1].set_ylabel('温升 (°C)')
    axes[1, 1].set_title('反应热 vs 温升')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. 预测温度序列图
    axes[1, 2].plot(range(len(df)), df['预测温度'], alpha=0.7, color='blue', linewidth=1)
    axes[1, 2].axhline(df['预测温度'].mean(), color='red', linestyle='--', 
                      label=f'平均值: {df["预测温度"].mean():.1f}°C')
    axes[1, 2].axhline(df['预测温度'].mean() + df['预测温度'].std(), 
                      color='orange', linestyle=':', label=f'+1σ: {df["预测温度"].mean() + df["预测温度"].std():.1f}°C')
    axes[1, 2].axhline(df['预测温度'].mean() - df['预测温度'].std(), 
                      color='orange', linestyle=':', label=f'-1σ: {df["预测温度"].mean() - df["预测温度"].std():.1f}°C')
    axes[1, 2].set_xlabel('样本序号')
    axes[1, 2].set_ylabel('预测温度 (°C)')
    axes[1, 2].set_title('预测温度序列')
    axes[1, 2].legend()
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('FactSage优化版第五批测试数据预测结果分析图.png', dpi=300, bbox_inches='tight')
    print("图表已保存为: FactSage优化版第五批测试数据预测结果分析图.png")
    
    # 显示统计信息
    print("\n=== 预测结果统计摘要 ===")
    print(f"样本数量: {len(df)}")
    print(f"预测温度: {df['预测温度'].mean():.1f} ± {df['预测温度'].std():.1f}°C")
    print(f"温度范围: {df['预测温度'].min():.1f}°C - {df['预测温度'].max():.1f}°C")
    print(f"供氧效率: {df['供氧效率'].mean():.3f} ± {df['供氧效率'].std():.3f}")
    print(f"底吹传质增强: {df['底吹传质增强'].mean():.3f} ± {df['底吹传质增强'].std():.3f}")
    print(f"最佳碱度: {df['最佳碱度'].mean():.2f} ± {df['最佳碱度'].std():.2f}")
    print(f"C活度系数: {df['C活度系数'].mean():.4f} ± {df['C活度系数'].std():.4f}")
    print(f"Si活度系数: {df['Si活度系数'].mean():.4f} ± {df['Si活度系数'].std():.4f}")
    print(f"P活度系数: {df['P活度系数'].mean():.4f} ± {df['P活度系数'].std():.4f}")
    
    # 显示关键特征的相关性
    print("\n=== 关键特征相关性分析 ===")
    correlation_vars = ['预测温度', '供氧效率', '底吹传质增强', '最佳碱度', 'C活度系数', 'Si活度系数', 'P活度系数']
    corr_matrix = df[correlation_vars].corr()
    
    # 创建相关性热图
    plt.figure(figsize=(10, 8))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.3f', cbar_kws={'label': '相关系数'})
    plt.title('FactSage优化版模型特征相关性矩阵', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('FactSage优化版特征相关性矩阵.png', dpi=300, bbox_inches='tight')
    print("相关性矩阵已保存为: FactSage优化版特征相关性矩阵.png")
    
    plt.show()

if __name__ == "__main__":
    create_visualization() 