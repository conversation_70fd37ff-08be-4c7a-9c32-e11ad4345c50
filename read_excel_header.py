import pandas as pd
import sys

PREDICTION_FILE = '4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx'
TARGET_COLUMN = '渣中FeO'

try:
    df = pd.read_excel(PREDICTION_FILE)
    print(f"Headers from {PREDICTION_FILE}:")
    print(list(df.columns))
    print("\n--- Checking column: {TARGET_COLUMN} ---")
    if TARGET_COLUMN in df.columns:
        print(f"First 5 values of '{TARGET_COLUMN}':")
        print(df[TARGET_COLUMN].head())
        print(f"Unique values in '{TARGET_COLUMN}':")
        print(df[TARGET_COLUMN].unique())
        if df[TARGET_COLUMN].nunique() == 1:
            print(f"WARNING: Column '{TARGET_COLUMN}' contains only one unique value.")
        # Check data type and describe
        print(f"Data type of '{TARGET_COLUMN}': {df[TARGET_COLUMN].dtype}")
        print(f"Descriptive stats for '{TARGET_COLUMN}':")
        print(df[TARGET_COLUMN].describe())

    else:
        print(f"Column '{TARGET_COLUMN}' not found in {PREDICTION_FILE}.")
        
except FileNotFoundError:
    print(f"ERROR: File not found: {PREDICTION_FILE}", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"Error reading or processing Excel file {PREDICTION_FILE}: {e}", file=sys.stderr)
    sys.exit(1) 