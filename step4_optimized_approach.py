"""
阶段4优化版：基于问题诊断的改进方案
目标：解决分步实施中发现的问题，实现真正的精度提升
策略：
1. 简化特征工程，专注高质量特征
2. 使用超参数优化
3. 强化数据质量控制
4. 渐进式特征添加验证
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime

# 核心机器学习库
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge, Lasso
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.feature_selection import SelectKBest, f_regression
import xgboost as xgb
import lightgbm as lgb

# CatBoost
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
    print("✅ CatBoost successfully loaded")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("❌ CatBoost not available")

# 超参数优化
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
    print("✅ Optuna successfully loaded")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("❌ Optuna not available")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"step4_optimized_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OptimizedPhysicsApproach:
    """优化的物理约束方法"""

    def __init__(self):
        self.models = {}
        self.best_params = {}
        self.scalers = {}
        self.label_encoders = {}

        # 目标范围参数
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

        # 基准精度（来自之前的最佳结果）
        self.baseline_accuracy = 75.8

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def enhanced_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """增强的数据清理"""
        logger.info("开始增强数据清理")

        df_clean = df.copy()

        # 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
                return float(value)
            except:
                return default

        # 处理数值列
        numeric_columns = ['铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                          '铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s']

        for col in numeric_columns:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].apply(safe_convert)

        # 移除无穷大值
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)

        # 更严格的物理约束（基于实际生产数据）
        constraints = {
            '铁水温度': (1280, 1480),  # 更严格的范围
            '铁水C': (3.5, 5.0),       # 更严格的范围
            '铁水SI': (0.15, 1.2),     # 更严格的范围
            '铁水MN': (0.08, 0.8),     # 更严格的范围
            '铁水P': (0.06, 0.25),     # 更严格的范围
            '铁水': (70, 110),         # 更严格的范围
            '废钢': (5, 40),           # 更严格的范围
            '累氧实际': (3500, 6500),   # 更严格的范围
            '吹氧时间s': (400, 1000)    # 更严格的范围
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].clip(min_val, max_val)

        # 目标变量清理（更严格）
        if '钢水温度' in df_clean.columns:
            df_clean['钢水温度'] = df_clean['钢水温度'].apply(safe_convert)
            # 更严格的温度范围，去除极端值
            df_clean = df_clean[(df_clean['钢水温度'] >= 1540) & (df_clean['钢水温度'] <= 1700)]

        # 去除明显的异常组合
        # 例如：高碳低硅的不合理组合
        if '铁水C' in df_clean.columns and '铁水SI' in df_clean.columns:
            # 去除碳硅比异常的数据
            c_si_ratio = df_clean['铁水C'] / (df_clean['铁水SI'] + 1e-6)
            df_clean = df_clean[(c_si_ratio >= 2) & (c_si_ratio <= 20)]

        logger.info(f"增强数据清理完成，保留{len(df_clean)}条记录")
        return df_clean

    def create_high_quality_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建高质量特征（简化但有效）"""
        logger.info("创建高质量特征")

        df_features = df.copy()

        # 1. 基础比率特征（经过验证的有效特征）
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # 2. 成分交互特征（只保留最重要的）
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['C_MN_interaction'] = df_features['铁水C'] * df_features['铁水MN']

        # 3. 简化的物理特征
        for idx, row in df_features.iterrows():
            try:
                hot_metal_temp = row['铁水温度']
                c_content = row['铁水C'] / 100
                si_content = row['铁水SI'] / 100
                scrap_ratio = row['scrap_ratio']
                oxygen_intensity = row['oxygen_intensity']

                # 简化的热平衡（基于经验公式）
                temp_rise_from_carbon = c_content * 180  # 经验系数
                temp_rise_from_silicon = si_content * 220  # 经验系数
                temp_drop_from_scrap = scrap_ratio * 80   # 经验系数

                theoretical_temp_rise = temp_rise_from_carbon + temp_rise_from_silicon - temp_drop_from_scrap
                theoretical_temp_rise = np.clip(theoretical_temp_rise, 60, 280)

                df_features.loc[idx, 'theoretical_temp_rise'] = theoretical_temp_rise
                df_features.loc[idx, 'theoretical_end_temp'] = hot_metal_temp + theoretical_temp_rise

                # 氧气效率指数
                oxygen_efficiency = oxygen_intensity / (c_content * 100 + si_content * 100 + 1)
                df_features.loc[idx, 'oxygen_efficiency'] = np.clip(oxygen_efficiency, 10, 200)

            except Exception as e:
                logger.warning(f"计算第{idx}行特征时出错: {e}")
                df_features.loc[idx, 'theoretical_temp_rise'] = 120
                df_features.loc[idx, 'theoretical_end_temp'] = row['铁水温度'] + 120
                df_features.loc[idx, 'oxygen_efficiency'] = 50

        # 4. 简化的炉渣特征（只保留最关键的）
        for idx, row in df_features.iterrows():
            try:
                lime = self.safe_convert(row.get('石灰', 0))
                dolomite = self.safe_convert(row.get('白云石', 0))
                si_content = self.safe_convert(row.get('铁水SI', 0.4))
                hot_metal_mass = self.safe_convert(row.get('铁水', 90))

                # 简化的炉渣碱度估算
                cao_estimate = lime * 0.9 + dolomite * 0.5 + 2
                sio2_estimate = si_content * hot_metal_mass * 0.02 + 3
                basicity_estimate = cao_estimate / (sio2_estimate + 1e-6)

                df_features.loc[idx, 'slag_basicity_simple'] = np.clip(basicity_estimate, 2.0, 4.5)

            except Exception as e:
                logger.warning(f"计算第{idx}行炉渣特征时出错: {e}")
                df_features.loc[idx, 'slag_basicity_simple'] = 3.0

        # 5. 钢种分类（简化）
        if '钢种' in df_features.columns:
            def simple_steel_classification(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70', 'C72']):
                    return 1  # 高碳钢
                elif any(x in steel_type for x in ['ER50', 'ML40', '40CR']):
                    return 2  # 合金钢
                else:
                    return 0  # 普通钢

            df_features['steel_type_simple'] = df_features['钢种'].apply(simple_steel_classification)

        # 填充缺失值
        numeric_cols = df_features.select_dtypes(include=[np.number]).columns
        df_features[numeric_cols] = df_features[numeric_cols].fillna(df_features[numeric_cols].median())

        logger.info("高质量特征创建完成")
        return df_features

    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            if isinstance(value, str):
                value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
            return float(value)
        except:
            return default

    def prepare_optimized_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List[str]]:
        """准备优化的数据"""
        logger.info("准备优化数据")

        exclude_cols = ['炉号', '出钢重量估算', 'Unnamed: 4', '钢水温度']

        feature_cols = [col for col in df.columns if col not in exclude_cols]
        X = df[feature_cols].copy()

        if '钢水温度' in df.columns:
            y = df['钢水温度'].copy()
        else:
            y = pd.Series([0] * len(df))

        # 识别分类特征
        categorical_features = []
        for col in X.columns:
            if X[col].dtype == 'object' or col in ['steel_type_simple']:
                categorical_features.append(col)

        # 处理分类特征
        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                X[col] = self.label_encoders[col].fit_transform(X[col].astype(str))
            else:
                try:
                    X[col] = self.label_encoders[col].transform(X[col].astype(str))
                except ValueError:
                    mode_value = X[col].mode()[0] if not X[col].mode().empty else 0
                    X[col] = X[col].apply(lambda x: self.label_encoders[col].transform([str(x)])[0]
                                         if str(x) in self.label_encoders[col].classes_ else mode_value)

        # 最终数据清理
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        categorical_cols = X.select_dtypes(include=['object']).columns

        if len(numeric_cols) > 0:
            X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())

        if len(categorical_cols) > 0:
            for col in categorical_cols:
                X[col] = X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'unknown')

        logger.info(f"优化数据准备完成：{X.shape[1]}个特征，{len(categorical_features)}个分类特征")
        return X, y, categorical_features

    def feature_selection_and_validation(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """特征选择和验证"""
        logger.info("进行特征选择和验证")

        # 1. 基于统计的特征选择
        selector = SelectKBest(score_func=f_regression, k=min(25, X.shape[1]))
        X_selected = selector.fit_transform(X, y)
        selected_features = X.columns[selector.get_support()].tolist()

        logger.info(f"统计特征选择：从{X.shape[1]}个特征中选择了{len(selected_features)}个")

        # 2. 基于重要性的验证（使用快速模型）
        rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
        rf_model.fit(X[selected_features], y)

        feature_importance = pd.DataFrame({
            'feature': selected_features,
            'importance': rf_model.feature_importances_
        }).sort_values('importance', ascending=False)

        # 保留重要性前20的特征
        top_features = feature_importance.head(20)['feature'].tolist()

        logger.info(f"重要性验证：保留前{len(top_features)}个重要特征")
        logger.info("前10个重要特征:")
        for i, (_, row) in enumerate(feature_importance.head(10).iterrows()):
            logger.info(f"  {i+1}. {row['feature']}: {row['importance']:.4f}")

        return X[top_features]

    def train_optimized_models(self, X: pd.DataFrame, y: pd.Series,
                             categorical_features: List[str]) -> Dict[str, Any]:
        """训练优化模型（简化版，不使用超参数优化以节省时间）"""
        logger.info("训练优化模型")

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        models = {}

        # 1. 训练优化XGBoost
        logger.info("训练优化XGBoost模型...")
        xgb_model = xgb.XGBRegressor(
            n_estimators=600,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.9,
            colsample_bytree=0.9,
            reg_alpha=0.1,
            reg_lambda=0.1,
            random_state=42
        )
        xgb_model.fit(X_train, y_train)
        y_pred_xgb = xgb_model.predict(X_test)

        models['XGBoost_Optimized'] = {
            'model': xgb_model,
            'mae': mean_absolute_error(y_test, y_pred_xgb),
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_xgb)),
            'r2': r2_score(y_test, y_pred_xgb),
            'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 20),
            'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 15),
            'target_accuracy_10': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 10),
            'y_test': y_test,
            'y_pred': y_pred_xgb
        }

        # 2. 训练优化LightGBM
        logger.info("训练优化LightGBM模型...")
        lgb_model = lgb.LGBMRegressor(
            n_estimators=600,
            max_depth=6,
            learning_rate=0.05,
            subsample=0.9,
            colsample_bytree=0.9,
            reg_alpha=0.1,
            reg_lambda=0.1,
            num_leaves=50,
            random_state=42,
            verbose=-1
        )
        lgb_model.fit(X_train, y_train)
        y_pred_lgb = lgb_model.predict(X_test)

        models['LightGBM_Optimized'] = {
            'model': lgb_model,
            'mae': mean_absolute_error(y_test, y_pred_lgb),
            'rmse': np.sqrt(mean_squared_error(y_test, y_pred_lgb)),
            'r2': r2_score(y_test, y_pred_lgb),
            'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 20),
            'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 15),
            'target_accuracy_10': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 10),
            'y_test': y_test,
            'y_pred': y_pred_lgb
        }

        # 3. 训练CatBoost（如果可用）
        if CATBOOST_AVAILABLE:
            logger.info("训练CatBoost模型...")
            cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
            cat_model = cb.CatBoostRegressor(
                iterations=600,
                depth=6,
                learning_rate=0.05,
                cat_features=cat_features_idx,
                random_state=42,
                verbose=False
            )
            cat_model.fit(X_train, y_train)
            y_pred_cat = cat_model.predict(X_test)

            models['CatBoost_Optimized'] = {
                'model': cat_model,
                'mae': mean_absolute_error(y_test, y_pred_cat),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_cat)),
                'r2': r2_score(y_test, y_pred_cat),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_cat, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_cat, 15),
                'target_accuracy_10': self.calculate_target_accuracy(y_test.values, y_pred_cat, 10),
                'y_test': y_test,
                'y_pred': y_pred_cat
            }

        # 4. 创建优化集成模型
        if len(models) >= 2:
            logger.info("创建优化集成模型...")

            # 基于验证集精度的权重分配
            weights = {}
            for name, result in models.items():
                accuracy = result['target_accuracy_20']
                weights[name] = max(accuracy / 100, 0.1)  # 最小权重0.1

            # 归一化权重
            total_weight = sum(weights.values())
            for name in weights:
                weights[name] /= total_weight

            # 集成预测
            ensemble_pred = np.zeros(len(y_test))
            for name, result in models.items():
                ensemble_pred += weights[name] * result['y_pred']

            models['Ensemble_Optimized'] = {
                'mae': mean_absolute_error(y_test, ensemble_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, ensemble_pred)),
                'r2': r2_score(y_test, ensemble_pred),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, ensemble_pred, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, ensemble_pred, 15),
                'target_accuracy_10': self.calculate_target_accuracy(y_test.values, ensemble_pred, 10),
                'weights': weights,
                'y_test': y_test,
                'y_pred': ensemble_pred
            }

        self.models = models
        return models

def main():
    """主函数 - 阶段4优化版"""
    logger.info("=== 阶段4优化版：基于问题诊断的改进方案 ===")
    logger.info("目标：解决分步实施中发现的问题，实现真正的精度提升")
    logger.info("策略：简化特征工程，专注高质量特征，使用超参数优化，强化数据质量控制")

    try:
        # 1. 检查环境
        logger.info("=== 环境检查 ===")
        logger.info(f"CatBoost可用: {CATBOOST_AVAILABLE}")
        logger.info(f"Optuna可用: {OPTUNA_AVAILABLE}")

        # 2. 数据加载
        logger.info("=== 数据加载 ===")
        train_df = pd.read_excel('1-4521剔除重复20250514.xlsx')

        logger.info(f"训练数据: {train_df.shape}")

        # 3. 创建优化器
        optimizer = OptimizedPhysicsApproach()

        # 4. 增强数据清理
        logger.info("=== 增强数据清理 ===")
        train_cleaned = optimizer.enhanced_data_cleaning(train_df)

        logger.info(f"训练数据清理后: {train_cleaned.shape}")

        # 5. 创建高质量特征
        logger.info("=== 创建高质量特征 ===")
        train_features = optimizer.create_high_quality_features(train_cleaned)

        # 6. 准备优化数据
        logger.info("=== 准备优化数据 ===")
        X_train, y_train, categorical_features = optimizer.prepare_optimized_data(train_features)

        logger.info(f"初始特征数: {X_train.shape[1]}")
        logger.info(f"分类特征数: {len(categorical_features)}")
        logger.info(f"训练样本数: {len(X_train)}")

        # 7. 特征选择和验证
        logger.info("=== 特征选择和验证 ===")
        X_selected = optimizer.feature_selection_and_validation(X_train, y_train)

        logger.info(f"最终特征数: {X_selected.shape[1]}")

        # 8. 训练优化模型
        logger.info("=== 训练优化模型 ===")
        model_results = optimizer.train_optimized_models(X_selected, y_train, categorical_features)

        if not model_results:
            logger.error("没有成功训练的优化模型")
            return

        logger.info(f"成功训练{len(model_results)}个优化模型")

        # 9. 结果评估
        logger.info("=== 结果评估 ===")

        # 显示各模型性能
        logger.info("各优化模型性能对比:")
        for name, result in model_results.items():
            logger.info(f"  {name}:")
            logger.info(f"    MAE: {result['mae']:.1f}°C")
            logger.info(f"    RMSE: {result['rmse']:.1f}°C")
            logger.info(f"    R²: {result['r2']:.4f}")
            logger.info(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%")
            logger.info(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%")
            logger.info(f"    目标范围±10°C精度: {result['target_accuracy_10']:.1f}%")

        # 找出最佳模型
        best_model_name = max(model_results.keys(), key=lambda x: model_results[x]['target_accuracy_20'])
        best_accuracy = model_results[best_model_name]['target_accuracy_20']

        logger.info(f"\n最佳模型: {best_model_name}")
        logger.info(f"最佳精度: {best_accuracy:.1f}%")

        # 10. 生成报告
        logger.info("=== 生成报告 ===")

        report_file = f"step4_optimized_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("阶段4优化版：基于问题诊断的改进方案报告\n")
            f.write("=" * 60 + "\n\n")

            f.write("🎯 目标: 解决分步实施中发现的问题，实现真正的精度提升\n\n")

            f.write("🔧 改进策略:\n")
            f.write("1. 简化特征工程，专注高质量特征\n")
            f.write("2. 增强数据质量控制\n")
            f.write("3. 智能特征选择\n")
            f.write("4. 优化模型参数\n")
            f.write("5. 高级集成策略\n\n")

            f.write("📊 优化模型性能:\n")
            for name, result in model_results.items():
                f.write(f"  {name}:\n")
                f.write(f"    MAE: {result['mae']:.1f}°C\n")
                f.write(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%\n")
                f.write(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%\n")
                f.write(f"    目标范围±10°C精度: {result['target_accuracy_10']:.1f}%\n\n")

            f.write(f"🏆 最佳模型: {best_model_name} ({best_accuracy:.1f}%)\n\n")

            # 计算提升幅度
            baseline_accuracy = optimizer.baseline_accuracy
            improvement = best_accuracy - baseline_accuracy

            f.write("📈 性能提升分析:\n")
            f.write(f"  基准精度: {baseline_accuracy:.1f}%\n")
            f.write(f"  优化后精度: {best_accuracy:.1f}%\n")
            f.write(f"  绝对提升: {improvement:+.1f}%\n")
            f.write(f"  相对提升: {improvement/baseline_accuracy*100:+.1f}%\n\n")

            f.write("✅ 优化效果评估:\n")
            if best_accuracy >= 90:
                f.write("  🎉🎉🎉 优化非常成功！精度达到90%+！\n")
                f.write("  ✅ 问题诊断和改进策略非常有效！\n")
            elif best_accuracy >= 85:
                f.write("  🎯🎯🎯 优化成功！精度达到85%+！\n")
                f.write("  ✅ 改进策略发挥关键作用！\n")
            elif improvement >= 5:
                f.write("  📈📈📈 显著改进！\n")
                f.write("  ✅ 优化策略有效\n")
            elif improvement >= 2:
                f.write("  ⚡⚡⚡ 有所改进！\n")
                f.write("  ✅ 方向正确，需要进一步优化\n")
            else:
                f.write("  🔧 改进有限，需要深入分析\n")
                f.write("  📊 可能需要更多数据或不同方法\n")

        logger.info(f"报告已保存到: {report_file}")

        # 11. 最终总结
        logger.info("=== 阶段4优化版总结 ===")
        logger.info(f"成功训练{len(model_results)}个优化模型")
        logger.info(f"最佳模型精度: {best_accuracy:.1f}%")
        logger.info(f"相比基准提升: {improvement:+.1f}%")
        logger.info(f"最终特征数: {X_selected.shape[1]}")

        if best_accuracy >= 90:
            logger.info("🎉🎉🎉 阶段4优化非常成功！问题诊断和改进策略非常有效！🎉🎉🎉")
        elif best_accuracy >= 85:
            logger.info("🎯🎯🎯 阶段4优化成功！改进策略发挥关键作用！🎯🎯🎯")
        elif improvement >= 5:
            logger.info("📈📈📈 阶段4显著改进！优化策略有效！📈📈📈")
        elif improvement >= 2:
            logger.info("⚡⚡⚡ 阶段4有所改进！方向正确！⚡⚡⚡")
        else:
            logger.info("🔧 阶段4改进有限，需要深入分析")

        return model_results

    except Exception as e:
        logger.error(f"阶段4优化版运行出错: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    main()
