#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版炉渣成分预测模型
基于数据分析结果的改进版本
针对实际生产数据特点进行优化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedSlagPredictor:
    """改进版炉渣成分预测器"""
    
    def __init__(self):
        # 修正后的分子量和转换系数
        self.molecular_weights = {
            'CaO': 56.08, 'SiO2': 60.08, 'FeO': 71.85, 'MgO': 40.30,
            'P2O5': 141.94, 'MnO': 70.94
        }
        
        # 修正的氧化系数（基于实际生产数据）
        self.oxidation_coeffs = {
            'Si_to_SiO2': 2.14,
            'Mn_to_MnO': 1.29,
            'P_to_P2O5': 2.29
        }
        
        # 造渣材料有效成分（修正值）
        self.flux_compositions = {
            'lime': {'CaO': 0.85, 'MgO': 0.03, 'SiO2': 0.05},
            'dolomite': {'CaO': 0.30, 'MgO': 0.20, 'SiO2': 0.03},
            'limestone': {'CaO': 0.50, 'MgO': 0.02, 'SiO2': 0.04}
        }
    
    def safe_convert_to_float(self, value):
        """安全转换为浮点数"""
        if pd.isna(value):
            return 0.0
        if isinstance(value, str):
            try:
                return float(value)
            except:
                return 0.0
        return float(value)
    
    def calculate_slag_components(self, row):
        """计算炉渣各组分质量"""
        # 安全获取数据
        hot_metal = self.safe_convert_to_float(row['铁水'])
        si_content = self.safe_convert_to_float(row['铁水SI'])
        mn_content = self.safe_convert_to_float(row['铁水MN'])
        p_content = self.safe_convert_to_float(row['铁水P'])
        
        lime = self.safe_convert_to_float(row['石灰'])
        dolomite = self.safe_convert_to_float(row['白云石'])
        limestone = self.safe_convert_to_float(row['石灰石'])
        
        # 计算氧化产物
        sio2_from_si = hot_metal * si_content * self.oxidation_coeffs['Si_to_SiO2'] / 100
        mno_from_mn = hot_metal * mn_content * self.oxidation_coeffs['Mn_to_MnO'] / 100
        p2o5_from_p = hot_metal * p_content * self.oxidation_coeffs['P_to_P2O5'] / 100
        
        # 计算造渣材料贡献
        cao_from_lime = lime * self.flux_compositions['lime']['CaO']
        cao_from_dolomite = dolomite * self.flux_compositions['dolomite']['CaO']
        cao_from_limestone = limestone * self.flux_compositions['limestone']['CaO']
        total_cao = cao_from_lime + cao_from_dolomite + cao_from_limestone
        
        mgo_from_lime = lime * self.flux_compositions['lime']['MgO']
        mgo_from_dolomite = dolomite * self.flux_compositions['dolomite']['MgO']
        mgo_from_limestone = limestone * self.flux_compositions['limestone']['MgO']
        total_mgo = mgo_from_lime + mgo_from_dolomite + mgo_from_limestone
        
        sio2_from_flux = (lime * self.flux_compositions['lime']['SiO2'] +
                         dolomite * self.flux_compositions['dolomite']['SiO2'] +
                         limestone * self.flux_compositions['limestone']['SiO2'])
        total_sio2 = sio2_from_si + sio2_from_flux
        
        # FeO来源：氧化铁损失（经验值1.5%）
        feo_mass = hot_metal * 0.015
        
        # 计算总炉渣量
        total_slag = total_cao + total_sio2 + total_mgo + feo_mass + mno_from_mn + p2o5_from_p
        total_slag = max(total_slag, 10)  # 最小炉渣量
        
        return {
            'CaO_mass': total_cao,
            'SiO2_mass': total_sio2,
            'FeO_mass': feo_mass,
            'MgO_mass': total_mgo,
            'MnO_mass': mno_from_mn,
            'P2O5_mass': p2o5_from_p,
            'total_slag': total_slag
        }
    
    def predict_composition(self, row):
        """预测炉渣成分百分比"""
        components = self.calculate_slag_components(row)
        
        if components['total_slag'] <= 0:
            return {
                'CaO': 50.0, 'SiO2': 15.0, 'FeO': 20.0,
                'MgO': 8.0, 'MnO': 5.0, 'P2O5': 2.0,
                'basicity': 3.33
            }
        
        # 计算百分比
        composition = {}
        for comp in ['CaO', 'SiO2', 'FeO', 'MgO', 'MnO', 'P2O5']:
            mass_key = comp + '_mass'
            composition[comp] = components[mass_key] / components['total_slag'] * 100
        
        # 归一化到100%
        total_pct = sum(composition.values())
        if total_pct > 0:
            for comp in composition:
                composition[comp] = composition[comp] / total_pct * 100
        
        # 计算碱度
        composition['basicity'] = composition['CaO'] / composition['SiO2'] if composition['SiO2'] > 0 else 0
        
        return composition

class ImprovedTemperaturePredictor:
    """改进版温度预测器"""
    
    def __init__(self):
        # 修正的热力学参数
        self.heat_params = {
            'decarb_heat': 11500,      # kJ/kg C
            'si_oxidation_heat': 30800, # kJ/kg Si
            'mn_oxidation_heat': 7200,  # kJ/kg Mn
            'steel_heat_capacity': 0.75, # kJ/kg·K
            'heat_loss_rate': 0.03      # K/s
        }
    
    def safe_convert_to_float(self, value):
        """安全转换为浮点数"""
        if pd.isna(value):
            return 0.0
        if isinstance(value, str):
            try:
                return float(value)
            except:
                return 0.0
        return float(value)
    
    def predict_temperature(self, row):
        """预测钢水温度"""
        # 安全获取数据
        hot_metal_temp = self.safe_convert_to_float(row['铁水温度'])
        if hot_metal_temp <= 0:
            hot_metal_temp = 1350  # 默认铁水温度
        
        hot_metal_mass = self.safe_convert_to_float(row['铁水'])
        scrap_mass = self.safe_convert_to_float(row['废钢'])
        
        c_content = self.safe_convert_to_float(row['铁水C'])
        si_content = self.safe_convert_to_float(row['铁水SI'])
        mn_content = self.safe_convert_to_float(row['铁水MN'])
        
        blow_time = self.safe_convert_to_float(row['吹氧时间s'])
        if blow_time <= 0:
            blow_time = 600  # 默认吹氧时间
        
        # 计算反应热
        carbon_oxidized = c_content * 0.8  # 假设80%碳被氧化
        decarb_heat = carbon_oxidized * self.heat_params['decarb_heat']
        
        si_heat = si_content * self.heat_params['si_oxidation_heat']
        mn_heat = mn_content * self.heat_params['mn_oxidation_heat']
        
        total_heat = decarb_heat + si_heat + mn_heat
        
        # 钢水质量
        total_steel = hot_metal_mass + scrap_mass
        if total_steel <= 0:
            total_steel = 100  # 默认值
        
        # 温升计算
        temp_rise = total_heat / (total_steel * self.heat_params['steel_heat_capacity'])
        
        # 热损失
        heat_loss = self.heat_params['heat_loss_rate'] * blow_time
        
        # 最终温度
        final_temp = hot_metal_temp + temp_rise - heat_loss
        
        return max(final_temp, 1500)  # 最低温度限制

def main():
    """主函数"""
    print("=== 优化版炉渣成分预测系统 ===")
    print("基于数据分析结果的改进版本\n")
    
    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return
    
    # 初始化预测器
    slag_predictor = ImprovedSlagPredictor()
    temp_predictor = ImprovedTemperaturePredictor()
    
    # 预测结果
    results = []
    
    print("开始优化预测...")
    for idx, row in df.iterrows():
        if idx % 500 == 0:
            print(f"已处理 {idx}/{len(df)} 条记录")
        
        try:
            # 炉渣成分预测
            slag_comp = slag_predictor.predict_composition(row)
            
            # 温度预测
            pred_temp = temp_predictor.predict_temperature(row)
            
            # 保存结果
            result = {
                '炉号': row['炉号'],
                '钢种': row['钢种'],
                '实际温度': row['钢水温度'] if pd.notna(row['钢水温度']) else None,
                '预测温度': pred_temp,
                '温度偏差': abs(pred_temp - row['钢水温度']) if pd.notna(row['钢水温度']) else None,
                'CaO预测': slag_comp['CaO'],
                'SiO2预测': slag_comp['SiO2'],
                'FeO预测': slag_comp['FeO'],
                'MgO预测': slag_comp['MgO'],
                'MnO预测': slag_comp['MnO'],
                'P2O5预测': slag_comp['P2O5'],
                '预测碱度': slag_comp['basicity']
            }
            results.append(result)
            
        except Exception as e:
            print(f"处理第{idx}行数据时出错：{e}")
            continue
    
    # 保存结果
    results_df = pd.DataFrame(results)
    
    # 计算统计信息
    temp_valid = results_df.dropna(subset=['温度偏差'])
    if len(temp_valid) > 0:
        print(f"\n=== 优化后预测精度 ===")
        print(f"温度预测样本数: {len(temp_valid)}")
        print(f"平均绝对误差: {temp_valid['温度偏差'].mean():.1f}°C")
        print(f"标准偏差: {temp_valid['温度偏差'].std():.1f}°C")
        print(f"±20°C精度: {(temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100:.1f}%")
        print(f"±50°C精度: {(temp_valid['温度偏差'] <= 50).sum() / len(temp_valid) * 100:.1f}%")
    
    print(f"\n炉渣成分统计:")
    print(f"CaO平均值: {results_df['CaO预测'].mean():.1f}%")
    print(f"SiO2平均值: {results_df['SiO2预测'].mean():.1f}%")
    print(f"FeO平均值: {results_df['FeO预测'].mean():.1f}%")
    print(f"平均碱度: {results_df['预测碱度'].mean():.1f}")
    
    # 保存优化结果
    output_file = '优化版预测结果.xlsx'
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        results_df.to_excel(writer, sheet_name='优化预测结果', index=False)
        
        # 对比分析
        comparison = pd.DataFrame({
            '指标': ['平均绝对误差(°C)', '±20°C精度(%)', 'CaO平均值(%)', 'SiO2平均值(%)', '平均碱度'],
            '原始模型': [321.3, 0.7, 95.8, 0.0, 5921.8],
            '优化模型': [
                temp_valid['温度偏差'].mean() if len(temp_valid) > 0 else 0,
                (temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100 if len(temp_valid) > 0 else 0,
                results_df['CaO预测'].mean(),
                results_df['SiO2预测'].mean(),
                results_df['预测碱度'].mean()
            ]
        })
        comparison.to_excel(writer, sheet_name='模型对比', index=False)
    
    print(f"\n优化结果已保存到: {output_file}")
    print("=== 优化版预测系统运行完成 ===")

if __name__ == "__main__":
    main()
