#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第二步：FactSage优化版模型训练和数据处理
修正数据类型问题，确保模型达到最佳状态
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

class FactSageOptimalTrainer:
    """FactSage优化版模型训练器"""
    
    def __init__(self):
        print("=== 第二步：FactSage优化版模型训练 ===")
        
        # FactSage活度计算参数（已验证的最佳配置）
        self.factsage_activity = {
            'e_C_C': 0.14, 'e_C_Si': 0.08, 'e_Si_Si': 0.11, 'e_Mn_C': -0.012,
            'e_P_C': 0.062, 'e_P_Si': 0.077, 'e_O_C': -0.45, 'e_O_Si': -0.131, 'e_O_P': -0.070
        }
        
        # FactSage热力学数据（已验证）
        self.factsage_thermo = {
            'delta_H_CO': -110.5, 'delta_H_CO2': -393.5, 'delta_H_SiO2': -910.7,
            'delta_H_MnO': -385.2, 'delta_H_P2O5': -1640.1, 'delta_H_FeO': -272.0,
            'Cp_steel': 46.0, 'Cp_slag': 85.0, 'Cp_gas': 29.0
        }
        
        # 双渣法脱磷参数（已验证）
        self.dephosphorization = {
            'A_deP': 22350, 'B_deP': -16.94, 'optimal_basicity_base': 2.8,
            'basicity_temp_coeff': 0.001, 'basicity_p_coeff': 15.0,
            'log_pO2_optimal': -8.5, 'pO2_temp_coeff': 0.002, 'pO2_feo_coeff': 0.1
        }
        
        # 供氧模型参数（已验证）
        self.oxygen_supply = {
            'base_intensity': 500, 'temp_response_coeff': 0.8, 'decarb_demand_coeff': 12,
            'si_demand_coeff': 2.3, 'mn_demand_coeff': 0.8, 'p_demand_coeff': 2.5
        }
        
        # 底吹模型参数（已验证）
        self.bottom_blowing = {
            'stirring_efficiency': 0.85, 'mass_transfer_coeff': 0.12,
            'reaction_rate_enhance': 1.25, 'heat_transfer_coeff': 0.08
        }
        
        # 机器学习模型
        self.ml_model = None
        self.is_trained = False
        
    def safe_convert(self, value, default=0.0):
        """安全数值转换，处理各种数据类型"""
        if pd.isna(value):
            return default
        if isinstance(value, str):
            try:
                # 处理字符串中的数字
                cleaned = value.replace(',', '').replace(' ', '')
                return float(cleaned)
            except:
                return default
        try:
            return float(value)
        except:
            return default
    
    def load_and_clean_data(self, file_path):
        """加载和清理数据"""
        print(f"📂 正在加载数据: {file_path}")
        
        try:
            df = pd.read_excel(file_path)
            print(f"✅ 成功读取数据，原始记录数: {len(df)}")
            
            # 数据清理和类型转换
            numeric_columns = ['铁水', '废钢', '铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                             '吹氧时间s', '累氧实际', '最大角度', '石灰', '白云石', '钢水温度']
            
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: self.safe_convert(x))
            
            # 移除异常数据
            initial_count = len(df)
            
            # 基本合理性检查
            if '铁水' in df.columns:
                df = df[(df['铁水'] >= 70) & (df['铁水'] <= 150)]
            if '废钢' in df.columns:
                df = df[(df['废钢'] >= 10) & (df['废钢'] <= 50)]
            if '铁水温度' in df.columns:
                df = df[(df['铁水温度'] >= 1250) & (df['铁水温度'] <= 1450)]
            if '吹氧时间s' in df.columns:
                df = df[(df['吹氧时间s'] >= 300) & (df['吹氧时间s'] <= 1000)]
            
            cleaned_count = len(df)
            removed_count = initial_count - cleaned_count
            
            print(f"🧹 数据清理完成:")
            print(f"   - 移除异常记录: {removed_count}条")
            print(f"   - 保留有效记录: {cleaned_count}条")
            print(f"   - 数据质量: {cleaned_count/initial_count*100:.1f}%")
            
            return df
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return None
    
    def calculate_activity_coefficients(self, composition):
        """计算活度系数"""
        x_C = min(composition['C'] / 100, 0.05)
        x_Si = min(composition['Si'] / 100, 0.015)
        x_Mn = min(composition['Mn'] / 100, 0.02)
        x_P = min(composition['P'] / 100, 0.002)
        
        ln_gamma_C = (self.factsage_activity['e_C_C'] * x_C +
                     self.factsage_activity['e_C_Si'] * x_Si +
                     self.factsage_activity['e_Mn_C'] * x_Mn)
        
        ln_gamma_Si = (self.factsage_activity['e_Si_Si'] * x_Si +
                      self.factsage_activity['e_C_Si'] * x_C)
        
        ln_gamma_P = (self.factsage_activity['e_P_C'] * x_C +
                     self.factsage_activity['e_P_Si'] * x_Si)
        
        return {
            'gamma_C': max(0.5, min(np.exp(ln_gamma_C), 2.0)),
            'gamma_Si': max(0.5, min(np.exp(ln_gamma_Si), 2.0)),
            'gamma_P': max(0.5, min(np.exp(ln_gamma_P), 2.0))
        }
    
    def calculate_oxygen_potential(self, feo_pct, temp_k):
        """计算氧势"""
        delta_G = self.factsage_thermo['delta_H_FeO'] - temp_k * 60.8 / 1000
        log_pO2 = 2 * np.log10(max(feo_pct / 100, 0.1)) + 2 * delta_G / (2.303 * 8.314 * temp_k)
        return log_pO2
    
    def predict_temperature_factsage(self, row):
        """FactSage优化版温度预测"""
        # 基础数据
        hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)
        hot_metal_mass = self.safe_convert(row['铁水'], 90)
        scrap_mass = self.safe_convert(row['废钢'], 20)
        blow_time = self.safe_convert(row['吹氧时间s'], 600)
        
        # 修正铁水成分
        composition = {
            'C': max(self.safe_convert(row['铁水C'], 4.2), 3.5),
            'Si': self.safe_convert(row['铁水SI'], 0.4),
            'Mn': self.safe_convert(row['铁水MN'], 0.17),
            'P': self.safe_convert(row['铁水P'], 0.13),
            'S': self.safe_convert(row['铁水S'], 0.03)
        }
        
        temp_k = hot_metal_temp + 273.15
        
        # 1. FactSage活度计算
        activity_coeffs = self.calculate_activity_coefficients(composition)
        
        # 2. 反应热计算（考虑活度系数）
        c_heat = (hot_metal_mass * composition['C'] * 0.85 / 100 * 
                 abs(self.factsage_thermo['delta_H_CO']) * activity_coeffs['gamma_C'])
        
        si_heat = (hot_metal_mass * composition['Si'] * 0.95 / 100 * 
                  abs(self.factsage_thermo['delta_H_SiO2']) * activity_coeffs['gamma_Si'])
        
        mn_heat = hot_metal_mass * composition['Mn'] * 0.80 / 100 * abs(self.factsage_thermo['delta_H_MnO'])
        
        p_heat = (hot_metal_mass * composition['P'] * 0.85 / 100 * 
                 abs(self.factsage_thermo['delta_H_P2O5']) * activity_coeffs['gamma_P'] / 2)
        
        total_reaction_heat = c_heat + si_heat + mn_heat + p_heat
        
        # 3. 动态供氧模型
        oxygen_intensity = self.safe_convert(row['累氧实际'], 5000) / blow_time * 3600
        optimal_oxygen = self.calculate_dynamic_oxygen_demand(composition, temp_k, blow_time)
        oxygen_efficiency = min(1.2, max(0.8, oxygen_intensity / optimal_oxygen))
        
        # 4. 底吹效应
        bottom_effects = self.model_bottom_blowing_effects(composition, oxygen_intensity)
        
        # 5. 脱磷优化
        deP_params = self.optimize_dephosphorization(composition, temp_k)
        
        # 6. 废钢熔化耗热
        scrap_heat = scrap_mass * 1150 * (1 - 0.1 * bottom_effects['heat_transfer_factor'])
        
        # 7. 净反应热
        net_heat = (total_reaction_heat * oxygen_efficiency * 
                   bottom_effects['reaction_rate_factor'] - scrap_heat)
        
        # 8. 温升计算
        total_steel = hot_metal_mass + scrap_mass
        temp_rise = net_heat / (total_steel * self.factsage_thermo['Cp_steel'])
        
        # 9. 热损失
        base_loss = 0.6 * blow_time / 60
        stirring_loss_reduction = 0.2 * bottom_effects['stirring_efficiency']
        actual_loss = base_loss * (1 - stirring_loss_reduction)
        
        # 10. 工艺参数修正
        lance_angle = self.safe_convert(row['最大角度'], 15)
        lance_effect = (lance_angle - 15) * 0.8
        
        flux_mass = self.safe_convert(row['石灰'], 4000) + self.safe_convert(row['白云石'], 700)
        flux_effect = (flux_mass - 4700) * 0.002
        
        # 11. 最终温度
        final_temp = (hot_metal_temp + temp_rise - actual_loss + 
                     lance_effect + flux_effect)
        
        # 12. 基于脱磷要求的温度约束
        min_temp_for_deP = 1580 + (deP_params['optimal_basicity'] - 2.8) * 20
        final_temp = max(final_temp, min_temp_for_deP)
        
        # 软约束
        if final_temp < 1500:
            final_temp = 1500 + (final_temp - 1500) * 0.2
        elif final_temp > 1850:
            final_temp = 1850 - (final_temp - 1850) * 0.2
        
        return {
            'predicted_temp': final_temp,
            'reaction_heat': total_reaction_heat,
            'oxygen_efficiency': oxygen_efficiency,
            'bottom_effects': bottom_effects,
            'dephosphorization': deP_params,
            'activity_coefficients': activity_coeffs
        }
    
    def calculate_dynamic_oxygen_demand(self, composition, temp_k, blow_time):
        """动态计算供氧需求"""
        decarb_demand = composition['C'] * self.oxygen_supply['decarb_demand_coeff']
        si_demand = composition['Si'] * self.oxygen_supply['si_demand_coeff']
        mn_demand = composition['Mn'] * self.oxygen_supply['mn_demand_coeff']
        p_demand = composition['P'] * self.oxygen_supply['p_demand_coeff']
        
        total_demand = decarb_demand + si_demand + mn_demand + p_demand
        temp_correction = 1 + self.oxygen_supply['temp_response_coeff'] * (temp_k - 1873) / 100
        time_factor = 1 - 0.3 * (blow_time / 600)
        
        return (self.oxygen_supply['base_intensity'] + total_demand) * temp_correction * time_factor
    
    def model_bottom_blowing_effects(self, composition, oxygen_intensity):
        """模拟底吹效应"""
        mass_transfer_enhancement = (1 + self.bottom_blowing['mass_transfer_coeff'] * 
                                   np.sqrt(oxygen_intensity / 500))
        
        reaction_rate_factor = (self.bottom_blowing['reaction_rate_enhance'] * 
                              mass_transfer_enhancement)
        
        heat_transfer_factor = (1 + self.bottom_blowing['heat_transfer_coeff'] * 
                              mass_transfer_enhancement)
        
        return {
            'mass_transfer_enhancement': mass_transfer_enhancement,
            'reaction_rate_factor': reaction_rate_factor,
            'heat_transfer_factor': heat_transfer_factor,
            'stirring_efficiency': self.bottom_blowing['stirring_efficiency']
        }
    
    def optimize_dephosphorization(self, composition, temp_k):
        """优化脱磷工艺参数"""
        log_K = self.dephosphorization['A_deP'] / temp_k + self.dephosphorization['B_deP']
        K_eq = 10 ** log_K
        
        optimal_basicity = (self.dephosphorization['optimal_basicity_base'] +
                           self.dephosphorization['basicity_temp_coeff'] * (temp_k - 1873) +
                           self.dephosphorization['basicity_p_coeff'] * composition['P'])
        
        optimal_log_pO2 = (self.dephosphorization['log_pO2_optimal'] +
                           self.dephosphorization['pO2_temp_coeff'] * (temp_k - 1873))
        
        activity_coeffs = self.calculate_activity_coefficients(composition)
        dephosphorization_efficiency = min(100, K_eq * optimal_basicity**2.5 * 
                                         activity_coeffs['gamma_P'] * 10**(optimal_log_pO2/2))
        
        return {
            'optimal_basicity': optimal_basicity,
            'optimal_log_pO2': optimal_log_pO2,
            'predicted_efficiency': dephosphorization_efficiency,
            'equilibrium_constant': K_eq
        }
    
    def train_ml_model(self, df):
        """训练机器学习模型"""
        print("🤖 正在训练机器学习模型...")
        
        # 准备训练数据
        X = []
        y = []
        
        for idx, row in df.iterrows():
            if pd.notna(row.get('钢水温度')) and self.safe_convert(row.get('钢水温度')) > 0:
                features = [
                    self.safe_convert(row['铁水温度'], 1350),
                    self.safe_convert(row['铁水'], 90),
                    self.safe_convert(row['废钢'], 20),
                    self.safe_convert(row['吹氧时间s'], 600),
                    max(self.safe_convert(row['铁水C'], 4.2), 3.5),
                    self.safe_convert(row['铁水SI'], 0.4),
                    self.safe_convert(row['铁水MN'], 0.17),
                    self.safe_convert(row['铁水P'], 0.13),
                    self.safe_convert(row['累氧实际'], 5000),
                    self.safe_convert(row['最大角度'], 15),
                    self.safe_convert(row['石灰'], 4000),
                    self.safe_convert(row['白云石'], 700)
                ]
                
                X.append(features)
                y.append(self.safe_convert(row['钢水温度']))
        
        X = np.array(X)
        y = np.array(y)
        
        if len(X) > 100:
            self.ml_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42
            )
            
            self.ml_model.fit(X, y)
            self.is_trained = True
            
            print(f"✅ 机器学习模型训练完成，训练样本数: {len(X)}")
        else:
            print("⚠️ 训练数据不足，跳过机器学习模型训练")

def main():
    """主函数：第二步模型训练"""
    print("=" * 60)
    print("第二步：FactSage优化版模型训练和数据处理")
    print("目标：修正数据问题，训练最佳状态模型")
    print("=" * 60)
    
    # 初始化训练器
    trainer = FactSageOptimalTrainer()
    
    # 加载和清理历史训练数据
    print("\n📚 加载历史训练数据...")
    train_df = trainer.load_and_clean_data('1-4521剔除重复20250514.xlsx')
    
    if train_df is None:
        print("❌ 历史数据加载失败")
        return False
    
    # 训练机器学习模型
    trainer.train_ml_model(train_df)
    
    # 验证模型性能（小样本测试）
    print("\n🧪 验证模型性能...")
    test_results = []
    
    for idx in range(min(50, len(train_df))):
        row = train_df.iloc[idx]
        if trainer.safe_convert(row.get('钢水温度')) > 0:
            try:
                result = trainer.predict_temperature_factsage(row)
                actual_temp = trainer.safe_convert(row['钢水温度'])
                temp_error = abs(result['predicted_temp'] - actual_temp)
                test_results.append(temp_error)
            except Exception as e:
                continue
    
    if test_results:
        mae = np.mean(test_results)
        accuracy_20c = sum(1 for e in test_results if e <= 20) / len(test_results) * 100
        
        print(f"✅ 模型性能验证完成:")
        print(f"   - 测试样本数: {len(test_results)}")
        print(f"   - 平均绝对误差: {mae:.1f}°C")
        print(f"   - ±20°C精度: {accuracy_20c:.1f}%")
        
        if accuracy_20c >= 50:
            print("🎯 模型性能达到预期，准备进行第五批数据预测")
        else:
            print("⚠️ 模型性能需要进一步优化")
    
    # 检查第五批测试数据
    print("\n📋 检查第五批测试数据...")
    test_df = trainer.load_and_clean_data('4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx')
    
    if test_df is not None:
        print(f"✅ 第五批测试数据准备就绪，共{len(test_df)}条记录")
        
        # 保存训练好的模型状态
        model_state = {
            'trainer': trainer,
            'train_df': train_df,
            'test_df': test_df,
            'performance': {
                'mae': mae if test_results else 0,
                'accuracy_20c': accuracy_20c if test_results else 0
            }
        }
        
        print("\n" + "=" * 60)
        print("✅ 第二步完成：模型训练和数据处理成功")
        print("📋 下一步：使用FactSage优化版进行第五批数据预测")
        print("=" * 60)
        
        return model_state
    else:
        print("❌ 第五批测试数据加载失败")
        return False

if __name__ == "__main__":
    result = main()
    if result:
        print("\n🎯 准备进入第三步：第五批数据预测")
    else:
        print("\n❌ 第二步失败，请检查数据和模型")
