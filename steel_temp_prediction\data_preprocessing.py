"""
Data preprocessing module for the steel temperature prediction model.
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import IsolationForest
from sklearn.cluster import DBSCAN
import logging
from typing import Dict, List, Tuple, Union, Any

from steel_temp_prediction.config import TARGET, STEEL_GRADE_GROUPS, ANOMALY_DETECTION

# Set up logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
logger = logging.getLogger(__name__)

def load_data(train_path: str, test_path: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Load training and test data from Excel files.

    Args:
        train_path: Path to training data Excel file
        test_path: Path to test data Excel file

    Returns:
        Tuple of (training_data, test_data) as pandas DataFrames
    """
    logger.info(f"Loading training data from {train_path}")
    train_df_raw = pd.read_excel(train_path, sheet_name=0)
    test_df_raw = pd.read_excel(test_path, sheet_name=0)

    # Log raw training data info and head
    logger.info("Raw training data loaded. Info:")
    try:
        import io
        buffer = io.StringIO()
        train_df_raw.info(buf=buffer)
        logger.info(buffer.getvalue())
        logger.info("Raw training data head (first 5 rows):")
        logger.info(train_df_raw.head().to_string())
    except Exception as e:
        logger.error(f"Error logging raw training data details: {e}")

    logger.info(f"Training data shape: {train_df_raw.shape}")
    logger.info(f"Test data shape: {test_df_raw.shape}")

    return train_df_raw, test_df_raw

def clean_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Clean the data by handling missing values and outliers.

    Args:
        df: Input DataFrame

    Returns:
        Cleaned DataFrame
    """
    logger.info("Cleaning data...")

    # Make a copy to avoid modifying the original
    df_clean = df.copy()

    # Check for missing values
    missing_values = df_clean.isnull().sum()
    logger.info(f"Missing values before cleaning:\n{missing_values[missing_values > 0]}")

    # Fill missing values with appropriate methods
    # For numerical columns, use median
    num_cols = df_clean.select_dtypes(include=['float64', 'int64']).columns
    for col in num_cols:
        if df_clean[col].isnull().sum() > 0:
            df_clean[col] = df_clean[col].fillna(df_clean[col].median())

    # For categorical columns, use mode
    cat_cols = df_clean.select_dtypes(include=['object']).columns
    for col in cat_cols:
        if df_clean[col].isnull().sum() > 0:
            df_clean[col] = df_clean[col].fillna(df_clean[col].mode()[0])

    # Check if all missing values are handled
    missing_after = df_clean.isnull().sum()
    logger.info(f"Missing values after cleaning:\n{missing_after[missing_after > 0]}")

    # For test data, we might have columns that are expected to be missing (like target variables)
    # Let's exclude these from outlier detection
    exclude_cols = ['钢水温度', '钢水SI', '钢水S', '钢水P', '钢水MN', '钢水C']
    outlier_cols = [col for col in num_cols if col not in exclude_cols and df_clean[col].isnull().sum() == 0]

    if len(outlier_cols) > 0:
        # Handle outliers using IsolationForest
        logger.info("Detecting outliers using Isolation Forest...")

        # Only use numerical columns without missing values for outlier detection
        X = df_clean[outlier_cols].copy()

        # Initialize and fit the isolation forest
        iso_forest = IsolationForest(
            contamination=ANOMALY_DETECTION['isolation_forest']['contamination'],
            random_state=ANOMALY_DETECTION['isolation_forest']['random_state']
        )
        outliers = iso_forest.fit_predict(X)

        # Mark outliers (returns -1 for outliers, 1 for inliers)
        df_clean['is_outlier'] = (outliers == -1)

        # Log the number of detected outliers
        n_outliers = np.sum(df_clean['is_outlier'])
        logger.info(f"Detected {n_outliers} outliers ({n_outliers/len(df_clean)*100:.2f}%)")
    else:
        logger.info("Skipping outlier detection due to missing values in all numerical columns")
        df_clean['is_outlier'] = False



    # Instead of removing outliers, we'll keep them but mark them for later analysis

    return df_clean

def classify_steel_grades(df: pd.DataFrame) -> pd.DataFrame:
    """
    Classify steel grades based on composition.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added steel grade classification
    """
    logger.info("Classifying steel grades...")

    # Make a copy to avoid modifying the original
    df_classified = df.copy()

    # Initialize steel grade column
    df_classified['steel_grade_class'] = 'unknown'

    # Classify based on carbon content
    if '铁水C' in df_classified.columns:
        mask_low_carbon = df_classified['铁水C'] <= STEEL_GRADE_GROUPS['low_carbon']['C_max']
        mask_high_carbon = df_classified['铁水C'] >= STEEL_GRADE_GROUPS['high_carbon']['C_min']
        mask_medium_carbon = ~(mask_low_carbon | mask_high_carbon)

        df_classified.loc[mask_low_carbon, 'steel_grade_class'] = 'low_carbon'
        df_classified.loc[mask_medium_carbon, 'steel_grade_class'] = 'medium_carbon'
        df_classified.loc[mask_high_carbon, 'steel_grade_class'] = 'high_carbon'

    # Further classify based on alloy content
    if '铁水SI' in df_classified.columns and '铁水MN' in df_classified.columns:
        mask_high_alloy = (
            (df_classified['铁水SI'] >= STEEL_GRADE_GROUPS['high_alloy']['Si_min']) |
            (df_classified['铁水MN'] >= STEEL_GRADE_GROUPS['high_alloy']['Mn_min'])
        )

        # Update classification for high alloy steels
        df_classified.loc[mask_high_alloy, 'steel_grade_class'] += '_high_alloy'

    # Count the number of samples in each class
    grade_counts = df_classified['steel_grade_class'].value_counts()
    logger.info(f"Steel grade classification counts:\n{grade_counts}")

    return df_classified

def normalize_features(train_df: pd.DataFrame, test_df: pd.DataFrame,
                      exclude_cols: List[str] = None) -> Tuple[pd.DataFrame, pd.DataFrame, Any]:
    """
    Normalize numerical features using RobustScaler.

    Args:
        train_df: Training DataFrame
        test_df: Test DataFrame
        exclude_cols: Columns to exclude from normalization

    Returns:
        Tuple of (normalized_train_df, normalized_test_df, scaler)
    """
    logger.info("Normalizing features...")

    # Make copies to avoid modifying the originals
    train_norm = train_df.copy()
    test_norm = test_df.copy()

    if exclude_cols is None:
        exclude_cols = []

    # Add non-numeric columns to exclude list
    for col in train_df.columns:
        if train_df[col].dtype == 'object' or col in exclude_cols:
            if col not in exclude_cols:
                exclude_cols.append(col)

    # Get columns to normalize
    cols_to_normalize = [col for col in train_df.columns if col not in exclude_cols]

    # Initialize and fit the scaler on training data
    scaler = RobustScaler()
    train_norm[cols_to_normalize] = scaler.fit_transform(train_df[cols_to_normalize])

    # Transform test data using the same scaler
    test_norm[cols_to_normalize] = scaler.transform(test_df[cols_to_normalize])

    logger.info(f"Normalized {len(cols_to_normalize)} features")

    return train_norm, test_norm, scaler

def preprocess_data(train_path: str, test_path: str, target_col: str = TARGET) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Main data preprocessing function."""
    logger.info("Starting data preprocessing...")

    # Load data directly using read_excel as it seems the load_data helper was causing confusion
    logger.info(f"Loading training data from {train_path}")
    train_df_raw = pd.read_excel(train_path, sheet_name=0)
    logger.info(f"Loading test data from {test_path}")
    test_df_raw = pd.read_excel(test_path, sheet_name=0)

    # Log raw training data info and head immediately after loading
    logger.info("--- Raw Training Data Exploration Start ---")
    logger.info("Raw training data .info():")
    print("--- Raw Training Data Exploration Start (Console Output) ---")
    print("Raw training data .info():")
    try:
        import io
        buffer = io.StringIO()
        train_df_raw.info(buf=buffer)
        log_info_output = buffer.getvalue()
        logger.info(log_info_output)
        print(log_info_output)

        logger.info("Raw training data .head(5):")
        print("Raw training data .head(5):")
        log_head_output = train_df_raw.head().to_string()
        logger.info(log_head_output)
        print(log_head_output)
    except Exception as e:
        logger.error(f"Error logging raw training data details: {e}")
        print(f"Error printing raw training data details to console: {e}")
    logger.info("--- Raw Training Data Exploration End ---")
    print("--- Raw Training Data Exploration End (Console Output) ---")

    # --- Additional Data Exploration: Object Columns Value Counts ---
    print("\n--- Raw Training Data: Object Columns Value Counts (Console Output) ---")
    object_cols = train_df_raw.select_dtypes(include=['object']).columns
    for col in object_cols:
        print(f"\nValue counts for object column: {col}")
        try:
            print(train_df_raw[col].value_counts(dropna=False).to_string()) # dropna=False to see NaNs if any
        except Exception as e:
            print(f"Error printing value_counts for {col}: {e}")
    print("--- Raw Training Data: Object Columns Value Counts End (Console Output) ---\n")

    # --- Additional Data Exploration: Numeric Columns Describe ---
    print("\n--- Raw Training Data: Numeric Columns Describe (Console Output) ---")
    try:
        # Include both float64 and int64
        print(train_df_raw.describe(include=[np.number]).to_string())
    except Exception as e:
        print(f"Error printing describe for numeric columns: {e}")
    print("--- Raw Training Data: Numeric Columns Describe End (Console Output) ---\n")

    # --- Handle Rare Categories for '装入制度' and '钢种' ---
    print("\n--- Handling Rare Categories (Applied to both Train and Test Raw Data) (Console Output) ---")
    rare_threshold_percentage = 0.005 # 0.5%
    
    dataframes_to_process = {
        'train': train_df_raw,
        # We will apply the mapping derived from train to test
    }
    # Test data will be processed separately using mapping from train

    for col_name in ['装入制度', '钢种']:
        if col_name in train_df_raw.columns:
            print(f"\nProcessing rare categories for column: {col_name}")
            
            # --- Training Data: Identify and Transform ---
            print(f"Value counts for {col_name} in TRAINING data BEFORE rare handling:")
            try:
                print(train_df_raw[col_name].value_counts(dropna=False).to_string())
            except Exception as e:
                print(f"Error printing initial value_counts for {col_name} in train: {e}")

            train_value_counts = train_df_raw[col_name].value_counts(dropna=False)
            total_train_samples = len(train_df_raw)
            
            # Identify rare categories based on TRAINING data
            rare_categories_train = train_value_counts[train_value_counts < total_train_samples * rare_threshold_percentage].index.tolist()
            new_category_name = f"{col_name}_Rare" # Consistent name for the new category

            if rare_categories_train:
                print(f"Identified rare categories in TRAINING data for {col_name} (less than {rare_threshold_percentage*100:.1f}% frequency): {rare_categories_train}")
                # Replace rare categories in train_df_raw
                train_df_raw[col_name] = train_df_raw[col_name].replace(rare_categories_train, new_category_name)
                
                print(f"Value counts for {col_name} in TRAINING data AFTER rare handling:")
                try:
                    print(train_df_raw[col_name].value_counts(dropna=False).to_string())
                except Exception as e:
                    print(f"Error printing updated value_counts for {col_name} in train: {e}")
            else:
                print(f"No rare categories found in TRAINING data for {col_name} based on the threshold.")

            # --- Test Data: Apply Transformation based on Training Data ---
            if col_name in test_df_raw.columns:
                print(f"\nValue counts for {col_name} in TEST data BEFORE applying train-derived rare handling:")
                try:
                    print(test_df_raw[col_name].value_counts(dropna=False).to_string())
                except Exception as e:
                    print(f"Error printing initial value_counts for {col_name} in test: {e}")

                if rare_categories_train: # Only apply if rare categories were found in train
                    # For test data, replace categories that were deemed rare in TRAIN set
                    # Also, any category in test that was NOT in train at all should also be mapped to the new_category_name
                    # to prevent new unknown categories during one-hot encoding later.
                    all_train_categories = train_value_counts.index.tolist() # Original categories before 'Rare' merge
                    # Categories in test that are either rare in train OR not present in train (before rare merging) get mapped to new_category_name
                    test_categories_to_replace = [cat for cat in test_df_raw[col_name].unique() if (cat in rare_categories_train) or (cat not in all_train_categories and cat != new_category_name)]
                    
                    if test_categories_to_replace:
                         print(f"Categories in TEST data for {col_name} to be replaced with '{new_category_name}' (either rare in train or not in train): {test_categories_to_replace}")
                         test_df_raw[col_name] = test_df_raw[col_name].replace(test_categories_to_replace, new_category_name)
                    else:
                        print(f"No categories in TEST data for {col_name} needed replacement based on train-derived rare/unknown logic.")

                print(f"Value counts for {col_name} in TEST data AFTER applying train-derived rare handling:")
                try:
                    print(test_df_raw[col_name].value_counts(dropna=False).to_string())
                except Exception as e:
                    print(f"Error printing updated value_counts for {col_name} in test: {e}")
            else:
                logger.warning(f"Column '{col_name}' not found in test_df_raw for applying rare category handling.")
        else:
            logger.warning(f"Column '{col_name}' not found in train_df_raw for rare category handling.")
    print("--- Handling Rare Categories End (Console Output) ---\n")

    # Clean column names (original logic)
    train_df = train_df_raw.copy() # Work on a copy
    test_df = test_df_raw.copy() # Work on a copy

    train_df.columns = [col.strip().replace(' ', '') for col in train_df.columns]
    test_df.columns = [col.strip().replace(' ', '') for col in test_df.columns]

    # Clean data
    train_df_clean = clean_data(train_df)
    test_df_clean = clean_data(test_df)

    # Explicitly convert potentially problematic columns to numeric before classification
    cols_to_convert_numeric = ['铁水SI', '铁水MN', '铁水C'] # Added 铁水C as it's also used in classification
    for df in [train_df_clean, test_df_clean]:
        for col in cols_to_convert_numeric:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                # Re-run median fill for these specific columns if NaNs were introduced by 'coerce'
                if df[col].isnull().any():
                    logger.info(f"NaNs introduced in '{col}' after pd.to_numeric. Refilling with median.")
                    df[col] = df[col].fillna(df[col].median())
            else:
                logger.warning(f"Column '{col}' not found in dataframe during numeric conversion step.")


    # Classify steel grades
    train_df_classified = classify_steel_grades(train_df_clean)
    test_df_classified = classify_steel_grades(test_df_clean)

    # For now, we'll return the classified data without normalization
    # Normalization will be applied after feature engineering

    return train_df_classified, test_df_classified
