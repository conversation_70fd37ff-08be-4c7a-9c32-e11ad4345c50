"""
使用预训练模型预测钢水温度。
此脚本加载已训练好的模型，对"4523-4905筛选后完整数据20250519（第五批测试数据）"进行钢水温度预测。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
import pickle
import joblib
import json
from typing import Dict, List, Tuple, Any, Optional

# 确保模块可以被导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='prediction_results.log'
)
logger = logging.getLogger(__name__)
# 同时输出到控制台
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logger.addHandler(console)

# 导入所需模块
try:
    from steel_temp_prediction.feature_engineering import engineer_all_features
    from steel_temp_prediction.model_development import SequentialThinkingModel
except ImportError:
    logger.error("ImportError: Could not import from steel_temp_prediction. Trying direct imports.")
    try:
        from feature_engineering import engineer_all_features
        from model_development import SequentialThinkingModel
    except ImportError as e_direct:
        logger.error(f"Direct import failed as well: {e_direct}")
        sys.exit("Essential modules could not be imported. Exiting.")

def load_model(model_path: str) -> Any:
    """
    加载预训练模型。
    
    Args:
        model_path: 模型文件路径
        
    Returns:
        加载的模型
    """
    logger.info(f"加载预训练模型: {model_path}")
    try:
        # 尝试使用joblib加载
        try:
            model = joblib.load(model_path)
            logger.info("使用joblib成功加载模型")
        except:
            # 如果joblib失败，尝试使用pickle
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            logger.info("使用pickle成功加载模型")
        
        return model
    except Exception as e:
        logger.error(f"加载模型时出错: {str(e)}")
        raise

def load_test_data(file_path: str) -> pd.DataFrame:
    """
    加载测试数据。
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        加载的DataFrame
    """
    logger.info(f"加载测试数据: {file_path}")
    try:
        df = pd.read_excel(file_path)
        logger.info(f"成功加载数据，共 {df.shape[0]} 行, {df.shape[1]} 列")
        
        # 输出列名，以便检查
        logger.info(f"数据列名: {df.columns.tolist()}")
        
        # YOLO FIX: Rename column for consistency if it exists in test_data
        if '炉子最大倾角' in df.columns and '最大角度' not in df.columns:
            logger.info("检测到预测数据中使用 '炉子最大倾角'，将其重命名为 '最大角度' 以匹配训练特征名。")
            df.rename(columns={'炉子最大倾角': '最大角度'}, inplace=True)
        elif '炉子最大倾角' in df.columns and '最大角度' in df.columns:
            logger.warning("警告: 预测数据中同时存在 '炉子最大倾角' 和 '最大角度' 列。优先使用 '最大角度'。")
            # 可以选择删除 '炉子最大倾角' df.drop(columns=['炉子最大倾角'], inplace=True)
        
        return df
    except Exception as e:
        logger.error(f"加载数据时出错: {str(e)}")
        raise

def preprocess_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    预处理测试数据。
    
    Args:
        df: 原始DataFrame
        
    Returns:
        预处理后的DataFrame
    """
    logger.info("预处理测试数据...")
    
    # 复制数据，避免修改原始数据
    df_clean = df.copy()
    
    # 检查并处理缺失值
    missing_values = df_clean.isnull().sum()
    if missing_values.any():
        logger.info("处理缺失值:")
        for col in missing_values[missing_values > 0].index:
            missing_count = missing_values[col]
            missing_percent = missing_count / len(df_clean) * 100
            logger.info(f"  列 '{col}' 有 {missing_count} 缺失值 ({missing_percent:.2f}%)")
            
            # 对数值列使用中位数填充
            if df_clean[col].dtype in ['int64', 'float64']:
                df_clean[col] = df_clean[col].fillna(df_clean[col].median())
            # 对分类列使用众数填充
            else:
                df_clean[col] = df_clean[col].fillna(df_clean[col].mode()[0] if not df_clean[col].mode().empty else "未知")
    
    # 检查列名是否匹配
    required_columns = [
        '铁水', '铁水温度', '铁水SI', '铁水MN', '铁水P', '铁水C',
        '废钢', '钢水SI', '钢水MN', '钢水P',
        '石灰', '白云石'
    ]
    
    # 列名映射，用于标准化列名（将不同数据集可能的列名变体映射到标准列名）
    column_mapping = {
        '累氧': '累氧实际',
        '累氧实际': '累氧实际',
        '氧气消耗量': '累氧实际',
        '吹氧时间': '吹氧时间s',
        '吹氧时间s': '吹氧时间s',
        '铁水硅': '铁水SI',
        '铁水锰': '铁水MN',
        '铁水磷': '铁水P',
        '铁水碳': '铁水C',
        '钢水硅': '钢水SI',
        '钢水锰': '钢水MN',
        '钢水磷': '钢水P',
        '渣中FeO': '炉渣FeO'
    }
    
    # 应用列名映射
    df_clean = df_clean.rename(columns={k: v for k, v in column_mapping.items() if k in df_clean.columns})
    
    # 检查是否缺少所需列
    missing_columns = [col for col in required_columns if col not in df_clean.columns]
    if missing_columns:
        logger.warning(f"缺少以下列: {missing_columns}")
        # 为缺少的列填充中位数或常见值（根据实际数据情况调整）
        for col in missing_columns:
            if col in ['铁水SI', '铁水MN', '铁水P', '铁水C', '钢水SI', '钢水MN', '钢水P']:
                df_clean[col] = 0.1  # 默认值，根据实际情况调整
            elif col in ['铁水温度']:
                df_clean[col] = 1350  # 默认值，根据实际情况调整
            elif col in ['铁水', '废钢']:
                df_clean[col] = 80000  # 默认值，根据实际情况调整
            elif col in ['石灰', '白云石']:
                df_clean[col] = 3000  # 默认值，根据实际情况调整
    
    logger.info(f"预处理完成，处理后的数据形状: {df_clean.shape}")
    return df_clean

def apply_feature_engineering(df: pd.DataFrame) -> pd.DataFrame:
    """
    对数据应用特征工程。
    现在此函数仅调用 engineer_all_features 并返回其结果。
    特征对齐和选择将在 clean_features_for_prediction 中进行。
    
    Args:
        df: 预处理后的DataFrame
        
    Returns:
        特征工程后的DataFrame
    """
    logger.info("进行特征工程 (调用 engineer_all_features)...")
    
    try:
        # 与 train_model.py 中的调用保持一致，使用 engineer_all_features 的默认启用开关
        # 如果训练时显式传递了这些开关，这里也需要对应修改
        df_featured = engineer_all_features(
            df, 
            enable_slag=True, # 假设与训练时一致 (默认)
            enable_process_params=True, # 假设与训练时一致 (默认)
            enable_time_series=False,  # 假设与训练时一致 (若训练时启用了时间序列，这里需改为True并配置相关ts_参数)
            enable_interactions=True, # 假设与训练时一致 (默认)
            enable_physicochem_props=True, # 假设与训练时一致 (默认)
            enable_advanced_slag=True, # 假设与训练时一致 (默认) -> 但实际效果是禁用的，因为其内部调用被注释
            enable_lance_dynamics=True, # 假设与训练时一致 (默认)
            interactions_poly_config={'degree': 2, 'target_cols': []} # 与训练时一致
        )
        logger.info(f"特征工程完成，输出特征数量: {len(df_featured.columns)}")
        logger.info(f"特征工程后列名示例: {df_featured.columns[:20].tolist()}...")
        return df_featured
    except Exception as e:
        logger.error(f"特征工程时出错: {str(e)}")
        raise

def clean_features_for_prediction(df: pd.DataFrame, expected_actual_feature_list: List[str]) -> pd.DataFrame:
    """
    清洗特征数据以进行预测，处理inf, NaN和极端值。
    此函数确保返回的DataFrame包含与 expected_actual_feature_list (从JSON文件加载的实际特征名) 完全相同的列，并按相同顺序排列。
    
    Args:
        df: 特征工程后的DataFrame
        expected_actual_feature_list: 模型训练时使用的实际特征列名列表 (从JSON加载)
        
    Returns:
        清洗后用于预测的DataFrame，列与expected_actual_feature_list一致。
    """
    logger.info(f"清洗特征数据 (处理inf, NaN, 极端值)，确保返回 {len(expected_actual_feature_list)} 个期望的实际特征名...")
    logger.info(f"期望的实际特征名 (前10): {expected_actual_feature_list[:10]}...")
    
    X_aligned = pd.DataFrame(index=df.index) # 创建一个新的DataFrame以保证顺序和完整性

    # 确保所有期望的列都存在，如果缺少则用0填充，并按期望顺序排列
    missing_in_df_featured = []
    for col_name in expected_actual_feature_list:
        if col_name in df.columns:
            X_aligned[col_name] = df[col_name]
        else:
            X_aligned[col_name] = 0  # 如果特征工程后缺失，则用0填充
            missing_in_df_featured.append(col_name)
    
    if missing_in_df_featured:
        logger.warning(f"清洗阶段: 以下 {len(missing_in_df_featured)} 个期望特征在特征工程后的数据中缺失，已用0填充:")
        logger.warning(f"  缺失列表: {missing_in_df_featured[:10]}...")

    # 此时 X_aligned 的列名和顺序与 expected_actual_feature_list 完全一致
    X = X_aligned 

    # 处理无穷大值 (替换为NaN，之后用中位数填充)
    X.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 处理NaN值 (使用中位数填充，也可以考虑其他策略如0填充或均值填充)
    # 注意：这里假设所有特征都是数值型，如果存在非数值型且有NaN，需要额外处理
    # 在我们的流程中，engineer_all_features 和之前的预处理应该已经处理了大部分类型问题
    cols_with_nan = X.isnull().any()
    if cols_with_nan.any():
        logger.info("清洗阶段: 以下列存在NaN值，将用中位数填充:")
        for col_name in cols_with_nan[cols_with_nan].index:
            median_val = X[col_name].median()
            if pd.isna(median_val): # 如果中位数本身是NaN (可能列全是NaN)
                median_val = 0 # Fallback to 0
                logger.warning(f"  列 '{col_name}' 的中位数也是NaN，将用0填充。")
            X[col_name].fillna(median_val, inplace=True)
            logger.info(f"  列 '{col_name}' 已用中位数 {median_val:.4f} 填充。")

    # 再次检查列数和列名是否完全匹配 (理论上此时应已对齐)
    if len(X.columns) != len(expected_actual_feature_list) or not all(X.columns == expected_actual_feature_list):
        logger.error(f"清洗后特征列与期望不符。得到 {len(X.columns)} 列 (名称: {X.columns.tolist()[:5]}...)，期望 {len(expected_actual_feature_list)} 列 (名称: {expected_actual_feature_list[:5]}...)。")
        # 可以选择抛出错误或尝试强制对齐，但此时应已对齐
        # 强制重排 (如果前面的逻辑正确，这步应该不需要)
        try:
            X = X[expected_actual_feature_list]
            logger.warning("进行了强制特征重排以匹配期望列表。")
        except KeyError as e:
            logger.error(f"强制特征重排失败: {e}。这表明期望的特征在对齐后仍然缺失。")
            raise ValueError("清洗后特征与期望严重不符，无法继续。")

    logger.info(f"特征清洗完成。返回的特征数量: {len(X.columns)}")
    return X

def predict_and_save_results(model: Any, X_actual_names: pd.DataFrame, original_df: pd.DataFrame, output_path: str, actual_feature_names_list: List[str], y_actual_test: Optional[pd.Series] = None) -> pd.DataFrame:
    """
    使用模型进行预测并保存结果。
    
    Args:
        model: 预训练模型
        X_actual_names: 用于预测的特征DataFrame，列名为实际特征名 (与actual_feature_names_list一致)
        original_df: 原始数据
        output_path: 输出文件路径
        actual_feature_names_list: 训练时使用的实际特征名列表 (用于验证和重命名)
        y_actual_test: 可选，测试数据中的实际目标值 Series，用于评估
        
    Returns:
        包含预测结果的DataFrame
    """
    logger.info("使用模型进行预测...")
    
    if len(X_actual_names.columns) != len(actual_feature_names_list) or not all(X_actual_names.columns == actual_feature_names_list):
        logger.error(f"传递给 predict_and_save_results 的 X_actual_names 与期望的 {len(actual_feature_names_list)} 个实际特征名不符。")
        logger.error(f"  X_actual_names.columns (len {len(X_actual_names.columns)}): {X_actual_names.columns.tolist()[:5]}...")
        logger.error(f"  Expected (len {len(actual_feature_names_list)}): {actual_feature_names_list[:5]}...")
        raise ValueError("传递给 predict_and_save_results 的 X_actual_names 与期望的实际特征集不符")

    X_to_predict_generic_names = X_actual_names.copy()
    X_to_predict_generic_names.columns = [f'feature_{i}' for i in range(len(actual_feature_names_list))]
    logger.info(f"输入特征已重命名为通用名 (如 feature_0, ...)。数量: {len(X_to_predict_generic_names.columns)}")

    is_sequential_model = False
    try:
        if 'SequentialThinkingModel' not in globals():
            try:
                from steel_temp_prediction.model_development import SequentialThinkingModel as STM_local
                if isinstance(model, STM_local): is_sequential_model = True
            except ImportError:
                try:
                    from model_development import SequentialThinkingModel as STM_local_alt
                    if isinstance(model, STM_local_alt): is_sequential_model = True
                except ImportError: logger.warning("无法导入 SequentialThinkingModel 进行类型检查。")
        elif isinstance(model, SequentialThinkingModel):
            is_sequential_model = True
    except Exception as e_type_check: logger.warning(f"检查模型类型时出错: {e_type_check}")

    try:
        logger.info("--- Features being fed to model.predict() (X_to_predict_generic_names) ---")
        logger.info(f"Shape of X_to_predict_generic_names: {X_to_predict_generic_names.shape}")
        describe_output = X_to_predict_generic_names.describe().to_string()
        logger.info(describe_output)
        sample_rows_output = X_to_predict_generic_names.head().to_string()
        logger.info(sample_rows_output)
        predictions = model.predict(X_to_predict_generic_names)
    except ValueError as ve:
        logger.error(f"模型预测时发生 ValueError: {ve}")
        generic_feature_names_example = [f'feature_{i}' for i in range(len(actual_feature_names_list))]
        logger.error(f"  模型期望 (内部XGBoost等): {generic_feature_names_example[:15]}... (共{len(generic_feature_names_example)})")
        logger.error(f"  实际提供 (DataFrame.columns): {X_to_predict_generic_names.columns.tolist()[:15]}... (共{len(X_to_predict_generic_names.columns)}) ")
        raise
    except Exception as e:
        logger.error(f"模型预测时发生其他错误: {e}")
        raise
        
    logger.info(f"成功获得预测结果，数量: {len(predictions)}")
    
    results_df = original_df.copy()
    results_df['预测钢水温度'] = predictions
    
    if y_actual_test is not None:
        logger.info(f"测试数据中找到实际钢水温度列，进行评估。")
        results_df['实际钢水温度'] = y_actual_test # 使用从main函数传递过来的y_actual_test
        
        mae = np.mean(np.abs(results_df['实际钢水温度'] - results_df['预测钢水温度']))
        rmse = np.sqrt(np.mean((results_df['实际钢水温度'] - results_df['预测钢水温度'])**2))
        hit_rate_10 = np.mean(np.abs(results_df['实际钢水温度'] - results_df['预测钢水温度']) <= 10) * 100
        hit_rate_15 = np.mean(np.abs(results_df['实际钢水温度'] - results_df['预测钢水温度']) <= 15) * 100
        hit_rate_20 = np.mean(np.abs(results_df['实际钢水温度'] - results_df['预测钢水温度']) <= 20) * 100
        
        logger.info("测试集预测评估结果:")
        logger.info(f"  平均绝对误差 (MAE): {mae:.2f}°C")
        logger.info(f"  均方根误差 (RMSE): {rmse:.2f}°C")
        logger.info(f"  命中率 (±10°C): {hit_rate_10:.2f}%")
        logger.info(f"  命中率 (±15°C): {hit_rate_15:.2f}%")
        logger.info(f"  命中率 (±20°C): {hit_rate_20:.2f}%")

        plt.figure(figsize=(10, 6))
        plt.scatter(results_df['实际钢水温度'], results_df['预测钢水温度'], alpha=0.5)
        plt.plot([results_df['实际钢水温度'].min(), results_df['实际钢水温度'].max()], 
                 [results_df['实际钢水温度'].min(), results_df['实际钢水温度'].max()], 
                 'k--', lw=2)
        plt.xlabel(f"实际钢水温度") # 修正标签
        plt.ylabel("预测钢水温度")
        plt.title("预测值 vs 实际值 (新模型 v2)")
        
        results_dir = os.path.join(os.path.dirname(output_path), "results")
        os.makedirs(results_dir, exist_ok=True)
        plot_filename = os.path.join(results_dir, "prediction_vs_actual_v2.png")
        try:
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            logger.info(f"预测对比图已保存到: {plot_filename}")
        except Exception as e_plot:
            logger.error(f"保存预测对比图时出错: {e_plot}")
        plt.close()
    else:
        logger.info(f"测试数据中未找到实际钢水温度列，不进行评估。")
        
    try:
        results_df.to_excel(output_path, index=False)
        logger.info(f"预测结果已保存到: {output_path}")
    except Exception as e_save:
        logger.error(f"保存预测结果到Excel时出错: {e_save}")
        
    return results_df

def main():
    """
    主函数，处理预测流程。
    """
    logger.info("开始钢水温度预测 (使用新训练的模型 v2)")
    
    project_root_dir = os.path.dirname(os.path.abspath(__file__))

    test_data_filename = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"
    test_data_file = os.path.join(project_root_dir, test_data_filename)
    logger.info(f"测试数据文件路径: {test_data_file}")
    
    if not os.path.exists(test_data_file):
        alt_test_data_file_cwd = test_data_filename
        if os.path.exists(alt_test_data_file_cwd):
            test_data_file = alt_test_data_file_cwd
            logger.info(f"在当前工作目录中找到测试数据文件: {test_data_file}")
        else:
            logger.error(f"测试数据文件未找到: {test_data_filename} (检查路径: {test_data_file} 或 CWD '{alt_test_data_file_cwd}')")
            return

    model_path_v2 = os.path.join(project_root_dir, "results", "sequential_thinking_model_v2.pkl")
    model_path_v1 = os.path.join(project_root_dir, "results", "sequential_thinking_model.pkl")
    
    model_to_load = None
    if os.path.exists(model_path_v2):
        model_to_load = model_path_v2
        logger.info(f"优先加载新训练的模型: {model_to_load}")
    elif os.path.exists(model_path_v1):
        model_to_load = model_path_v1
        logger.warning(f"未找到新模型 v2 ({model_path_v2})，加载旧模型: {model_to_load}")
    else:
        logger.error(f"模型文件未找到。检查路径: {model_path_v2} 或 {model_path_v1}")
        return

    output_filename = "predicted_steel_temperatures_v2.xlsx"
    output_file = os.path.join(project_root_dir, "results", output_filename)
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    actual_features_list_path = os.path.join(project_root_dir, "results", "final_model_feature_names.json")
    if not os.path.exists(actual_features_list_path):
        logger.error(f"关键文件 final_model_feature_names.json 未在 '{os.path.join(project_root_dir, "results")}' 中找到。请先运行训练脚本。")
        return
    try:
        with open(actual_features_list_path, 'r', encoding='utf-8') as f:
            final_actual_feature_names = json.load(f)
        logger.info(f"成功加载实际特征名列表 ({len(final_actual_feature_names)} 个特征) 从: {actual_features_list_path}")
    except Exception as e_json:
        logger.error(f"加载 final_model_feature_names.json 时出错: {e_json}")
        return

    logger.info(f"预测结果将保存到: {output_file}")
    
    TARGET_COL = '钢水温度' # 定义目标列名
    y_actual_test = None # 初始化实际目标值变量

    try:
        model = load_model(model_to_load)
        df_test_original = load_test_data(test_data_file)
        
        # 检查测试数据中是否存在目标列
        if TARGET_COL in df_test_original.columns:
            logger.info(f"在测试数据中找到目标列 '{TARGET_COL}'。将其分离用于后续评估。")
            y_actual_test = df_test_original[TARGET_COL].copy()
            df_test_for_processing = df_test_original.drop(columns=[TARGET_COL])
        else:
            logger.info(f"测试数据中未找到目标列 '{TARGET_COL}'。仅进行预测。")
            df_test_for_processing = df_test_original.copy()
            
        df_test_processed = preprocess_data(df_test_for_processing)
        df_featured = apply_feature_engineering(df_test_processed)
        X_predict_actual_names = clean_features_for_prediction(df_featured, final_actual_feature_names)
        
        # 修改 predict_and_save_results 的调用，传入 y_actual_test
        predict_and_save_results(model, X_predict_actual_names, df_test_original, output_file, final_actual_feature_names, y_actual_test)
        
        logger.info("钢水温度预测流程完成。")
        
    except Exception as e:
        logger.error(f"预测流程中发生错误: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()