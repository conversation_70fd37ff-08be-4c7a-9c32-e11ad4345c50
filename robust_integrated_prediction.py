"""
稳健的集成钢水温度预测系统
基于FactSage、炉渣预测、转炉数学模型的实用解决方案
目标：95%命中率
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
import warnings
from typing import Dict, List, Tuple, Union, Any
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet, BayesianRidge
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"robust_integrated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RobustIntegratedPredictor:
    """稳健的集成预测器"""

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.feature_names = []

        # FactSage热力学常数
        self.factsage_constants = {
            'CaO_formation_enthalpy': -635.1,  # kJ/mol
            'SiO2_formation_enthalpy': -910.7,
            'FeO_formation_enthalpy': -272.0,
            'MgO_formation_enthalpy': -601.6,
        }

        # 专家规则
        self.expert_rules = {
            'high_silicon_correction': 15,
            'high_carbon_correction': 10,
            'late_addition_penalty': -0.08,
            'slag_basicity_optimal': 2.8,
        }

    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default

    def create_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建高级特征"""
        logger.info("创建高级特征")

        df_advanced = df.copy()

        # 1. FactSage优化特征
        for idx, row in df_advanced.iterrows():
            try:
                # 基础成分
                cao_content = self.safe_convert(row.get('slag_CaO_percent', 45)) / 100
                sio2_content = self.safe_convert(row.get('slag_SiO2_percent', 16)) / 100
                feo_content = self.safe_convert(row.get('slag_FeO_percent', 20)) / 100

                # 炉渣碱度
                basicity = cao_content / (sio2_content + 1e-6)
                df_advanced.loc[idx, 'factsage_basicity'] = basicity

                # 活度估算 (简化)
                cao_activity = cao_content * np.exp(-0.5 * sio2_content)
                df_advanced.loc[idx, 'factsage_cao_activity'] = cao_activity

                # 液相线温度估算
                liquidus_temp = 1600 - 50 * abs(basicity - 2.8)
                df_advanced.loc[idx, 'factsage_liquidus_temp'] = liquidus_temp

            except Exception as e:
                logger.warning(f"FactSage计算第{idx}行时出错: {e}")
                continue

        # 2. 转炉数学模型特征
        for idx, row in df_advanced.iterrows():
            try:
                # 供氧模型
                oxygen_flow = self.safe_convert(row.get('累氧实际', 4800))
                blow_time = self.safe_convert(row.get('吹氧时间s', 800))
                lance_height = self.safe_convert(row.get('最大角度', 150))

                # 氧气强度
                oxygen_intensity = oxygen_flow / (blow_time / 60) if blow_time > 0 else 0
                df_advanced.loc[idx, 'converter_oxygen_intensity'] = oxygen_intensity

                # 搅拌功率指数
                stirring_index = oxygen_intensity * lance_height / 100
                df_advanced.loc[idx, 'converter_stirring_index'] = stirring_index

                # 热平衡特征
                hot_metal_temp = self.safe_convert(row.get('铁水温度', 1350))
                hot_metal_mass = self.safe_convert(row.get('铁水', 90))
                scrap_mass = self.safe_convert(row.get('废钢', 20))

                # 理论温升
                c_content = self.safe_convert(row.get('铁水C', 4.2)) / 100
                si_content = self.safe_convert(row.get('铁水SI', 0.4)) / 100

                # 简化的氧化热计算
                oxidation_heat = (c_content * 10000 + si_content * 30000) * hot_metal_mass
                scrap_heat = scrap_mass * 1500  # 废钢加热耗热
                net_heat = oxidation_heat - scrap_heat

                theoretical_temp_rise = net_heat / (hot_metal_mass * 750) if hot_metal_mass > 0 else 0
                df_advanced.loc[idx, 'converter_theoretical_temp_rise'] = theoretical_temp_rise
                df_advanced.loc[idx, 'converter_theoretical_end_temp'] = hot_metal_temp + theoretical_temp_rise

            except Exception as e:
                logger.warning(f"转炉模型计算第{idx}行时出错: {e}")
                continue

        # 3. 专家经验特征
        for idx, row in df_advanced.iterrows():
            try:
                # 温度控制难度
                steel_type = str(row.get('钢种', ''))
                if any(x in steel_type for x in ['65Mn', '70', 'C72DA']):
                    difficulty = 0.8  # 高碳钢
                elif any(x in steel_type for x in ['ER50', 'ML40Cr']):
                    difficulty = 0.9  # 合金钢
                else:
                    difficulty = 0.5  # 普通钢

                df_advanced.loc[idx, 'expert_control_difficulty'] = difficulty

                # 工艺稳定性指数
                oxygen_eff = self.safe_convert(row.get('converter_oxygen_intensity', 4.5))
                basicity = self.safe_convert(row.get('factsage_basicity', 2.8))

                stability = 1.0 - abs(oxygen_eff - 4.5) / 10 - abs(basicity - 2.8) / 5
                df_advanced.loc[idx, 'expert_process_stability'] = max(0, stability)

            except Exception as e:
                logger.warning(f"专家特征计算第{idx}行时出错: {e}")
                continue

        # 4. 交互特征
        if 'converter_theoretical_end_temp' in df_advanced.columns and '铁水温度' in df_advanced.columns:
            df_advanced['interaction_temp_deviation'] = (
                df_advanced['converter_theoretical_end_temp'] - df_advanced['铁水温度']
            )

        if 'factsage_basicity' in df_advanced.columns and 'converter_oxygen_intensity' in df_advanced.columns:
            df_advanced['interaction_basicity_oxygen'] = (
                df_advanced['factsage_basicity'] * df_advanced['converter_oxygen_intensity']
            )

        logger.info("高级特征创建完成")
        return df_advanced

    def apply_expert_rules(self, df: pd.DataFrame, predictions: np.ndarray) -> np.ndarray:
        """应用专家规则修正"""
        logger.info("应用专家规则修正")

        corrected_predictions = predictions.copy()

        for idx, row in df.iterrows():
            if idx >= len(corrected_predictions):
                break

            correction = 0.0

            # 规则1: 高硅铁水修正
            si_content = self.safe_convert(row.get('铁水SI', 0.4))
            if si_content > 0.8:
                correction += self.expert_rules['high_silicon_correction']

            # 规则2: 高碳铁水修正
            c_content = self.safe_convert(row.get('铁水C', 4.2))
            if c_content > 4.5:
                correction += self.expert_rules['high_carbon_correction']

            # 规则3: 后期加料修正
            late_addition = self.safe_convert(row.get('最后2分钟', 0))
            if late_addition > 0:
                correction += late_addition * self.expert_rules['late_addition_penalty']

            # 规则4: 炉渣碱度修正
            basicity = self.safe_convert(row.get('factsage_basicity', 2.8))
            basicity_deviation = abs(basicity - self.expert_rules['slag_basicity_optimal'])
            if basicity_deviation > 0.5:
                correction -= basicity_deviation * 5

            corrected_predictions[idx] += correction

        # 物理约束
        corrected_predictions = np.clip(corrected_predictions, 1500, 1750)

        logger.info("专家规则修正完成")
        return corrected_predictions

    def train_robust_models(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """训练稳健模型"""
        logger.info("开始训练稳健模型")

        # 清理数据
        X_clean = X.fillna(X.median())
        y_clean = y.dropna()
        X_clean = X_clean.loc[y_clean.index]

        logger.info(f"清理后数据量: {len(X_clean)}")

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X_clean, y_clean, test_size=0.2, random_state=42
        )

        models_config = {
            # 重新加入的线性模型
            'Ridge_Robust': Ridge(alpha=1.0, random_state=42),
            'Lasso_Robust': Lasso(alpha=0.1, random_state=42, max_iter=3000),
            'ElasticNet_Robust': ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42, max_iter=3000),
            'BayesianRidge_Robust': BayesianRidge(),

            # 树模型
            'RandomForest_Robust': RandomForestRegressor(
                n_estimators=500, max_depth=15, min_samples_split=5,
                min_samples_leaf=2, random_state=42, n_jobs=-1
            ),
            'ExtraTrees_Robust': ExtraTreesRegressor(
                n_estimators=500, max_depth=15, min_samples_split=5,
                min_samples_leaf=2, random_state=42, n_jobs=-1
            ),
            'GradientBoosting_Robust': GradientBoostingRegressor(
                n_estimators=300, max_depth=8, learning_rate=0.1,
                subsample=0.8, random_state=42
            ),

            # 梯度提升
            'XGBoost_Robust': xgb.XGBRegressor(
                n_estimators=500, max_depth=10, learning_rate=0.05,
                subsample=0.9, colsample_bytree=0.9, random_state=42
            ),
            'LightGBM_Robust': lgb.LGBMRegressor(
                n_estimators=500, max_depth=10, learning_rate=0.05,
                subsample=0.9, colsample_bytree=0.9, random_state=42, verbose=-1
            )
        }

        results = {}

        for name, model in models_config.items():
            try:
                logger.info(f"训练{name}模型...")

                if 'Ridge' in name or 'Lasso' in name or 'Elastic' in name or 'Bayesian' in name:
                    # 线性模型标准化
                    scaler = StandardScaler()
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)

                    model.fit(X_train_scaled, y_train)
                    y_pred = model.predict(X_test_scaled)
                    self.scalers[name] = scaler
                else:
                    # 树模型
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)
                    self.scalers[name] = None

                # 评估
                mae = mean_absolute_error(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                r2 = r2_score(y_test, y_pred)

                # 目标范围精度
                target_mask = (y_test >= 1590) & (y_test <= 1670)
                if target_mask.sum() > 0:
                    target_accuracy_20 = np.mean(np.abs(y_test[target_mask] - y_pred[target_mask]) <= 20) * 100
                    target_accuracy_15 = np.mean(np.abs(y_test[target_mask] - y_pred[target_mask]) <= 15) * 100
                    target_accuracy_10 = np.mean(np.abs(y_test[target_mask] - y_pred[target_mask]) <= 10) * 100
                else:
                    target_accuracy_20 = target_accuracy_15 = target_accuracy_10 = 0

                results[name] = {
                    'model': model,
                    'mae': mae,
                    'rmse': rmse,
                    'r2': r2,
                    'target_accuracy_20': target_accuracy_20,
                    'target_accuracy_15': target_accuracy_15,
                    'target_accuracy_10': target_accuracy_10,
                    'y_test': y_test,
                    'y_pred': y_pred
                }

                logger.info(f"{name} - MAE: {mae:.1f}°C, 目标范围±20°C精度: {target_accuracy_20:.1f}%")

            except Exception as e:
                logger.error(f"训练{name}失败: {e}")
                continue

        self.models = results
        return results

    def create_ensemble_prediction(self, X_test: pd.DataFrame, test_df: pd.DataFrame) -> np.ndarray:
        """创建集成预测"""
        logger.info("创建集成预测")

        X_test_clean = X_test.fillna(X_test.median())

        predictions = []
        weights = []

        for name, result in self.models.items():
            try:
                model = result['model']
                scaler = self.scalers[name]

                if scaler is not None:
                    X_test_scaled = scaler.transform(X_test_clean)
                    pred = model.predict(X_test_scaled)
                else:
                    pred = model.predict(X_test_clean)

                predictions.append(pred)
                # 基于目标范围精度计算权重
                weight = result['target_accuracy_20'] / 100.0
                weights.append(weight)

                logger.info(f"{name}预测完成，权重: {weight:.3f}")

            except Exception as e:
                logger.error(f"{name}预测失败: {e}")
                continue

        if predictions:
            # 归一化权重
            total_weight = sum(weights)
            if total_weight > 0:
                weights = [w / total_weight for w in weights]
            else:
                weights = [1.0 / len(predictions)] * len(predictions)

            # 加权集成
            ensemble_pred = np.zeros(len(predictions[0]))
            for pred, weight in zip(predictions, weights):
                ensemble_pred += pred * weight

            logger.info(f"集成预测完成，使用{len(predictions)}个模型")

            # 应用专家规则修正
            final_pred = self.apply_expert_rules(test_df, ensemble_pred)

            return final_pred
        else:
            logger.error("没有可用的预测模型")
            return np.array([1600] * len(X_test))

def load_and_process_data():
    """加载和处理数据"""
    logger.info("加载数据")

    # 加载训练数据
    train_df = pd.read_excel('1-4521剔除重复20250514.xlsx')
    logger.info(f"训练数据: {train_df.shape}")

    # 加载测试数据
    test_df = pd.read_excel('4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx')
    logger.info(f"测试数据: {test_df.shape}")

    return train_df, test_df

def prepare_features(predictor: RobustIntegratedPredictor,
                    train_df: pd.DataFrame,
                    test_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series]:
    """准备特征"""
    logger.info("准备特征")

    # 创建高级特征
    train_enhanced = predictor.create_advanced_features(train_df)
    test_enhanced = predictor.create_advanced_features(test_df)

    # 特征选择
    exclude_cols = ['炉号', '钢种', '出钢重量估算', 'Unnamed: 4', '钢水温度']

    # 找到共同特征
    train_features = [col for col in train_enhanced.columns if col not in exclude_cols]
    test_features = [col for col in test_enhanced.columns if col not in exclude_cols]
    common_features = list(set(train_features) & set(test_features))

    logger.info(f"训练特征数: {len(train_features)}")
    logger.info(f"测试特征数: {len(test_features)}")
    logger.info(f"共同特征数: {len(common_features)}")

    # 使用共同特征
    X_train = train_enhanced[common_features].copy()
    X_test = test_enhanced[common_features].copy()
    y_train = train_enhanced['钢水温度'].copy()

    # 处理分类特征
    categorical_cols = X_train.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        X_train[col] = pd.Categorical(X_train[col]).codes
        X_test[col] = pd.Categorical(X_test[col]).codes

    predictor.feature_names = common_features

    return X_train, X_test, y_train

def evaluate_results(y_true: pd.Series, y_pred: np.ndarray, model_results: Dict[str, Any]):
    """评估结果"""
    logger.info("=== 最终结果评估 ===")

    # 如果有真实标签
    if y_true is not None and len(y_true) > 0:
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)

        # 目标范围精度
        target_mask = (y_true >= 1590) & (y_true <= 1670)
        if target_mask.sum() > 0:
            target_accuracy_20 = np.mean(np.abs(y_true[target_mask] - y_pred[target_mask]) <= 20) * 100
            target_accuracy_15 = np.mean(np.abs(y_true[target_mask] - y_pred[target_mask]) <= 15) * 100
            target_accuracy_10 = np.mean(np.abs(y_true[target_mask] - y_pred[target_mask]) <= 10) * 100
        else:
            target_accuracy_20 = target_accuracy_15 = target_accuracy_10 = 0

        logger.info(f"最终集成模型性能:")
        logger.info(f"  MAE: {mae:.2f}°C")
        logger.info(f"  RMSE: {rmse:.2f}°C")
        logger.info(f"  R²: {r2:.4f}")
        logger.info(f"  目标范围±20°C精度: {target_accuracy_20:.1f}%")
        logger.info(f"  目标范围±15°C精度: {target_accuracy_15:.1f}%")
        logger.info(f"  目标范围±10°C精度: {target_accuracy_10:.1f}%")

        # 检查95%目标
        if target_accuracy_20 >= 95:
            logger.info("🎉🎉🎉 恭喜！95%目标已达成！🎉🎉🎉")
        else:
            logger.info(f"距离95%目标还差: {95 - target_accuracy_20:.1f}%")

    # 显示各模型性能
    logger.info("\n各模型性能对比:")
    for name, result in model_results.items():
        logger.info(f"  {name}: {result['target_accuracy_20']:.1f}%")

    # 预测统计
    logger.info(f"\n预测统计:")
    logger.info(f"  预测范围: {np.min(y_pred):.1f}°C - {np.max(y_pred):.1f}°C")
    logger.info(f"  平均预测: {np.mean(y_pred):.1f}°C")
    logger.info(f"  标准差: {np.std(y_pred):.1f}°C")

def create_visualizations(model_results: Dict[str, Any], final_predictions: np.ndarray,
                         test_df: pd.DataFrame):
    """创建可视化"""
    logger.info("创建可视化")

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # 1. 模型性能对比
    model_names = list(model_results.keys())
    accuracies = [model_results[name]['target_accuracy_20'] for name in model_names]

    bars = ax1.bar(model_names, accuracies, color='skyblue', edgecolor='black')
    ax1.set_title('各模型目标范围±20°C精度对比')
    ax1.set_ylabel('精度 (%)')
    ax1.tick_params(axis='x', rotation=45)
    ax1.axhline(y=95, color='r', linestyle='--', label='目标95%')
    ax1.legend()

    for i, v in enumerate(accuracies):
        ax1.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')

    # 2. 最终预测分布
    ax2.hist(final_predictions, bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
    ax2.axvline(np.mean(final_predictions), color='r', linestyle='--',
               label=f'平均值: {np.mean(final_predictions):.1f}°C')
    ax2.set_title('最终预测温度分布')
    ax2.set_xlabel('预测温度 (°C)')
    ax2.set_ylabel('频次')
    ax2.legend()
    ax2.grid(alpha=0.3)

    # 3. 铁水温度vs预测温度
    if '铁水温度' in test_df.columns:
        ax3.scatter(test_df['铁水温度'], final_predictions, alpha=0.6, s=30)
        ax3.set_title('铁水温度 vs 预测出钢温度')
        ax3.set_xlabel('铁水温度 (°C)')
        ax3.set_ylabel('预测出钢温度 (°C)')
        ax3.grid(alpha=0.3)

    # 4. 钢种分析
    if '钢种' in test_df.columns:
        steel_types = test_df['钢种'].value_counts().head(8).index
        steel_temps = []
        for steel_type in steel_types:
            mask = test_df['钢种'] == steel_type
            if mask.sum() > 0:
                avg_temp = final_predictions[mask].mean()
                steel_temps.append(avg_temp)
            else:
                steel_temps.append(0)

        bars = ax4.bar(range(len(steel_types)), steel_temps, color='lightcoral', edgecolor='black')
        ax4.set_title('主要钢种平均预测温度')
        ax4.set_xlabel('钢种')
        ax4.set_ylabel('平均预测温度 (°C)')
        ax4.set_xticks(range(len(steel_types)))
        ax4.set_xticklabels(steel_types, rotation=45)
        ax4.grid(alpha=0.3)

        for i, v in enumerate(steel_temps):
            if v > 0:
                ax4.text(i, v + 5, f'{v:.0f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f'robust_prediction_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png',
                dpi=300, bbox_inches='tight')
    plt.close()

    logger.info("可视化完成")

def main():
    """主函数"""
    logger.info("=== 稳健集成钢水温度预测系统启动 ===")
    logger.info("目标: 基于FactSage、炉渣预测、转炉数学模型达到95%命中率")

    try:
        # 1. 加载数据
        train_df, test_df = load_and_process_data()

        # 2. 创建预测器
        predictor = RobustIntegratedPredictor()

        # 3. 准备特征
        X_train, X_test, y_train = prepare_features(predictor, train_df, test_df)

        logger.info(f"最终特征数: {X_train.shape[1]}")
        logger.info(f"训练样本数: {X_train.shape[0]}")
        logger.info(f"测试样本数: {X_test.shape[0]}")

        # 4. 训练模型
        model_results = predictor.train_robust_models(X_train, y_train)

        if not model_results:
            logger.error("没有成功训练的模型")
            return

        # 5. 集成预测
        final_predictions = predictor.create_ensemble_prediction(X_test, test_df)

        # 6. 评估结果
        y_test = test_df.get('钢水温度', None)
        evaluate_results(y_test, final_predictions, model_results)

        # 7. 创建可视化
        create_visualizations(model_results, final_predictions, test_df)

        # 8. 保存结果
        results_df = test_df.copy()
        results_df['predicted_temperature'] = final_predictions

        # 添加预测置信度
        results_df['prediction_confidence'] = 'High'  # 简化处理

        output_file = f"robust_prediction_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        results_df.to_excel(output_file, index=False)
        logger.info(f"结果已保存到: {output_file}")

        # 9. 生成报告
        with open(f"robust_prediction_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 'w', encoding='utf-8') as f:
            f.write("稳健集成钢水温度预测报告\n")
            f.write("=" * 50 + "\n\n")

            f.write("模型性能:\n")
            for name, result in model_results.items():
                f.write(f"  {name}: {result['target_accuracy_20']:.1f}%\n")

            f.write(f"\n预测统计:\n")
            f.write(f"  预测样本数: {len(final_predictions)}\n")
            f.write(f"  预测范围: {np.min(final_predictions):.1f}°C - {np.max(final_predictions):.1f}°C\n")
            f.write(f"  平均预测: {np.mean(final_predictions):.1f}°C\n")
            f.write(f"  标准差: {np.std(final_predictions):.1f}°C\n")

        logger.info("=== 稳健集成预测系统完成 ===")

    except Exception as e:
        logger.error(f"系统运行出错: {e}")
        raise

if __name__ == "__main__":
    main()
