"""
Configuration parameters for the steel temperature prediction model.
"""

# Data paths
TRAIN_DATA_PATH = "1-4521剔除重复20250514.xlsx"
TEST_DATA_PATH = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"
RESULTS_DIR = "results"

# Target variable
TARGET = "钢水温度"

# --- 冶金常量 ---
# 元素和氧化物的摩尔质量 (g/mol)
MOLAR_MASS = {
    'Si': 28.085,    # 硅
    'Mn': 54.938,    # 锰
    'P': 30.974,     # 磷
    'C': 12.011,     # 碳
    'Fe': 55.845,    # 铁
    'O': 15.999,     # 氧
    'Ca': 40.078,    # 钙
    'Mg': 24.305,    # 镁
    'Al': 26.982,    # 铝
    'SiO2': 60.084,  # 二氧化硅
    'MnO': 70.937,   # 氧化锰
    'P2O5': 141.945, # 五氧化二磷
    'CaO': 56.077,   # 氧化钙
    'MgO': 40.304,   # 氧化镁
    'Al2O3': 101.96, # 氧化铝
    'FeO': 71.844,   # 氧化亚铁
    'Fe2O3': 159.69  # 氧化铁
}

# 元素氧化成氧化物的质量转换系数
ELEMENT_TO_OXIDE_MASS_RATIO = {
    'Si_to_SiO2': MOLAR_MASS['SiO2'] / MOLAR_MASS['Si'],
    'Mn_to_MnO': MOLAR_MASS['MnO'] / MOLAR_MASS['Mn'],
    'P_to_P2O5': MOLAR_MASS['P2O5'] / (2 * MOLAR_MASS['P'])
}

# 辅料中活性成分的估计含量
FLUX_COMPONENT_RATIO = {
    'CaO_in_Lime': 0.90,
    'CaO_in_Dolomite': 0.58,
    'MgO_in_Dolomite': 0.40,
    'Al2O3_in_Bauxite': 0.70
}

# --- 炉渣计算相关列名和默认值 ---
DEFAULT_SLAG_FEO_PERCENT = 18.0  # 默认炉渣FeO百分比
COL_SLAG_FEO_MEASURED_PERCENT = '炉渣FeO' # 如果有实测炉渣FeO百分比的列名
COL_SLAG_TOTAL_MASS_KG = 'feature_slag_total_mass_kg' # 计算得到的总炉渣质量的特征名
DEFAULT_IRON_MASS_COL = '铁水' # 当 \COL_HM_MASS_KG 缺失时，用于创建默认铁水质量列的名称

# --- 原始数据关键列名 (用于特征工程) ---
COL_HM_MASS_KG = '铁水'                 # 铁水质量(kg)
COL_STEEL_MASS_KG = '钢水重量'          # 钢水质量(kg)
COL_SCRAP_MASS_KG = '废钢'              # 废钢加入量(kg)
# 可以根据需要添加更多在 feature_engineering.py 中使用的原始数据列名常量
# 例如 COL_HM_TEMP_C, COL_HM_SI_PERCENT 等，如果它们在多个模块中被硬编码使用

# Feature engineering parameters
FEATURE_GROUPS = {
    "basic_features": [
        COL_HM_MASS_KG, COL_SCRAP_MASS_KG, "铁水C", "铁水SI", "铁水S", "铁水P", "铁水MN", 
        "铁水温度", "间隔时间min", "吹氧时间s", "累氧实际", "最大角度",
        "石灰", "白云石", "破碎料", "烧结返矿", "石灰石", "最后2分钟"
    ],
    "gas_features": [
        "CO中C(kg)", " CO2中C(kg)", "气体总C", "气体流量流速平均", "最低流速"
    ],
    "metallurgical_features": [
        # These will be created in feature engineering
        "铁水废钢比", "碱度", "氧化硅热", "氧化碳热", "氧化锰热", "氧化磷热",
        "总放热量", "散热量", "净热量"
    ],
    "time_series_features": [
        # These will be created in feature engineering
        "初期脱碳率", "中期脱碳率", "后期脱碳率", "CO峰值时刻", "脱碳速率最大时刻"
    ],
    "interaction_features": [
        # These will be created in feature engineering
        "铁水SI_氧供应强度", "铁水C_氧供应强度", "铁水温度_铁水SI"
    ]
}

# Model parameters
MODEL_PARAMS = {
    "xgboost": {
        "n_estimators": 500,
        "learning_rate": 0.05,
        "max_depth": 6,
        "subsample": 0.8,
        "colsample_bytree": 0.8,
        "objective": "reg:squarederror",
        "random_state": 42
    },
    "lightgbm": {
        "n_estimators": 500,
        "learning_rate": 0.05,
        "max_depth": 6,
        "subsample": 0.8,
        "colsample_bytree": 0.8,
        "objective": "regression",
        "random_state": 42
    },
    "random_forest": {
        "n_estimators": 500,
        "max_depth": 10,
        "min_samples_split": 5,
        "min_samples_leaf": 2,
        "random_state": 42
    },
    "neural_network": {
        "hidden_layer_sizes": (100, 50, 25),
        "activation": "relu",
        "solver": "adam",
        "alpha": 0.0001,
        "batch_size": 32,
        "learning_rate": "adaptive",
        "max_iter": 500,
        "random_state": 42
    },
    "catboost": {
        "iterations": 500,
        "learning_rate": 0.05,
        "depth": 6,
        "l2_leaf_reg": 3,
        "loss_function": "MAE",
        "eval_metric": "MAE",
        "random_seed": 42,
        "verbose": 0
    }
}

# Evaluation parameters
EVALUATION_METRICS = ["mae", "rmse", "r2", "hit_rate_20"]
CV_FOLDS = 5

# Physical constants for metallurgical calculations
HEAT_CONSTANTS = {
    "C_oxidation": 9800,  # Heat released by carbon oxidation (cal/g)
    "Si_oxidation": 7800,  # Heat released by silicon oxidation (cal/g)
    "Mn_oxidation": 1800,  # Heat released by manganese oxidation (cal/g)
    "P_oxidation": 5800,   # Heat released by phosphorus oxidation (cal/g)
    "heat_capacity_steel": 0.17,  # Heat capacity of steel (cal/g/°C)
    "heat_loss_rate": 2.5,  # Heat loss rate (°C/min)
}

# Steel grade classification
STEEL_GRADE_GROUPS = {
    "low_carbon": {"C_max": 0.25},
    "medium_carbon": {"C_min": 0.25, "C_max": 0.6},
    "high_carbon": {"C_min": 0.6},
    "low_alloy": {"Si_max": 0.5, "Mn_max": 1.0},
    "high_alloy": {"Si_min": 0.5, "Mn_min": 1.0}
}

# Process stage division
PROCESS_STAGES = {
    "early_stage": 0.3,  # First 30% of blowing time
    "mid_stage": 0.7,    # 30-70% of blowing time
    "late_stage": 1.0    # 70-100% of blowing time
}

# Anomaly detection parameters
ANOMALY_DETECTION = {
    "isolation_forest": {
        "contamination": 0.05,
        "random_state": 42
    },
    "dbscan": {
        "eps": 0.5,
        "min_samples": 5
    }
}

# Uncertainty quantification parameters
UNCERTAINTY_QUANTIFICATION = {
    "confidence_level": 0.95,
    "bootstrap_iterations": 100
}
