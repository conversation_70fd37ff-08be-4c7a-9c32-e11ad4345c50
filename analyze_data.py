import pandas as pd
import os

# Print current working directory
print(f"Current working directory: {os.getcwd()}")

# List files in the directory
print("Files in directory:")
for file in os.listdir():
    print(f"- {file}")

# Read training data
print("\nAnalyzing training data: 1-4521剔除重复20250514.xlsx")
try:
    train_data = pd.read_excel("1-4521剔除重复20250514.xlsx")
    print(f"Shape: {train_data.shape}")
    print("Columns:")
    for col in train_data.columns:
        print(f"- {col}")
    print("\nFirst 5 rows:")
    print(train_data.head())
    print("\nData types:")
    print(train_data.dtypes)
    print("\nSummary statistics:")
    print(train_data.describe())
except Exception as e:
    print(f"Error reading training data: {e}")

# Read test data
print("\nAnalyzing test data: 第四批测试数据20250513.xlsx")
try:
    test_data = pd.read_excel("第四批测试数据20250513.xlsx")
    print(f"Shape: {test_data.shape}")
    print("Columns:")
    for col in test_data.columns:
        print(f"- {col}")
    print("\nFirst 5 rows:")
    print(test_data.head())
    print("\nData types:")
    print(test_data.dtypes)
    print("\nSummary statistics:")
    print(test_data.describe())
except Exception as e:
    print(f"Error reading test data: {e}")
