import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
from sklearn.model_selection import train_test_split
import warnings

# Assuming steel_temp_prediction package is in PYTHONPATH or script is run from parent dir
try:
    from steel_temp_prediction.data_preprocessing import preprocess_data
    from steel_temp_prediction.feature_engineering import engineer_all_features
    from steel_temp_prediction.config import TRAIN_DATA_PATH, TEST_DATA_PATH, TARGET, RESULTS_DIR
    from steel_temp_prediction.utils import create_directory, clean_feature_names
except ImportError:
    # Fallback for running script directly within the package directory for dev
    from data_preprocessing import preprocess_data
    from feature_engineering import engineer_all_features
    from config import TRAIN_DATA_PATH, TEST_DATA_PATH, TARGET, RESULTS_DIR
    from utils import create_directory, clean_feature_names


# Suppress Matplotlib font warnings (common in environments without specific CJK fonts)
# Ideally, ensure appropriate fonts are installed or configured in Matplotlib.
warnings.filterwarnings("ignore", category=UserWarning, module="matplotlib")
# Specific warning for findfont:
warnings.filterwarnings("ignore", message="findfont: Font family.*not found.*")


logger = logging.getLogger(__name__)

def run_distribution_analysis():
    """
    Performs detailed data distribution analysis for training, validation,
    and external test sets.
    """
    logger.info("--- SCRIPT START: run_distribution_analysis ---")
    analysis_subdir = os.path.join(RESULTS_DIR, "distribution_analysis")
    logger.info(f"Target analysis subdirectory: {analysis_subdir}")

    logger.info("Attempting to create analysis subdirectory...")
    create_directory(analysis_subdir)
    logger.info(f"Analysis subdirectory creation attempted. Check if '{analysis_subdir}' exists.")

    # 1. Load and preprocess data
    logger.info("Step 1: Loading and preprocessing data...")
    try:
        logger.info("Calling preprocess_data...")
        train_df_processed, external_test_df_processed = preprocess_data(TRAIN_DATA_PATH, TEST_DATA_PATH)
        logger.info("preprocess_data call finished.")
        logger.info(f"Processed training data (train_df_processed) shape: {train_df_processed.shape}")
        logger.info(f"train_df_processed.head():\n{train_df_processed.head()}")
        if external_test_df_processed is not None:
            logger.info(f"Processed external test data (external_test_df_processed) shape: {external_test_df_processed.shape}")
            logger.info(f"external_test_df_processed.head():\n{external_test_df_processed.head()}")
        else:
            logger.warning("external_test_df_processed is None after preprocessing.")

    except Exception as e:
        logger.error(f"Error during data preprocessing: {e}", exc_info=True)
        return

    # 2. Split processed training data into new training and validation sets
    logger.info("Step 2: Splitting processed training data into new training and validation sets...")
    if TARGET not in train_df_processed.columns:
        logger.error(f"Target column '{TARGET}' not found in processed training data. Cannot proceed with split based on target presence.")
        # Decide if to proceed without target, or return
        # For now, we will proceed, as feature engineering might not need the target initially
        # But y_train, y_val will be None.
        train_data_for_fe = train_df_processed.copy()
        validation_data_for_fe = pd.DataFrame() # Or some other handling for empty val set
        logger.warning(f"Target '{TARGET}' not in train_df_processed. Splitting without target info; validation_data_for_fe will be empty or handled differently.")
    else:
        # Ensure target is present before attempting to use it or drop it
        train_data_for_fe, validation_data_for_fe = train_test_split(
            train_df_processed, test_size=0.2, random_state=42
        )
    logger.info(f"Data split for feature engineering - Training set (train_data_for_fe) shape: {train_data_for_fe.shape}")
    logger.info(f"train_data_for_fe.head():\n{train_data_for_fe.head()}")
    logger.info(f"Data split for feature engineering - Validation set (validation_data_for_fe) shape: {validation_data_for_fe.shape}")
    if not validation_data_for_fe.empty:
        logger.info(f"validation_data_for_fe.head():\n{validation_data_for_fe.head()}")
    else:
        logger.info("validation_data_for_fe is empty.")

    # 3. Engineer features
    logger.info("Step 3: Engineering features...")
    X_train, y_train, X_val, y_val = None, None, None, None # Initialize
    logger.info("Engineering features for new training and validation sets...")
    try:
        logger.info("Calling engineer_all_features for train_data_for_fe...")
        processed_train_df_with_features = engineer_all_features(train_data_for_fe.copy())
        logger.info(f"Engineered train data (processed_train_df_with_features) shape: {processed_train_df_with_features.shape}")
        logger.info(f"processed_train_df_with_features.head():\n{processed_train_df_with_features.head()}")
        if TARGET in processed_train_df_with_features.columns:
            X_train = processed_train_df_with_features.drop(columns=[TARGET])
            y_train = processed_train_df_with_features[TARGET]
            logger.info(f"y_train extracted. Shape: {y_train.shape}. Head:\n{y_train.head() if not y_train.empty else 'Empty'}")
        else:
            X_train = processed_train_df_with_features
            y_train = None
            logger.warning(f"Target column '{TARGET}' not found in processed_train_df_with_features.")

        if not validation_data_for_fe.empty:
            logger.info("Calling engineer_all_features for validation_data_for_fe...")
            processed_val_df_with_features = engineer_all_features(validation_data_for_fe.copy())
            logger.info(f"Engineered validation data (processed_val_df_with_features) shape: {processed_val_df_with_features.shape}")
            logger.info(f"processed_val_df_with_features.head():\n{processed_val_df_with_features.head()}")
            if TARGET in processed_val_df_with_features.columns:
                X_val = processed_val_df_with_features.drop(columns=[TARGET])
                y_val = processed_val_df_with_features[TARGET]
                logger.info(f"y_val extracted. Shape: {y_val.shape}. Head:\n{y_val.head() if not y_val.empty else 'Empty'}")
            else:
                X_val = processed_val_df_with_features
                y_val = None
                logger.warning(f"Target column '{TARGET}' not found in processed_val_df_with_features.")
        else:
            logger.info("Validation set (validation_data_for_fe) was empty, skipping feature engineering for it.")
            X_val, y_val = pd.DataFrame(), pd.Series(dtype='float64') # Empty to avoid None errors later if possible

        logger.info("engineer_all_features for train/validation finished.")
    except Exception as e:
        logger.error(f"Error engineering features for training/validation sets: {e}", exc_info=True)
        return

    logger.info(f"Features engineered - Train: X_train {X_train.shape if X_train is not None else 'None'}, y_train {y_train.shape if y_train is not None else 'None'}")
    logger.info(f"Features engineered - Val: X_val {X_val.shape if X_val is not None else 'None'}, y_val {y_val.shape if y_val is not None else 'None'}")

    logger.info("Engineering features for external test set...")
    X_test_external, y_test_external = None, None # Initialize
    if external_test_df_processed is not None and not external_test_df_processed.empty:
        try:
            logger.info("Calling engineer_all_features for external_test_df_processed...")
            processed_external_test_df_with_features = engineer_all_features(external_test_df_processed.copy())
            logger.info(f"Engineered external test data (processed_external_test_df_with_features) shape: {processed_external_test_df_with_features.shape}")
            logger.info(f"processed_external_test_df_with_features.head():\n{processed_external_test_df_with_features.head()}")

            if TARGET in processed_external_test_df_with_features.columns:
                if not processed_external_test_df_with_features[TARGET].isnull().all():
                    X_test_external = processed_external_test_df_with_features.drop(columns=[TARGET])
                    y_test_external = processed_external_test_df_with_features[TARGET]
                    logger.info(f"y_test_external extracted. Shape: {y_test_external.shape}. Head:\n{y_test_external.head() if not y_test_external.empty else 'Empty'}")
                else:
                    X_test_external = processed_external_test_df_with_features.drop(columns=[TARGET])
                    y_test_external = None 
                    logger.info(f"Target column '{TARGET}' found in external test data but is all NaN.")
            else:
                X_test_external = processed_external_test_df_with_features
                y_test_external = None 
                logger.info(f"Target column '{TARGET}' not found in processed external test data.")
            logger.info("engineer_all_features for external test set finished.")
        except Exception as e:
            logger.error(f"Error engineering features for external test set: {e}", exc_info=True)
    else:
        logger.warning("external_test_df_processed is None or empty. Skipping feature engineering for external test set.")
        X_test_external = pd.DataFrame() # Empty to avoid None errors

    if X_test_external is not None:
        logger.info(f"Features engineered - External Test: X_test_external {X_test_external.shape}")
        if y_test_external is not None:
            logger.info(f"External Test: y_test_external shape: {y_test_external.shape}")
        else:
            logger.info("External Test: y_test_external is None.")
    else:
        logger.warning("X_test_external is None after feature engineering attempt. External test set analysis will be limited.")

    # 4. Clean feature names
    logger.info("Step 4: Cleaning feature names...")
    if X_train is not None and not X_train.empty:
        logger.info(f"X_train columns before cleaning: {X_train.columns.tolist()}")
        X_train = clean_feature_names(X_train.copy())
        logger.info(f"X_train shape after cleaning: {X_train.shape}. Columns: {X_train.columns.tolist()}")
        logger.info(f"X_train.head() after cleaning:\n{X_train.head()}")
    if X_val is not None and not X_val.empty:
        logger.info(f"X_val columns before cleaning: {X_val.columns.tolist()}")
        X_val = clean_feature_names(X_val.copy())
        logger.info(f"X_val shape after cleaning: {X_val.shape}. Columns: {X_val.columns.tolist()}")
        logger.info(f"X_val.head() after cleaning:\n{X_val.head()}")
    if X_test_external is not None and not X_test_external.empty:
        logger.info(f"X_test_external columns before cleaning: {X_test_external.columns.tolist()}")
        X_test_external = clean_feature_names(X_test_external.copy())
        logger.info(f"X_test_external shape after cleaning: {X_test_external.shape}. Columns: {X_test_external.columns.tolist()}")
        logger.info(f"X_test_external.head() after cleaning:\n{X_test_external.head()}")
    logger.info("Feature names cleaned (if DataFrames were not None/empty).")

    # 5. Perform distribution analysis
    logger.info("Step 5: Performing distribution analysis and generating reports...")

    # --- Feature distribution analysis ---
    if X_train is not None and not X_train.empty:
        desc_train = X_train.describe().T
        summary_dfs = {'Train': desc_train}

        if X_val is not None and not X_val.empty:
            desc_val = X_val.describe().T
            summary_dfs['Validation'] = desc_val
        else:
            logger.warning("Validation features (X_val) are empty or None. Skipping in summary statistics and plots.")

        if X_test_external is not None and not X_test_external.empty:
            desc_test_external = X_test_external.describe().T
            summary_dfs['ExternalTest'] = desc_test_external
        else:
            logger.warning("External test features (X_test_external) are empty or None. Skipping in summary statistics and plots.")

        if summary_dfs:
            # Use ordered keys for consistent column order in concat
            ordered_keys = [k for k in ['Train', 'Validation', 'ExternalTest'] if k in summary_dfs]
            feature_summary_stats = pd.concat([summary_dfs[k] for k in ordered_keys], keys=ordered_keys, axis=1)
            feature_summary_path = os.path.join(analysis_subdir, 'feature_summary_statistics.csv')
            feature_summary_stats.to_csv(feature_summary_path)
            logger.info(f"Feature summary statistics saved to {feature_summary_path}")

        logger.info("Generating feature distribution plots...")
        # Ensure the plotting directory exists (create_directory is idempotent)
        create_directory(analysis_subdir) 

        # --- Crucial Step: Ensure all feature columns are strictly numeric before plotting ---
        datasets_to_clean = {\
            "X_train": X_train,\
            "X_val": X_val\
        }
        if X_test_external is not None:
            datasets_to_clean["X_test_external"] = X_test_external

        for df_name, df_obj in datasets_to_clean.items():
            if df_obj is not None:
                logger.info(f"Converting all columns of {df_name} to numeric and filling NaNs with 0...")
                for col in df_obj.columns:
                    if col == TARGET: # Skip target if it accidentally got in X
                        logger.warning(f"Target column '{TARGET}' found in {df_name}, skipping numeric conversion for it here.")
                        continue
                    try:
                        original_dtype = df_obj[col].dtype
                        df_obj[col] = pd.to_numeric(df_obj[col], errors='coerce').fillna(0)
                        if original_dtype != df_obj[col].dtype:
                            logger.debug(f"  Column '{col}' in {df_name}: dtype changed from {original_dtype} to {df_obj[col].dtype}")
                    except Exception as e:
                        logger.error(f"Error converting column '{col}' in {df_name} to numeric: {e}")
                logger.info(f"Finished numeric conversion for {df_name}.")

        # Use the cleaned dataframes for further processing
        X_train = X_train
        y_train_series = y_train # Keep original y_train as Series or None
        X_val = X_val
        y_val_series = y_val # Keep original y_val as Series or None
        X_test_ext = X_test_external
        # y_test_ext is not used for distribution plots in this script's current form

        logger.info("Step 4: Generating summary statistics...")
        if X_train is not None and not X_train.empty:
            desc_train = X_train.describe().T
            summary_dfs = {'Train': desc_train}

            if X_val is not None and not X_val.empty:
                desc_val = X_val.describe().T
                summary_dfs['Validation'] = desc_val
            else:
                logger.warning("Validation features (X_val) are empty or None. Skipping in summary statistics and plots.")

            if X_test_ext is not None and not X_test_ext.empty:
                desc_test_ext = X_test_ext.describe().T
                summary_dfs['ExternalTest'] = desc_test_ext
            else:
                logger.warning("External test features (X_test_ext) are empty or None. Skipping in summary statistics and plots.")

            if summary_dfs:
                # Use ordered keys for consistent column order in concat
                ordered_keys = [k for k in ['Train', 'Validation', 'ExternalTest'] if k in summary_dfs]
                feature_summary_stats = pd.concat([summary_dfs[k] for k in ordered_keys], keys=ordered_keys, axis=1)
                feature_summary_path = os.path.join(analysis_subdir, 'feature_summary_statistics.csv')
                feature_summary_stats.to_csv(feature_summary_path)
                logger.info(f"Feature summary statistics saved to {feature_summary_path}")

        logger.info("Generating feature distribution plots...")
        # Use X_train, X_val, X_test_ext directly as they are now cleaned copies or original references
        feature_columns_to_plot = X_train.columns.tolist()
        if TARGET in feature_columns_to_plot: # Should not happen if split was correct
            feature_columns_to_plot.remove(TARGET)

        num_features = len(feature_columns_to_plot)
        for i, feature in enumerate(feature_columns_to_plot):
            plt.figure(figsize=(18, 10))
            plt.suptitle(f'Distribution for Feature: {feature}', fontsize=16)

            # Plot Training Data
            plt.subplot(1, 3, 1)
            if feature in X_train.columns:
                sns.histplot(X_train[feature], label=f'Train (μ={X_train[feature].mean():.2f}, σ={X_train[feature].std():.2f})', kde=True, stat="density", common_norm=False, element="step", color="blue", alpha=0.7)
                plt.title(f'Train: {feature}')
                plt.legend()
            else:
                plt.title(f'Train: {feature} (Not Available)')

            # Plot Validation Data
            plt.subplot(1, 3, 2)
            if feature in X_val.columns:
                sns.histplot(X_val[feature], label=f'Validation (μ={X_val[feature].mean():.2f}, σ={X_val[feature].std():.2f})', kde=True, stat="density", common_norm=False, element="step", color="green", alpha=0.7)
                plt.title(f'Validation: {feature}')
                plt.legend()
            else:
                plt.title(f'Validation: {feature} (Not Available)')

            # Plot External Test Data
            plt.subplot(1, 3, 3)
            if X_test_ext is not None and feature in X_test_ext.columns:
                sns.histplot(X_test_ext[feature], label=f'Ext. Test (μ={X_test_ext[feature].mean():.2f}, σ={X_test_ext[feature].std():.2f})', kde=True, stat="density", common_norm=False, element="step", color="red", alpha=0.7)
                plt.title(f'Ext. Test: {feature}')
                plt.legend()
            else:
                plt.title(f'Ext. Test: {feature} (Not Available)')

            plt.tight_layout()
            plot_path = os.path.join(analysis_subdir, f'feature_{feature}_distribution.png')
            try:
                plt.savefig(plot_path)
            except Exception as e:
                logger.error(f"Failed to save plot {plot_path}: {e}", exc_info=True)
            plt.close()
        logger.info(f"Feature distribution plots saved in {analysis_subdir}")
    else:
        logger.error("X_train is None or empty. Cannot perform feature distribution analysis.")
        return


    # --- Target variable distribution analysis ---
    logger.info(f"Preparing for Target variable distribution analysis. Checking y_train and y_val.")
    logger.info(f"y_train is None: {y_train is None}")
    if y_train is not None:
        logger.info(f"y_train is empty: {y_train.empty}")
        logger.info(f"y_train.shape: {y_train.shape}")
        logger.info(f"y_train.head():\n{y_train.head()}")
        logger.info(f"y_train missing values: {y_train.isnull().sum()}")

    logger.info(f"y_val is None: {y_val is None}")
    if y_val is not None:
        logger.info(f"y_val is empty: {y_val.empty}")
        logger.info(f"y_val.shape: {y_val.shape}")
        logger.info(f"y_val.head():\n{y_val.head()}")
        logger.info(f"y_val missing values: {y_val.isnull().sum()}")

    if y_train is not None and not y_train.empty:
        desc_y_train = y_train.describe()
        target_summary_dfs = {'Train Target': desc_y_train}

        if y_val is not None and not y_val.empty:
            desc_y_val = y_val.describe()
            target_summary_dfs['Validation Target'] = desc_y_val
        else:
            logger.warning("Validation target (y_val) is None or empty. Skipping in summary and plots.")

        ordered_target_keys = [k for k in ['Train Target', 'Validation Target'] if k in target_summary_dfs]
        if ordered_target_keys:
            try:
                target_summary_stats = pd.concat([target_summary_dfs[k] for k in ordered_target_keys], keys=ordered_target_keys, axis=1)
                target_summary_path = os.path.join(analysis_subdir, 'target_summary_statistics.csv')
                target_summary_stats.to_csv(target_summary_path)
                logger.info(f"Target variable summary statistics saved to {target_summary_path}")
            except Exception as e:
                logger.error(f"Error saving target summary statistics: {e}", exc_info=True)

        create_directory(analysis_subdir) 
        plt.figure(figsize=(12, 7))
        sns.histplot(y_train, label=f'Train Target (μ={y_train.mean():.2f}, σ={y_train.std():.2f})', kde=True, stat="density", common_norm=False, element="step", color="blue", alpha=0.7)
        if y_val is not None and not y_val.empty:
            sns.histplot(y_val, label=f'Validation Target (μ={y_val.mean():.2f}, σ={y_val.std():.2f})', kde=True, stat="density", common_norm=False, element="step", color="orange", alpha=0.7)
        
        plt.title(f'Distribution of Target Variable: {TARGET}', fontsize=14)
        plt.xlabel(TARGET, fontsize=12)
        plt.ylabel("Density", fontsize=12)
        plt.legend()
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plot_path = os.path.join(analysis_subdir, 'target_distribution.png')
        try:
            plt.savefig(plot_path)
            logger.info(f"Target variable distribution plot saved in {plot_path}")
        except Exception as e:
            logger.error(f"Failed to save target distribution plot {plot_path}: {e}", exc_info=True)
        plt.close()
        # logger.info(f"Target variable distribution plot saved in {analysis_subdir}") # Redundant if savefig is successful
    else:
        logger.error("y_train is None or empty. Cannot perform target variable distribution analysis.")

    logger.info("--- SCRIPT END: run_distribution_analysis ---")

if __name__ == "__main__":
    base_results_dir_for_log = RESULTS_DIR if 'RESULTS_DIR' in globals() and RESULTS_DIR else "results"
    log_dir = os.path.join(base_results_dir_for_log, "distribution_analysis")
    
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
            print(f"Successfully created log directory: {log_dir}")
        except Exception as e:
            print(f"Error creating log directory {log_dir}: {e}")
            log_dir = "." 

    log_file_path = os.path.join(log_dir, "data_distribution_analysis.log")
    # Ensure the log directory exists before attempting to configure FileHandler.
    # This was moved to before the basicConfig call.
    print(f"Logging to: {log_file_path}") # Moved print here to confirm path before logging starts

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s',
        handlers=[
            logging.FileHandler(log_file_path, mode='w'),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)
    logger.info("Logging configured for analyze_data_distribution.") # Clarified which logger

    run_distribution_analysis() 