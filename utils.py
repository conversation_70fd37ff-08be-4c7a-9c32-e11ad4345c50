"""
工具函数模块，包含评估指标计算等功能。
"""

import numpy as np
import pandas as pd
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from typing import Dict, Any, Union, List, Tuple

def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """
    计算预测评估指标。
    
    Args:
        y_true: 真实值
        y_pred: 预测值
        
    Returns:
        包含各评估指标的字典
    """
    mae = mean_absolute_error(y_true, y_pred)
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_true, y_pred)
    
    # 计算误差在±20°C内的命中率
    hit_rate_20 = np.mean(np.abs(y_true - y_pred) <= 20) * 100
    
    return {
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'r2': r2,
        'hit_rate_20': hit_rate_20
    }

def format_time(seconds: float) -> str:
    """
    将秒数格式化为可读时间。
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.2f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.2f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.2f}小时"
    
def describe_feature_importance(
    model: Any, 
    feature_names: List[str], 
    top_n: int = 20
) -> pd.DataFrame:
    """
    描述特征重要性。
    
    Args:
        model: 模型
        feature_names: 特征名列表
        top_n: 显示前几个特征
        
    Returns:
        包含特征重要性的DataFrame
    """
    if not hasattr(model, 'feature_importances_'):
        return pd.DataFrame()
    
    importances = model.feature_importances_
    indices = np.argsort(importances)[::-1]
    
    top_indices = indices[:top_n]
    top_importances = importances[top_indices]
    top_features = [feature_names[i] for i in top_indices]
    
    importance_df = pd.DataFrame({
        '特征': top_features,
        '重要性': top_importances
    })
    
    # 按重要性降序排列
    importance_df = importance_df.sort_values('重要性', ascending=False)
    
    # 计算累积重要性百分比
    importance_df['累积百分比'] = (
        importance_df['重要性'].cumsum() / importance_df['重要性'].sum() * 100
    )
    
    return importance_df 