"""
配置文件，包含模型开发和特征工程所需的常量和参数。
"""

import logging
from typing import Dict, Any, List

# 设置日志级别
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )

# 冶金常量
HEAT_CONSTANTS = {
    'SPECIFIC_HEAT_STEEL': 0.18,       # 钢水比热容 (kcal/kg·℃)
    'SPECIFIC_HEAT_SLAG': 0.25,        # 炉渣比热容 (kcal/kg·℃)
    'HEAT_LOSS_RATE': 1.2,             # 热损失系数 (取经验值)
    'MELTING_POINT_STEEL': 1510,       # 钢水熔点 (℃)
    'MELTING_HEAT_STEEL': 65,          # 钢水熔化热 (kcal/kg)
    'HEAT_TRANSFER_EFFICIENCY': 0.85,  # 热传递效率
}

# 工艺阶段
PROCESS_STAGES = {
    'PREHEATING': 'preheating',        # 预热阶段
    'MELTING': 'melting',              # 熔化阶段
    'OXIDATION': 'oxidation',          # 氧化阶段
    'REDUCTION': 'reduction',          # 还原阶段
    'REFINING': 'refining',            # 精炼阶段
    'TAPPING': 'tapping',              # 出钢阶段
}

# 特征分组
FEATURE_GROUPS = {
    'RAW_MATERIALS': 'raw_materials',         # 原材料特征
    'PROCESS_PARAMETERS': 'process_params',   # 工艺参数特征
    'SLAG_PROPERTIES': 'slag_properties',     # 炉渣特性特征
    'TIME_SERIES': 'time_series',             # 时间序列特征
    'INTERACTION': 'interactions',            # 交互特征
    'METALLURGICAL': 'metallurgical'          # 冶金特征
}

# 目标变量
TARGET = '出钢温度'

# 时间序列常量
COL_TS_HEAT_ID = '炉号'            # 炉号列名，用于分组
COL_TS_TIMESTAMP = '时间戳'        # 时间戳列名
COL_TS_SENSOR_A_READING = '传感器A' # 示例传感器读数列名

# 模型参数
MODEL_PARAMS = {
    'xgboost': {
        'n_estimators': 200,
        'learning_rate': 0.05,
        'max_depth': 5,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'objective': 'reg:squarederror',
        'random_state': 42
    },
    'lightgbm': {
        'n_estimators': 200,
        'learning_rate': 0.05,
        'max_depth': 6,
        'num_leaves': 63,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'objective': 'regression',
        'random_state': 42
    },
    'random_forest': {
        'n_estimators': 200,
        'max_depth': 15,
        'min_samples_split': 5,
        'min_samples_leaf': 2,
        'random_state': 42
    },
    'neural_network': {
        'hidden_layer_sizes': (100, 50),
        'activation': 'relu',
        'solver': 'adam',
        'alpha': 0.0001,
        'batch_size': 'auto',
        'learning_rate': 'adaptive',
        'max_iter': 500,
        'random_state': 42
    },
    'ridge': {
        'alpha': 1.0,
        'random_state': 42
    },
    'lasso': {
        'alpha': 0.1,
        'random_state': 42
    },
    'svr': {
        'kernel': 'rbf',
        'C': 100,
        'gamma': 0.1,
        'epsilon': 0.1
    }
}

# 评估指标
EVALUATION_METRICS = ['mae', 'rmse', 'r2', 'hit_rate_20']

# 交叉验证折数
CV_FOLDS = 5 