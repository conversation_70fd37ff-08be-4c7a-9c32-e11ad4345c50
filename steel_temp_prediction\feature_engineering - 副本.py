"""
Feature engineering module for the steel temperature prediction model.
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import PolynomialFeatures, StandardScaler, OneHotEncoder
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
import logging
from typing import Dict, List, Tuple, Union, Any, Optional

# 修复导入路径
try:
    from steel_temp_prediction.config import HEAT_CONSTANTS, PROCESS_STAGES, FEATURE_GROUPS, TARGET, COL_SLAG_FEO_MEASURED_PERCENT, DEFAULT_SLAG_FEO_PERCENT, COL_SLAG_TOTAL_MASS_KG, ELEMENT_TO_OXIDE_MASS_RATIO, FLUX_COMPONENT_RATIO, MOLAR_MASS, COL_HM_MASS_KG, COL_STEEL_MASS_KG, COL_SCRAP_MASS_KG, DEFAULT_IRON_MASS_COL # 确保所有常量都被导入
except ImportError:
    # 当直接在包内运行时使用这个导入
    from config import HEAT_CONSTANTS, PROCESS_STAGES, FEATURE_GROUPS, TARGET, COL_SLAG_FEO_MEASURED_PERCENT, DEFAULT_SLAG_FEO_PERCENT, COL_SLAG_TOTAL_MASS_KG, ELEMENT_TO_OXIDE_MASS_RATIO, FLUX_COMPONENT_RATIO, MOLAR_MASS, COL_HM_MASS_KG, COL_STEEL_MASS_KG, COL_SCRAP_MASS_KG, DEFAULT_IRON_MASS_COL

# Set up logging
logger = logging.getLogger(__name__)

# 中文注释：在这里定义原始数据中可能用到的列名常量。
# 用户需要根据自己的实际数据集中的列名来修改这些常量。

# --- 冶金常量 ---
# 元素和氧化物的摩尔质量 (g/mol)
MOLAR_MASS = {
    'Si': 28.085,    # 硅
    'Mn': 54.938,    # 锰
    'P': 30.974,     # 磷
    'C': 12.011,     # 碳
    'Fe': 55.845,    # 铁
    'O': 15.999,     # 氧
    'Ca': 40.078,    # 钙
    'Mg': 24.305,    # 镁
    'Al': 26.982,    # 铝
    'SiO2': 60.084,  # 二氧化硅
    'MnO': 70.937,   # 氧化锰
    'P2O5': 141.945, # 五氧化二磷
    'CaO': 56.077,   # 氧化钙
    'MgO': 40.304,   # 氧化镁
    'Al2O3': 101.96, # 氧化铝
    'FeO': 71.844,   # 氧化亚铁
    'Fe2O3': 159.69  # 氧化铁
}

# 元素氧化成氧化物的质量转换系数
ELEMENT_TO_OXIDE_MASS_RATIO = {
    'Si_to_SiO2': MOLAR_MASS['SiO2'] / MOLAR_MASS['Si'],      # 约 2.14
    'Mn_to_MnO': MOLAR_MASS['MnO'] / MOLAR_MASS['Mn'],        # 约 1.29
    'P_to_P2O5': MOLAR_MASS['P2O5'] / (2 * MOLAR_MASS['P'])   # 约 2.29
}

# 辅料中活性成分的估计含量 (根据典型值设置，实际应根据具体工厂物料情况调整)
FLUX_COMPONENT_RATIO = {
    'CaO_in_Lime': 0.90,           # 石灰中CaO的含量，约90%
    'CaO_in_Dolomite': 0.58,       # 白云石中CaO的含量，约58%
    'MgO_in_Dolomite': 0.40,       # 白云石中MgO的含量，约40% (如有使用)
    'Al2O3_in_Bauxite': 0.70       # 铝矾土中Al2O3的含量，约70% (如有使用)
}

# 炉渣损耗系数 (根据图片中3.2和3.7节信息)
SLAG_LOSS_RATE_KG_PER_SEC = 0.0025  # 碱性辅料排渣损耗率 (kg/s)，表1中给出为0.0025kg/s

# --- 新增：FeO 相关默认值 ---
DEFAULT_SLAG_FEO_PERCENT = 18.0  # 当无法从数据中获取或估算时，默认的炉渣FeO百分比
DEFAULT_IRON_MASS_COL = '铁水' # 默认铁水质量列，如果原始数据没有明确指定
COL_SLAG_TOTAL_MASS_KG = 'feature_slag_total_mass_kg' # 总炉渣质量的特征名

# --- 原始数据列名定义 (请根据实际数据集调整) ---

# 铁水相关列
COL_HM_MASS_KG = '铁水'                 # 铁水质量(kg)
COL_HM_TEMP_C = '铁水温度'              # 铁水温度(℃)
COL_HM_SI_PERCENT = '铁水SI'            # 铁水中Si含量(%)
COL_HM_MN_PERCENT = '铁水MN'            # 铁水中Mn含量(%)
COL_HM_P_PERCENT = '铁水P'              # 铁水中P含量(%)
COL_HM_C_PERCENT = '铁水C'              # 铁水中C含量(%) (可选)

# 钢水相关列 (如果有)
COL_STEEL_MASS_KG = '钢水重量'          # 钢水质量(kg)，如果没有可用铁水+废钢近似
COL_SCRAP_MASS_KG = '废钢'              # 废钢加入量(kg)
COL_STEEL_SI_FINAL_PERCENT = '钢水SI'   # 钢水最终Si含量(%)
COL_STEEL_MN_FINAL_PERCENT = '钢水MN'   # 钢水最终Mn含量(%)
COL_STEEL_P_FINAL_PERCENT = '钢水P'     # 钢水最终P含量(%)

# 辅料加入相关列
COL_LIME_ADDITION_KG = '石灰'            # 石灰加入量(kg)
COL_DOLOMITE_ADDITION_KG = '白云石'       # 白云石加入量(kg)
COL_BAUXITE_ADDITION_KG = '铝矾土'        # 铝矾土加入量(kg) (如有)
COL_OTHER_FLUX_ADDITION_KG = '其他辅料'    # 其他辅料加入量(kg) (如有)

# 吹炼和工艺参数相关列
COL_OXYGEN_TOTAL_NM3 = '累氧实际'        # 累计供氧量(Nm³)
COL_BLOWING_TIME_SEC = '吹氧时间s'        # 吹氧时间(s)
COL_LANCE_HEIGHT_MM = '枪位'             # 喷枪高度(mm)
COL_LANCE_ANGLE_MAX = '最大角度'          # 最大枪角度(°)
COL_BOTTOM_BLOWING_INTENSITY = '底吹强度'  # 底吹强度 (如有)

# 炉渣相关列 (如果直接有测量数据，则可以使用)
COL_SLAG_WEIGHT_MEASURED_KG = '炉渣重量'  # 实测炉渣重量(kg) (如有)
COL_SLAG_FEO_MEASURED_PERCENT = '炉渣FeO'  # 实测炉渣中FeO含量(%) (如有)

# --- 炉渣成分相关列名 (示例) ---
# 注意：以下部分列可能在实际数据中并不存在，而是通过计算得到
COL_SLAG_CAO_KG = 'raw_slag_cao_kg'                # 例如：炉渣中CaO的量 (kg) 或来源
COL_SLAG_SIO2_KG = 'raw_slag_sio2_kg'              # 例如：炉渣中SiO2的量 (kg) 或来源
COL_SLAG_MGO_KG = 'raw_slag_mgo_kg'                # 例如：炉渣中MgO的量 (kg) 或来源
COL_SLAG_AL2O3_KG = 'raw_slag_al2o3_kg'            # 例如：炉渣中Al2O3的量 (kg) 或来源
COL_SLAG_FEO_PERCENT = 'raw_slag_feo_percent'      # 例如：炉渣中FeO的百分比 (直接测量或估算)
COL_SLAG_WEIGHT_ESTIMATED_KG = 'raw_slag_weight_kg' # 例如：估算的炉渣重量 (kg)

# --- 工艺参数相关列名 (示例) ---
COL_PROCESS_OXYGEN_RATE = 'raw_oxygen_flow_rate_m3_min' # 吹氧速率 (m³/min)
COL_PROCESS_OXYGEN_VOLUME = 'raw_total_oxygen_volume_m3'  # 总吹氧量 (m³)
COL_PROCESS_LANCE_HEIGHT = 'raw_lance_height_mm'          # 喷枪高度 (mm)
COL_PROCESS_STIRRING_POWER = 'raw_stirring_power_kw'    # 底吹搅拌强度/功率 (kW)
# 示例：辅料加入时间和量 (用户可能有多条此类数据，需要相应处理)
# COL_PROCESS_FLUX_A_TIME_SEC = 'raw_flux_a_add_time_sec' # 辅料A加入时间 (秒)
# COL_PROCESS_FLUX_A_AMOUNT_KG = 'raw_flux_a_add_amount_kg' # 辅料A加入量 (kg)

# --- 时间序列数据相关列名 (示例) ---
# 这些列通常与一个分组ID (如炉号) 和一个时间戳关联
COL_TS_HEAT_ID = 'heat_id'                         # 炉号/批次号，用于分组
COL_TS_TIMESTAMP = 'timestamp_sec'                 # 时间戳 (秒或其他单位)
COL_TS_SENSOR_A_READING = 'raw_sensor_a_value'     # 传感器A的读数
COL_TS_SENSOR_B_READING = 'raw_sensor_b_value'     # 传感器B的读数

# --- 其他可能用于交互或计算的特征列名 (示例) ---
COL_INTERACT_STEEL_TEMP_INITIAL = 'raw_steel_temp_initial_c' # 初始钢水温度 (℃)
COL_INTERACT_ALLOY_X_CONTENT = 'raw_alloy_x_content_percent' # 某种合金成分X的百分比

# --- NEW REDUCED FEATURE LIST ---
ACTUAL_MODEL_INPUT_FEATURES = [
    # Features with importance > 0 from meta-learner, mapped to original names
    # feature_0 (铁水)
    '铁水',
    # feature_1 (铁水温度)
    '铁水温度',
    # feature_2 (铁水SI)
    '铁水SI',
    # feature_3 (铁水MN)
    '铁水MN',
    # feature_4 (铁水P)
    '铁水P',
    # feature_5 (铁水C)
    '铁水C',
    # feature_6 (石灰)
    '石灰',
    # feature_7 (累氧实际)
    '累氧实际',
    # feature_8 (吹氧时间s)
    '吹氧时间s',
    # feature_9 (最大角度)
    '最大角度',
    # feature_10 (feature_slag_SiO2_from_Si_kg)
    'feature_slag_SiO2_from_Si_kg',
    # feature_11 (feature_slag_MnO_from_Mn_kg)
    'feature_slag_MnO_from_Mn_kg',
    # feature_12 (feature_slag_P2O5_from_P_kg)
    'feature_slag_P2O5_from_P_kg',
    # feature_13 (feature_slag_CaO_from_lime_kg)
    'feature_slag_CaO_from_lime_kg',
    # feature_14 (feature_slag_CaO_from_dolomite_kg)
    'feature_slag_CaO_from_dolomite_kg',
    # feature_15 (feature_slag_CaO_total_kg)
    'feature_slag_CaO_total_kg',
    # feature_16 (feature_slag_MgO_total_kg)
    'feature_slag_MgO_total_kg',
    # feature_18 (feature_slag_FeO_kg)
    'feature_slag_FeO_kg',
    # feature_19 (feature_slag_total_mass_kg)
    'feature_slag_total_mass_kg',
    # feature_20 (feature_slag_known_oxides_kg)
    'feature_slag_known_oxides_kg',
    
    # Original raw features that are fundamental
    '废钢',
    '白云石',
    
    # Consider if '铝矾土' and '底吹强度' should be here if they are consistently available
    # and if their absence in top importance doesn't mean they are useless
    # (e.g. they might be crucial for some base models or specific scenarios)

    # The following features were in the previous very long list but are not included now
    # as they either had 0 importance, were redundant, or are more complex derived features.
    # This aims to focus on a core, important set.
]
logger.info(f"Using REFINED ACTUAL_MODEL_INPUT_FEATURES list with {len(ACTUAL_MODEL_INPUT_FEATURES)} features based on meta-learner importance.")

def create_basic_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create basic features based on raw data.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added basic features
    """
    logger.info("Creating basic features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # Calculate iron to scrap ratio
    if '铁水' in df_features.columns and '废钢' in df_features.columns:
        df_features['铁水废钢比'] = df_features['铁水'] / df_features['废钢']
        logger.info("Created feature: 铁水废钢比")

    # Calculate basicity (CaO+MgO)/(SiO2+P2O5)
    # Since we don't have direct slag composition, we'll estimate from additions
    if '石灰' in df_features.columns and '白云石' in df_features.columns:
        # Estimate CaO+MgO from lime and dolomite additions
        df_features['碱度估算'] = (df_features['石灰'] * 0.9 + df_features['白云石'] * 0.58) / \
                            (df_features['铁水SI'] * 2.14 + df_features['铁水P'] * 2.29)
        logger.info("Created feature: 碱度估算")

    # Calculate total oxygen supply per ton of hot metal
    if '累氧实际' in df_features.columns and '铁水' in df_features.columns:
        df_features['单位铁水供氧量'] = df_features['累氧实际'] / df_features['铁水']
        logger.info("Created feature: 单位铁水供氧量")

    # Calculate blowing intensity (oxygen per minute)
    if '累氧实际' in df_features.columns and '吹氧时间s' in df_features.columns:
        df_features['吹氧强度'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60)
        logger.info("Created feature: 吹氧强度")

    # Calculate time-related features
    if '间隔时间min' in df_features.columns and '吹氧时间s' in df_features.columns:
        # Total process time
        df_features['总处理时间min'] = df_features['间隔时间min'] + (df_features['吹氧时间s'] / 60)
        logger.info("Created feature: 总处理时间min")

    return df_features

def create_metallurgical_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create metallurgical features based on thermodynamic principles.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added metallurgical features
    """
    logger.info("Creating metallurgical features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # Calculate heat generation from oxidation reactions

    # Silicon oxidation heat
    if '铁水SI' in df_features.columns and '铁水' in df_features.columns:
        df_features['氧化硅热'] = df_features['铁水SI'] * df_features['铁水'] * HEAT_CONSTANTS['Si_oxidation'] * 10
        logger.info("Created feature: 氧化硅热")

    # Carbon oxidation heat
    if 'CO中C(kg)' in df_features.columns and ' CO2中C(kg)' in df_features.columns:
        df_features['氧化碳热'] = (df_features['CO中C(kg)'] + df_features[' CO2中C(kg)']) * HEAT_CONSTANTS['C_oxidation']
        logger.info("Created feature: 氧化碳热")
    elif '气体总C' in df_features.columns:
        df_features['氧化碳热'] = df_features['气体总C'] * HEAT_CONSTANTS['C_oxidation']
        logger.info("Created feature: 氧化碳热 (using 气体总C)")

    # Manganese oxidation heat
    if '铁水MN' in df_features.columns and '铁水' in df_features.columns:
        # Assuming 80% of Mn is oxidized
        df_features['氧化锰热'] = df_features['铁水MN'] * df_features['铁水'] * 0.8 * HEAT_CONSTANTS['Mn_oxidation'] * 10
        logger.info("Created feature: 氧化锰热")

    # Phosphorus oxidation heat
    if '铁水P' in df_features.columns and '铁水' in df_features.columns:
        # Assuming 90% of P is oxidized
        df_features['氧化磷热'] = df_features['铁水P'] * df_features['铁水'] * 0.9 * HEAT_CONSTANTS['P_oxidation'] * 10
        logger.info("Created feature: 氧化磷热")

    # Calculate total heat generation
    heat_columns = [col for col in ['氧化硅热', '氧化碳热', '氧化锰热', '氧化磷热'] if col in df_features.columns]
    if heat_columns:
        df_features['总放热量'] = df_features[heat_columns].sum(axis=1)
        logger.info("Created feature: 总放热量")

    # Calculate heat loss
    if '间隔时间min' in df_features.columns and '吹氧时间s' in df_features.columns:
        total_time = df_features['间隔时间min'] + (df_features['吹氧时间s'] / 60)
        # Estimate heat loss based on time and furnace condition
        df_features['散热量'] = total_time * HEAT_CONSTANTS['heat_loss_rate'] * (df_features['铁水'] + df_features['废钢'])
        logger.info("Created feature: 散热量")

    # Calculate net heat balance
    if '总放热量' in df_features.columns and '散热量' in df_features.columns:
        df_features['净热量'] = df_features['总放热量'] - df_features['散热量']
        logger.info("Created feature: 净热量")

    # Calculate theoretical temperature increase
    if '净热量' in df_features.columns and '铁水' in df_features.columns and '废钢' in df_features.columns:
        total_weight = df_features['铁水'] + df_features['废钢']
        df_features['理论升温'] = df_features['净热量'] / (total_weight * HEAT_CONSTANTS['heat_capacity_steel'] * 1000)
        logger.info("Created feature: 理论升温")

    # Calculate theoretical final temperature
    if '理论升温' in df_features.columns and '铁水温度' in df_features.columns:
        df_features['理论终点温度'] = df_features['铁水温度'] + df_features['理论升温']
        logger.info("Created feature: 理论终点温度")

    return df_features

def create_time_series_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create time series features based on process stages.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added time series features
    """
    logger.info("Creating time series features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # For this simplified version, we'll create proxy features since we don't have the actual time series data

    # Estimate decarburization rate in different stages based on total carbon removal
    if '气体总C' in df_features.columns and '吹氧时间s' in df_features.columns:
        # Total decarburization rate
        df_features['总脱碳率'] = df_features['气体总C'] / (df_features['吹氧时间s'] / 60)
        logger.info("Created feature: 总脱碳率")

        # Estimate early, middle, and late stage decarburization rates
        # In reality, these would be calculated from time series data
        # Here we're using approximations based on typical patterns

        # Early stage: typically 20% of total carbon removal in 30% of time
        early_time = df_features['吹氧时间s'] * PROCESS_STAGES['early_stage'] / 60
        df_features['初期脱碳率'] = df_features['气体总C'] * 0.2 / early_time
        logger.info("Created feature: 初期脱碳率")

        # Middle stage: typically 60% of total carbon removal in 40% of time
        mid_time = df_features['吹氧时间s'] * (PROCESS_STAGES['mid_stage'] - PROCESS_STAGES['early_stage']) / 60
        df_features['中期脱碳率'] = df_features['气体总C'] * 0.6 / mid_time
        logger.info("Created feature: 中期脱碳率")

        # Late stage: typically 20% of total carbon removal in 30% of time
        late_time = df_features['吹氧时间s'] * (PROCESS_STAGES['late_stage'] - PROCESS_STAGES['mid_stage']) / 60
        df_features['后期脱碳率'] = df_features['气体总C'] * 0.2 / late_time
        logger.info("Created feature: 后期脱碳率")

    # Estimate CO peak time (typically occurs at around 40-60% of blowing time)
    if '吹氧时间s' in df_features.columns:
        # Random variation around 50% of blowing time
        np.random.seed(42)  # For reproducibility
        variation = np.random.normal(0, 0.1, size=len(df_features))
        df_features['CO峰值时刻'] = (0.5 + variation) * df_features['吹氧时间s']
        # Clip to reasonable range
        df_features['CO峰值时刻'] = df_features['CO峰值时刻'].clip(0.3 * df_features['吹氧时间s'], 0.7 * df_features['吹氧时间s'])
        logger.info("Created feature: CO峰值时刻")

    # Estimate maximum decarburization rate time (typically occurs slightly before CO peak)
    if 'CO峰值时刻' in df_features.columns:
        df_features['脱碳速率最大时刻'] = df_features['CO峰值时刻'] * 0.9
        logger.info("Created feature: 脱碳速率最大时刻")

    return df_features

def create_interaction_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create interaction features based on metallurgical principles.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added interaction features
    """
    logger.info("Creating interaction features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # Create interaction between silicon content and oxygen supply
    if '铁水SI' in df_features.columns and '单位铁水供氧量' in df_features.columns:
        df_features['铁水SI_氧供应强度'] = df_features['铁水SI'] * df_features['单位铁水供氧量']
        logger.info("Created feature: 铁水SI_氧供应强度")

    # Create interaction between carbon content and oxygen supply
    if '铁水C' in df_features.columns and '单位铁水供氧量' in df_features.columns:
        df_features['铁水C_氧供应强度'] = df_features['铁水C'] * df_features['单位铁水供氧量']
        logger.info("Created feature: 铁水C_氧供应强度")

    # Create interaction between hot metal temperature and silicon content
    if '铁水温度' in df_features.columns and '铁水SI' in df_features.columns:
        df_features['铁水温度_铁水SI'] = df_features['铁水温度'] * df_features['铁水SI']
        logger.info("Created feature: 铁水温度_铁水SI")

    # Create interaction between basicity and phosphorus
    if '碱度估算' in df_features.columns and '铁水P' in df_features.columns:
        df_features['碱度_铁水P'] = df_features['碱度估算'] * df_features['铁水P']
        logger.info("Created feature: 碱度_铁水P")

    # Create interaction between iron-to-scrap ratio and hot metal temperature
    if '铁水废钢比' in df_features.columns and '铁水温度' in df_features.columns:
        df_features['铁水废钢比_铁水温度'] = df_features['铁水废钢比'] * df_features['铁水温度']
        logger.info("Created feature: 铁水废钢比_铁水温度")

    # Create interaction between blowing intensity and maximum lance angle
    if '吹氧强度' in df_features.columns and '最大角度' in df_features.columns:
        df_features['吹氧强度_最大角度'] = df_features['吹氧强度'] * df_features['最大角度']
        logger.info("Created feature: 吹氧强度_最大角度")

    return df_features

def create_polynomial_features(df: pd.DataFrame, degree: int = 2,
                              feature_names: List[str] = None) -> pd.DataFrame:
    """
    Create polynomial features for selected columns.

    Args:
        df: Input DataFrame
        degree: Polynomial degree
        feature_names: List of feature names to use for polynomial features

    Returns:
        DataFrame with added polynomial features
    """
    logger.info(f"Creating polynomial features (degree={degree})...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    if feature_names is None:
        # Use basic metallurgical features
        feature_names = [
            '铁水温度', '铁水SI', '铁水C', '铁水废钢比', '单位铁水供氧量',
            '吹氧强度', '总处理时间min'
        ]

    # Filter to only include features that exist in the DataFrame
    feature_names = [f for f in feature_names if f in df_features.columns]

    if not feature_names:
        logger.warning("No valid features for polynomial feature creation")
        return df_features

    # Create polynomial features
    poly = PolynomialFeatures(degree=degree, include_bias=False, interaction_only=False)
    poly_features = poly.fit_transform(df_features[feature_names])

    # Get feature names
    poly_feature_names = poly.get_feature_names_out(feature_names)

    # Remove the original features from the polynomial feature names
    poly_feature_names = [name for name in poly_feature_names if name not in feature_names]

    # Add polynomial features to DataFrame
    for i, name in enumerate(poly_feature_names):
        # Skip the first 'n' features which are the original ones
        idx = i + len(feature_names)
        if idx < poly_features.shape[1]:
            df_features[f'poly_{name}'] = poly_features[:, idx]

    logger.info(f"Created {len(poly_feature_names)} polynomial features")

    return df_features

def select_best_features(X_train: pd.DataFrame, y_train: pd.Series,
                        X_test: pd.DataFrame, k: int = 50) -> Tuple[pd.DataFrame, pd.DataFrame, List[str]]:
    """
    Select the best k features based on F-regression.

    Args:
        X_train: Training features
        y_train: Training target
        X_test: Test features
        k: Number of features to select

    Returns:
        Tuple of (X_train_selected, X_test_selected, selected_feature_names)
    """
    logger.info(f"Selecting best {k} features...")

    # Initialize feature selector
    selector = SelectKBest(score_func=f_regression, k=k)

    # Fit and transform training data
    X_train_selected = selector.fit_transform(X_train, y_train)

    # Transform test data
    X_test_selected = selector.transform(X_test)

    # Get selected feature names
    selected_indices = selector.get_support(indices=True)
    selected_features = X_train.columns[selected_indices]

    logger.info(f"Selected features: {', '.join(selected_features)}")

    # Convert back to DataFrame with feature names
    X_train_selected_df = pd.DataFrame(X_train_selected, columns=selected_features, index=X_train.index)
    X_test_selected_df = pd.DataFrame(X_test_selected, columns=selected_features, index=X_test.index)

    return X_train_selected_df, X_test_selected_df, selected_features

def calculate_slag_basicity(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算炉渣碱度。
    
    根据公式(3-14)，碱度R = CaO% / SiO2%
    也可以计算扩展的碱度定义，如三元碱度等。

    Args:
        df: 输入DataFrame，包含炉渣中CaO和SiO2的百分比含量。

    Returns:
        添加了炉渣碱度特征的DataFrame。
    """
    df_out = df.copy()
    
    # 确保有CaO和SiO2的百分比含量
    if 'feature_slag_CaO_percent' in df_out.columns and 'feature_slag_SiO2_percent' in df_out.columns:
        # 处理分母可能为零的情况，加上一个极小值epsilon
        epsilon = 1e-6
        # 计算二元碱度 R2 = CaO% / SiO2%
        df_out['feature_slag_basicity_R2'] = df_out['feature_slag_CaO_percent'] / \
                                               (df_out['feature_slag_SiO2_percent'] + epsilon)
        
        # 计算三元碱度 R3 = (CaO% + MgO%) / SiO2% (如果有MgO数据)
        if 'feature_slag_MgO_percent' in df_out.columns:
            df_out['feature_slag_basicity_R3'] = (df_out['feature_slag_CaO_percent'] + 
                                                 df_out['feature_slag_MgO_percent']) / \
                                                (df_out['feature_slag_SiO2_percent'] + epsilon)
        
        # 计算四元碱度 R4 = (CaO% + MgO%) / (SiO2% + Al2O3%) (如果有Al2O3数据)
        if 'feature_slag_MgO_percent' in df_out.columns and 'feature_slag_Al2O3_percent' in df_out.columns:
            df_out['feature_slag_basicity_R4'] = (df_out['feature_slag_CaO_percent'] + 
                                                 df_out['feature_slag_MgO_percent']) / \
                                                (df_out['feature_slag_SiO2_percent'] + 
                                                 df_out['feature_slag_Al2O3_percent'] + epsilon)
    else:
        logger.warning("无法计算炉渣碱度：缺少必要的列 'feature_slag_CaO_percent' 或 'feature_slag_SiO2_percent'")
    
    return df_out

def add_oxide_content_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    添加关键氧化物含量作为特征。
    这些特征可以直接使用原始数据中的含量（如果是百分比），
    或者根据加入量和估算的炉渣总量来计算百分比。
    """
    df_out = df.copy()
    oxide_cols_map = {
        'feature_slag_mgo_content': COL_SLAG_MGO_KG,
        'feature_slag_al2o3_content': COL_SLAG_AL2O3_KG,
        'feature_slag_feo_content_percent': COL_SLAG_FEO_PERCENT,
    }
    for feature_name, raw_col_name in oxide_cols_map.items():
        if raw_col_name in df_out.columns:
            # 假设原始列已经是所需的形式（例如，FeO是百分比，其他是总量）
            # 如果原始列是加入量，而需要百分比，则需除以 COL_SLAG_WEIGHT_ESTIMATED_KG
            df_out[feature_name] = df_out[raw_col_name]
            # print(f"信息: 已添加氧化物特征 {feature_name}。")
        # else:
            # print(f"警告: 氧化物特征源列 {raw_col_name} 未找到。")
    return df_out

def estimate_slag_physical_properties(df: pd.DataFrame) -> pd.DataFrame:
    """
    估算炉渣的物理化学性质（如粘度、熔点等）。
    
    这部分通常需要基于炉渣成分和物理化学模型进行，本函数提供一些简化的经验公式估算。
    用户可以根据具体需求和专业知识进一步细化和改进。
    
    Args:
        df: 输入DataFrame，包含炉渣的成分百分比、碱度等。
        
    Returns:
        添加了炉渣物理化学性质估算特征的DataFrame。
    """
    df_out = df.copy()
    
    # 前提条件检查
    required_cols = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                     'feature_slag_FeO_percent', 'feature_slag_basicity_R2']
    if not all(col in df_out.columns for col in required_cols):
        logger.warning(f"估算炉渣物理化学性质需要列: {required_cols}，但部分列缺失")
        return df_out
    
    # 1. 估算炉渣粘度 (牛顿/平方米)
    # 使用简化的经验公式，仅考虑碱度和FeO的影响
    # 实际中，粘度与温度、成分有复杂的关系，此处为示例
    try:
        # 示例公式：粘度 = 10^(A/T + B)，其中A与碱度和成分有关，B为常数，T为温度
        # 此处假设温度为1600℃，简化为粘度指数 = f(碱度, FeO)
        viscosity_index = (1.0 / df_out['feature_slag_basicity_R2']) + (df_out['feature_slag_FeO_percent'] * 0.05)
        df_out['feature_slag_viscosity_index'] = viscosity_index
    except Exception as e:
        logger.warning(f"计算炉渣粘度指数时出错: {e}")
    
    # 2. 估算炉渣熔点 (℃)
    # 简化公式，实际熔点受多种成分影响，是复杂的多元函数
    try:
        # 示例公式：熔点近似值 = f(CaO, SiO2, FeO)
        cao_effect = df_out['feature_slag_CaO_percent'] * 2.5
        sio2_effect = df_out['feature_slag_SiO2_percent'] * 2.0
        feo_effect = df_out['feature_slag_FeO_percent'] * (-1.5)  # FeO会降低熔点
        
        base_melt_point = 1300  # 基础熔点 (℃)
        estimated_melt_point = base_melt_point + cao_effect + sio2_effect + feo_effect
        
        # 限制在合理范围内
        estimated_melt_point = estimated_melt_point.clip(1200, 1700)
        df_out['feature_slag_melt_point_C'] = estimated_melt_point
    except Exception as e:
        logger.warning(f"计算炉渣熔点估计值时出错: {e}")
    
    # 3. 其他可能的物理化学特性，如导热性、热容量等
    # 这些通常需要更复杂的模型，这里仅作为占位符
    
    return df_out

def add_all_slag_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算和添加所有炉渣相关特征。
    
    集成所有炉渣特征计算的步骤：
    1. 计算各氧化物的质量和总炉渣质量。
    2. 计算各氧化物的百分比含量。
    3. 计算炉渣碱度。
    4. 估算炉渣的物理化学性质。
    
    Args:
        df: 输入DataFrame，包含铁水成分、钢水成分、辅料加入量等原始数据。
        
    Returns:
        添加了炉渣相关特征的DataFrame。
    """
    # 1. 计算各氧化物的质量和总炉渣质量
    df_with_masses = _calculate_individual_oxide_masses_and_total_slag(df)
    
    # 2. 计算各氧化物的百分比含量
    df_with_percents = df_with_masses.copy()
    
    # 确保总炉渣质量有效
    if 'feature_slag_total_mass_kg' in df_with_percents.columns:
        slag_total_mass = df_with_percents['feature_slag_total_mass_kg']
        
        # 列出需要计算百分比的氧化物
        oxide_masses = {
            'SiO2': 'feature_slag_SiO2_from_Si_kg',
            'MnO': 'feature_slag_MnO_from_Mn_kg',
            'P2O5': 'feature_slag_P2O5_from_P_kg',
            'CaO': 'feature_slag_CaO_total_kg',
            'MgO': 'feature_slag_MgO_total_kg',
            'Al2O3': 'feature_slag_Al2O3_total_kg',
            'FeO': 'feature_slag_FeO_kg'
        }
        
        # 计算每种氧化物的百分比含量
        for oxide_name, mass_col in oxide_masses.items():
            if mass_col in df_with_percents.columns:
                percent_col = f'feature_slag_{oxide_name}_percent'
                df_with_percents[percent_col] = df_with_percents[mass_col] / slag_total_mass * 100
    else:
        logger.warning("无法计算氧化物百分比：缺少 'feature_slag_total_mass_kg' 列")
    
    # 3. 计算炉渣碱度
    df_with_basicity = calculate_slag_basicity(df_with_percents)
    
    # 4. 估算炉渣的物理化学性质
    df_result = estimate_slag_physical_properties(df_with_basicity)
    
    return df_result

def add_quantified_process_parameters(df: pd.DataFrame) -> pd.DataFrame:
    """
    添加量化的工艺参数作为特征。
    这些通常是直接从数据中获取的参数，这里主要是为了明确它们可以作为特征使用。
    """
    df_out = df.copy()
    process_parameter_cols = {
        'feature_process_oxygen_rate': COL_PROCESS_OXYGEN_RATE,
        'feature_process_oxygen_volume': COL_PROCESS_OXYGEN_VOLUME,
        'feature_process_lance_height': COL_PROCESS_LANCE_HEIGHT,
        'feature_process_stirring_power': COL_PROCESS_STIRRING_POWER,
        # 用户可以根据自己的数据添加更多工艺参数
        # 'feature_process_flux_a_amount': COL_PROCESS_FLUX_A_AMOUNT_KG,
    }
    for feature_name, raw_col_name in process_parameter_cols.items():
        if raw_col_name in df_out.columns:
            df_out[feature_name] = df_out[raw_col_name]
            # print(f"信息: 已添加工艺参数特征 {feature_name}。")
    # else:
            # print(f"警告: 工艺参数源列 {raw_col_name} 未找到。")
    return df_out

def add_time_series_derived_features(
    df: pd.DataFrame,
    group_by_col: str, # 用于分组的列，如炉号 COL_TS_HEAT_ID
    time_col: str,     # 时间序列的时间戳列 COL_TS_TIMESTAMP
    target_ts_cols: List[str], # 需要处理的时间序列列名列表，如 [COL_TS_SENSOR_A_READING]
    lag_periods: Optional[List[int]] = None,
    rolling_windows: Optional[List[int]] = None
) -> pd.DataFrame:
    """
    为时间序列数据提取衍生特征（滞后、滑动窗口统计）。
    数据需要先按 group_by_col 分组，然后在组内按 time_col 排序。

    Args:
        df: 输入DataFrame。
        group_by_col: 分组列名（例如炉号）。
        time_col: 时间序列排序列名。
        target_ts_cols: 需要生成衍生特征的原始时间序列列名。
        lag_periods: 滞后周期列表, e.g., [1, 2, 3]。
        rolling_windows: 滑动窗口大小列表, e.g., [3, 5, 10]。
    """
    df_out = df.copy()
    if not (group_by_col in df_out.columns and time_col in df_out.columns):
        print(f"警告: 时间序列分组列 '{group_by_col}' 或时间戳列 '{time_col}' 未在数据中找到，跳过时间序列特征提取。")
        return df_out

    if lag_periods is None: lag_periods = []
    if rolling_windows is None: rolling_windows = []

    # print(f"信息: 开始为列 {target_ts_cols} 生成时间序列特征 (分组依据: {group_by_col}, 时间排序: {time_col})。")

    # 确保数据按分组和时间正确排序是关键
    df_out = df_out.sort_values(by=[group_by_col, time_col])
    
    grouped = df_out.groupby(group_by_col)

    for col_to_process in target_ts_cols:
        if col_to_process not in df_out.columns:
            print(f"警告: 目标时间序列列 {col_to_process} 未找到，跳过。")
            continue

        # 计算滞后特征
        for lag in lag_periods:
            feature_name = f'feature_ts_{col_to_process}_lag_{lag}'
            df_out[feature_name] = grouped[col_to_process].shift(lag)
        
        # 计算滑动窗口统计特征
        for window in rolling_windows:
            # 均值
            mean_feat_name = f'feature_ts_{col_to_process}_roll_mean_{window}'
            df_out[mean_feat_name] = grouped[col_to_process].rolling(window=window, min_periods=1).mean().reset_index(level=0, drop=True)
            # 标准差
            std_feat_name = f'feature_ts_{col_to_process}_roll_std_{window}'
            df_out[std_feat_name] = grouped[col_to_process].rolling(window=window, min_periods=1).std().reset_index(level=0, drop=True)
            # 可以添加更多: min, max, median, sum, var 等
            # max_feat_name = f'feature_ts_{col_to_process}_roll_max_{window}'
            # df_out[max_feat_name] = grouped[col_to_process].rolling(window=window, min_periods=1).max().reset_index(level=0, drop=True)

    # 时间序列分解 (如趋势、季节性、残差) 通常使用 statsmodels 库。
    # 这需要数据是清晰的单变量时间序列，并且具有足够的周期性。
    # 例如: from statsmodels.tsa.seasonal import seasonal_decompose
    # result = seasonal_decompose(df_group[col_to_process], model='additive', period=APPROPRIATE_PERIOD)
    # df_group[f'feature_ts_{col_to_process}_trend'] = result.trend
    # 这部分逻辑较为复杂，需要根据具体数据特性和周期进行调整，此处不作通用实现。
    # print(f"信息: 时间序列特征已为 {target_ts_cols} 生成完毕。")
    return df_out

def add_interaction_features(
    df: pd.DataFrame,
    manual_interaction_pairs: Optional[List[Tuple[str, str]]] = None,
    poly_degree: Optional[int] = None,
    poly_target_cols: Optional[List[str]] = None,
    poly_interaction_only: bool = False,
    poly_include_bias: bool = False
) -> pd.DataFrame:
    """
    添加特征交互项。
    可以手动指定交互对，或者使用多项式特征生成（来自sklearn.preprocessing.PolynomialFeatures）。
    """
    df_out = df.copy()

    # 1. 手动指定的交互特征对
    if manual_interaction_pairs:
        for col1, col2 in manual_interaction_pairs:
            if col1 in df_out.columns and col2 in df_out.columns:
                feature_name = f'feature_interact_{col1}_x_{col2}'
                df_out[feature_name] = df_out[col1] * df_out[col2]
                # logger.info(f"信息: 已创建手动交互特征 {feature_name}。")
            else: # Corresponds to: if col1 in df_out.columns and col2 in df_out.columns
                # This warning is for a specific pair failing
                logger.warning(f"警告: 创建手动交互特征对 ({col1} x {col2}) 失败，因为列 '{col1}' 或 '{col2}' 未在DataFrame中找到。")
    else: # Corresponds to: if manual_interaction_pairs
        logger.info("未提供手动交互特征对 (manual_interaction_pairs)，跳过手动创建部分。")

    # 2. 使用PolynomialFeatures生成多项式和交互特征
    if poly_degree and poly_target_cols:
        from sklearn.preprocessing import PolynomialFeatures
        
        existing_poly_target_cols = [col for col in poly_target_cols if col in df_out.columns]
        if not existing_poly_target_cols:
            print("警告: 未找到用于生成多项式特征的目标列，跳过PolynomialFeatures。")
            return df_out

        poly = PolynomialFeatures(
            degree=poly_degree,
            interaction_only=poly_interaction_only,
            include_bias=poly_include_bias
        )
        
        poly_features_array = poly.fit_transform(df_out[existing_poly_target_cols])
        poly_feature_names = poly.get_feature_names_out(existing_poly_target_cols)
        
        # 将生成的特征数组转换为DataFrame并合并
        # 注意：原始列可能会被包含在输出中（取决于degree和interaction_only），需要小心处理重复列名
        # get_feature_names_out 会给出有意义的列名
        
        for i, name in enumerate(poly_feature_names):
            # 清理一下列名，使其更可读
            clean_name = name.replace(" ", "_").replace("^", "pow")
            df_out[f'feature_poly_{clean_name}'] = poly_features_array[:, i]
        
        # print(f"信息: 已为列 {existing_poly_target_cols} 生成了 {poly_degree} 次多项式/交互特征。")

    # 更高级的特征交互发现方法（如基于树模型的特征重要性分析或决策树路径解析）
    # 不在此处直接实现，但用户可以考虑。
    return df_out

def add_physicochemical_derived_properties(df: pd.DataFrame) -> pd.DataFrame:
    """
    基于现有特征计算具有明确物理或化学意义的派生属性。
    这部分高度依赖领域知识和特定的物理化学公式或模型。
    用户需要根据自己的需求和知识库来实现这部分。
    """
    df_out = df.copy()
    # 示例：假设需要根据钢水温度和某种合金含量计算一个假想的"反应活性指数"
    # if COL_INTERACT_STEEL_TEMP_INITIAL in df_out.columns and COL_INTERACT_ALLOY_X_CONTENT in df_out.columns:
    #     df_out['feature_derived_reactivity_index'] = \
    #         (df_out[COL_INTERACT_STEEL_TEMP_INITIAL] / 1000) * (df_out[COL_INTERACT_ALLOY_X_CONTENT] + 0.1)
    #     print("信息: 已计算(示例)物理化学派生特征 'feature_derived_reactivity_index'。")
    
    logger.info("计算物理化学派生特征")
    return df_out

def calculate_phosphate_capacity(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算炉渣的磷容量系数和脱磷效率指数，基于炉渣成分和FactSage热力学计算。
    
    磷容量系数(Phosphate Capacity)可用来评估炉渣的脱磷能力，
    是表征炉渣-金属界面反应热力学倾向的重要参数。
    
    Args:
        df: 输入数据，包含炉渣各组分含量
        
    Returns:
        增加磷容量系数和脱磷效率指数特征的DataFrame
    """
    logger.info("计算炉渣磷容量系数和脱磷效率指数...")
    df_result = df.copy()
    
    # 检查输入数据是否包含必要的炉渣成分列
    required_slag_components = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                               'feature_slag_MgO_percent', 'feature_slag_FeO_percent']
    
    missing_components = [comp for comp in required_slag_components if comp not in df_result.columns]
    if missing_components:
        logger.warning(f"缺少计算磷容量系数所需的炉渣成分: {', '.join(missing_components)}")
        logger.warning("尝试将在可用的炉渣成分列上进行计算")
    
    # 创建炉渣成分字典，使用默认值填充缺失的组分
    slag_composition = {
        'CaO': df_result.get('feature_slag_CaO_percent', 45.0),  # 典型CaO含量约45%
        'SiO2': df_result.get('feature_slag_SiO2_percent', 15.0), # 典型SiO2含量约15%
        'MgO': df_result.get('feature_slag_MgO_percent', 8.0),    # 典型MgO含量约8%
        'FeO': df_result.get('feature_slag_FeO_percent', 20.0),   # 典型FeO含量约20%
        'MnO': df_result.get('feature_slag_MnO_percent', 5.0),    # 典型MnO含量约5%
        'P2O5': df_result.get('feature_slag_P2O5_percent', 1.0),  # 典型P2O5含量约1%
        'Al2O3': df_result.get('feature_slag_Al2O3_percent', 3.0)  # 典型Al2O3含量约3%
    }
    
    # 计算光学碱度 (Optical Basicity)
    # 参考: "A Model Study of Phosphorus Capacity of Steelmaking Slags"
    optical_basicity_values = {
        'CaO': 1.0,
        'SiO2': 0.48,
        'MgO': 0.78,
        'FeO': 0.93,
        'MnO': 0.97,
        'P2O5': 0.40,
        'Al2O3': 0.60
    }
    
    # 计算各组分的摩尔含量
    molar_fractions = {}
    for component, percent in slag_composition.items():
        # 找到对应的摩尔质量 (已在冶金常量部分定义)
        molar_mass = MOLAR_MASS.get(component, 70.0)  # 默认值70，避免缺失组分导致错误
        moles = percent / molar_mass
        molar_fractions[component] = moles
    
    # 归一化摩尔分数
    total_moles = sum(molar_fractions.values())
    for component in molar_fractions:
        molar_fractions[component] /= total_moles
    
    # 计算光学碱度
    optical_basicity = sum(molar_fractions[component] * optical_basicity_values.get(component, 0.7) 
                          for component in molar_fractions)
    
    df_result['feature_slag_optical_basicity'] = optical_basicity
    
    # 计算磷容量系数 (对数值，基于热力学模型)
    # 基于光学碱度的磷容量系数计算公式
    # log Cp = 22.0 * Λ - 11.0 + 0.02 * T(K)
    # 其中Λ是光学碱度，T是温度(K)
    
    # 如果有温度数据，使用实际温度；否则使用典型的转炉温度1600°C
    temperature_c = 1600  # 默认温度
    if 'feature_steel_temperature_c' in df_result.columns:
        temperature_c = df_result['feature_steel_temperature_c']
    elif COL_HM_TEMP_C in df_result.columns:
        # 如果有铁水温度，假设钢水温度比铁水温度高100°C
        temperature_c = df_result[COL_HM_TEMP_C] + 100
    
    temperature_k = temperature_c + 273.15  # 转换为开尔文
    
    # 计算磷容量系数的对数值
    log_phosphate_capacity = 22.0 * optical_basicity - 11.0 + 0.02 * temperature_k
    
    # 计算磷容量系数
    phosphate_capacity = 10 ** log_phosphate_capacity
    
    df_result['feature_slag_log_phosphate_capacity'] = log_phosphate_capacity
    df_result['feature_slag_phosphate_capacity'] = phosphate_capacity
    
    # 计算脱磷效率指数 (综合碱度、温度和FeO含量的影响)
    # 脱磷效率与 (碱度 × FeO含量) / 温度 成正比
    
    # 计算碱度 (CaO/SiO2)
    if 'feature_slag_CaO_percent' in df_result.columns and 'feature_slag_SiO2_percent' in df_result.columns:
        basicity = df_result['feature_slag_CaO_percent'] / df_result['feature_slag_SiO2_percent']
    else:
        # 使用默认值
        basicity = slag_composition['CaO'] / slag_composition['SiO2']
    
    # 计算修正碱度 ((CaO+MgO)/SiO2)
    if 'feature_slag_MgO_percent' in df_result.columns:
        modified_basicity = (df_result['feature_slag_CaO_percent'] + df_result['feature_slag_MgO_percent']) / df_result['feature_slag_SiO2_percent']
    else:
        modified_basicity = (slag_composition['CaO'] + slag_composition['MgO']) / slag_composition['SiO2']
    
    # 计算脱磷效率指数
    # 根据热力学原理，脱磷效率与温度成反比
    dephosphorization_index = (basicity * slag_composition['FeO']) / temperature_k * 1000
    
    # 修正的脱磷效率指数 (考虑修正碱度和磷容量系数)
    dephosphorization_index_modified = (modified_basicity * slag_composition['FeO'] * phosphate_capacity) / temperature_k * 100
    
    df_result['feature_slag_dephosphorization_index'] = dephosphorization_index
    df_result['feature_slag_dephosphorization_index_modified'] = dephosphorization_index_modified
    
    logger.info(f"炉渣光学碱度范围: {df_result['feature_slag_optical_basicity'].min():.2f}-{df_result['feature_slag_optical_basicity'].max():.2f}")
    logger.info(f"炉渣磷容量系数范围: {df_result['feature_slag_phosphate_capacity'].min():.2e}-{df_result['feature_slag_phosphate_capacity'].max():.2e}")
    logger.info(f"脱磷效率指数范围: {df_result['feature_slag_dephosphorization_index'].min():.2f}-{df_result['feature_slag_dephosphorization_index'].max():.2f}")
    
    return df_result

def calculate_slag_phase_properties(df: pd.DataFrame) -> pd.DataFrame:
    """
    基于炉渣成分计算炉渣的相平衡特性，包括：
    - 液相百分比
    - 熔融温度区间
    - 固液界面特性
    
    该模型基于FactSage热力学数据库的计算结果构建的简化模型。
    
    Args:
        df: 输入数据，包含炉渣各组分含量
        
    Returns:
        增加炉渣相平衡特性特征的DataFrame
    """
    logger.info("计算炉渣相平衡特性...")
    df_result = df.copy()
    
    # 检查输入数据是否包含必要的炉渣成分列
    required_slag_components = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                               'feature_slag_MgO_percent', 'feature_slag_FeO_percent']
    
    missing_components = [comp for comp in required_slag_components if comp not in df_result.columns]
    if missing_components:
        logger.warning(f"缺少计算炉渣相平衡特性所需的炉渣成分: {', '.join(missing_components)}")
        logger.warning("尝试将在可用的炉渣成分列上进行计算")
    
    # 创建炉渣成分字典，使用默认值填充缺失的组分
    slag_composition = {
        'CaO': df_result.get('feature_slag_CaO_percent', 45.0),  # 典型CaO含量约45%
        'SiO2': df_result.get('feature_slag_SiO2_percent', 15.0), # 典型SiO2含量约15%
        'MgO': df_result.get('feature_slag_MgO_percent', 8.0),    # 典型MgO含量约8%
        'FeO': df_result.get('feature_slag_FeO_percent', 20.0),   # 典型FeO含量约20%
        'MnO': df_result.get('feature_slag_MnO_percent', 5.0),    # 典型MnO含量约5%
        'P2O5': df_result.get('feature_slag_P2O5_percent', 1.0),  # 典型P2O5含量约1%
        'Al2O3': df_result.get('feature_slag_Al2O3_percent', 3.0), # 典型Al2O3含量约3%
        'Fe2O3': df_result.get('feature_slag_Fe2O3_percent', 3.0)  # 典型Fe2O3含量约3%
    }
    
    # 计算炉渣的液相点和固相点温度
    # 这里使用基于FactSage数据的回归模型
    
    # 1. 计算碱度比 (B = CaO/SiO2)
    if 'feature_slag_CaO_percent' in df_result.columns and 'feature_slag_SiO2_percent' in df_result.columns:
        basicity = df_result['feature_slag_CaO_percent'] / df_result['feature_slag_SiO2_percent']
    else:
        basicity = slag_composition['CaO'] / slag_composition['SiO2']
    
    # 2. 计算三元相图位置参数 (CaO-SiO2-FeO系)
    # 归一化主要组分
    total_major = slag_composition['CaO'] + slag_composition['SiO2'] + slag_composition['FeO']
    cao_norm = slag_composition['CaO'] / total_major * 100
    sio2_norm = slag_composition['SiO2'] / total_major * 100
    feo_norm = slag_composition['FeO'] / total_major * 100
    
    # 3. 计算液相线温度 (°C)
    # 该公式是基于CaO-SiO2-FeO三元系统的简化模型
    # 参考：基于FactSage计算优化转炉脱磷工艺的基础研究
    liquidus_temp = (
        1200  # 基础温度
        - 3.5 * feo_norm  # FeO降低液相线温度
        + 10 * (basicity - 2.5) ** 2  # 碱度偏离最佳值2.5时升高液相线温度
        + 15 * slag_composition['MgO']  # MgO升高液相线温度
        - 8 * slag_composition['P2O5']  # P2O5降低液相线温度
        - 5 * slag_composition['MnO']   # MnO降低液相线温度
    )
    
    # 4. 计算固相线温度 (°C)
    # 固相线温度通常比液相线温度低100-200°C
    # 具体差值受炉渣成分影响，特别是MgO和Al2O3含量
    solidus_temp = (
        liquidus_temp 
        - 150  # 基础差值
        + 10 * slag_composition['MgO']  # MgO增大温度间隔
        + 8 * slag_composition['Al2O3']  # Al2O3增大温度间隔
        - 5 * slag_composition['FeO']  # FeO减小温度间隔
    )
    
    # 5. 如果有温度数据，计算当前温度下的液相百分比
    temperature_c = 1600  # 默认温度
    if 'feature_steel_temperature_c' in df_result.columns:
        temperature_c = df_result['feature_steel_temperature_c']
    elif COL_HM_TEMP_C in df_result.columns:
        # 如果有铁水温度，假设钢水温度比铁水温度高100°C
        temperature_c = df_result[COL_HM_TEMP_C] + 100
    
    # 液相百分比计算
    # 低于固相线：0% 液相
    # 高于液相线：100% 液相
    # 在固-液共存区：线性插值
    liquid_percent = np.zeros_like(temperature_c, dtype=float)
    
    # 完全固态
    solid_mask = temperature_c < solidus_temp
    liquid_percent[solid_mask] = 0.0
    
    # 完全液态
    liquid_mask = temperature_c > liquidus_temp
    liquid_percent[liquid_mask] = 100.0
    
    # 固液共存区
    pasty_mask = ~(solid_mask | liquid_mask)
    liquid_percent[pasty_mask] = (
        (temperature_c[pasty_mask] - solidus_temp[pasty_mask]) / 
        (liquidus_temp[pasty_mask] - solidus_temp[pasty_mask]) * 100.0
    )
    
    # 6. 计算炉渣结晶性指数 (与固态相的存在相关)
    # 该指数越低，炉渣越接近完全液态，流动性越好
    crystallinity_index = np.zeros_like(temperature_c, dtype=float)
    
    # 完全固态
    crystallinity_index[solid_mask] = 100.0
    
    # 完全液态
    crystallinity_index[liquid_mask] = 0.0
    
    # 固液共存区 - 非线性关系，考虑黏度变化
    crystallinity_index[pasty_mask] = (
        100.0 - liquid_percent[pasty_mask] + 
        20.0 * (1.0 - liquid_percent[pasty_mask] / 100.0) ** 2  # 非线性项，反映固相黏度贡献
    )
    
    # 7. 计算钢水-炉渣界面特性指数
    # 该指数反映钢水-炉渣界面的传质和反应性质
    # 影响因素：液相百分比、温度、FeO含量
    interface_index = (
        liquid_percent / 100.0 *  # 液相比例影响
        (0.8 + 0.2 * slag_composition['FeO'] / 30.0) *  # FeO含量影响 (归一化到30%)
        (0.9 + 0.1 * (temperature_c - 1500) / 200.0)  # 温度影响 (归一化到1500-1700°C)
    )
    
    # 8. 保存计算结果
    df_result['feature_slag_liquidus_temp_c'] = liquidus_temp
    df_result['feature_slag_solidus_temp_c'] = solidus_temp
    df_result['feature_slag_liquid_percent'] = liquid_percent
    df_result['feature_slag_crystallinity_index'] = crystallinity_index
    df_result['feature_slag_interface_index'] = interface_index
    
    # 9. 计算炉渣熔融区间宽度
    df_result['feature_slag_melting_range_c'] = liquidus_temp - solidus_temp
    
    logger.info(f"炉渣液相线温度范围: {df_result['feature_slag_liquidus_temp_c'].min():.1f}-{df_result['feature_slag_liquidus_temp_c'].max():.1f}°C")
    logger.info(f"炉渣液相百分比范围: {df_result['feature_slag_liquid_percent'].min():.1f}-{df_result['feature_slag_liquid_percent'].max():.1f}%")
    logger.info(f"炉渣熔融区间宽度范围: {df_result['feature_slag_melting_range_c'].min():.1f}-{df_result['feature_slag_melting_range_c'].max():.1f}°C")
    
    return df_result

def calculate_viscosity_from_factsage(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算炉渣粘度，基于FactSage热力学数据和网络结构模型。
    
    该模型将炉渣视为硅氧网络结构，其中:
    - 网络形成体 (Network Formers): 主要是SiO2和P2O5，形成连续的网络结构
    - 网络修饰体 (Network Modifiers): 主要是CaO和MgO，打断网络结构
    - 两性氧化物 (Amphoteric Oxides): 如Al2O3，根据环境可充当形成体或修饰体
    
    Args:
        df: 输入数据，包含炉渣各组分含量
        
    Returns:
        增加炉渣粘度和网络结构特征的DataFrame
    """
    logger.info("计算炉渣粘度和网络结构特性...")
    df_result = df.copy()
    
    # 检查输入数据是否包含必要的炉渣成分列
    required_slag_components = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                               'feature_slag_MgO_percent', 'feature_slag_FeO_percent']
    
    missing_components = [comp for comp in required_slag_components if comp not in df_result.columns]
    if missing_components:
        logger.warning(f"缺少计算炉渣粘度所需的炉渣成分: {', '.join(missing_components)}")
        logger.warning("尝试将在可用的炉渣成分列上进行计算")
    
    # 创建炉渣成分字典，使用默认值填充缺失的组分
    slag_composition = {
        'CaO': df_result.get('feature_slag_CaO_percent', 45.0),  # 典型CaO含量约45%
        'SiO2': df_result.get('feature_slag_SiO2_percent', 15.0), # 典型SiO2含量约15%
        'MgO': df_result.get('feature_slag_MgO_percent', 8.0),    # 典型MgO含量约8%
        'FeO': df_result.get('feature_slag_FeO_percent', 20.0),   # 典型FeO含量约20%
        'MnO': df_result.get('feature_slag_MnO_percent', 5.0),    # 典型MnO含量约5%
        'P2O5': df_result.get('feature_slag_P2O5_percent', 1.0),  # 典型P2O5含量约1%
        'Al2O3': df_result.get('feature_slag_Al2O3_percent', 3.0), # 典型Al2O3含量约3%
        'Fe2O3': df_result.get('feature_slag_Fe2O3_percent', 3.0)  # 典型Fe2O3含量约3%
    }
    
    # 1. 分类各氧化物为网络形成体和网络修饰体
    network_formers = {
        'SiO2': slag_composition['SiO2'],
        'P2O5': slag_composition['P2O5']
    }
    
    network_modifiers = {
        'CaO': slag_composition['CaO'],
        'MgO': slag_composition['MgO'],
        'FeO': slag_composition['FeO'],
        'MnO': slag_composition['MnO']
    }
    
    amphoteric_oxides = {
        'Al2O3': slag_composition['Al2O3'],
        'Fe2O3': slag_composition['Fe2O3']
    }
    
    # 2. 计算各类氧化物的总摩尔数
    # 计算氧化物的摩尔数 (mol%)
    former_moles = {}
    for oxide, percent in network_formers.items():
        molar_mass = MOLAR_MASS.get(oxide, 60.0)  # 默认值60，避免缺失组分导致错误
        former_moles[oxide] = percent / molar_mass
    
    modifier_moles = {}
    for oxide, percent in network_modifiers.items():
        molar_mass = MOLAR_MASS.get(oxide, 60.0)
        modifier_moles[oxide] = percent / molar_mass
    
    amphoteric_moles = {}
    for oxide, percent in amphoteric_oxides.items():
        molar_mass = MOLAR_MASS.get(oxide, 100.0)
        amphoteric_moles[oxide] = percent / molar_mass
    
    # 计算总摩尔数
    total_former_moles = sum(former_moles.values())
    total_modifier_moles = sum(modifier_moles.values())
    total_amphoteric_moles = sum(amphoteric_moles.values())
    
    # 3. 计算网络形成体与修饰体的比例
    # 在碱性炉渣中，部分Al2O3可能作为网络修饰体
    # 而在酸性炉渣中，部分Fe2O3可能作为网络形成体
    # 这里根据碱度判断Al2O3和Fe2O3的行为
    
    # 碱度指数
    if 'feature_slag_basicity_R2' in df_result.columns:
        basicity = df_result['feature_slag_basicity_R2']
    else:
        basicity = slag_composition['CaO'] / slag_composition['SiO2']
    
    # 根据碱度调整Al2O3的行为
    al2o3_as_former_ratio = np.where(basicity < 2.0, 0.7, 0.3)  # 碱度低时，更多Al2O3作为网络形成体
    fe2o3_as_former_ratio = np.where(basicity < 1.5, 0.5, 0.1)  # 碱度低时，更多Fe2O3作为网络形成体
    
    # 调整网络形成体和修饰体的总摩尔数
    adjusted_former_moles = total_former_moles + \
                            al2o3_as_former_ratio * amphoteric_moles.get('Al2O3', 0) + \
                            fe2o3_as_former_ratio * amphoteric_moles.get('Fe2O3', 0)
    
    adjusted_modifier_moles = total_modifier_moles + \
                             (1 - al2o3_as_former_ratio) * amphoteric_moles.get('Al2O3', 0) + \
                             (1 - fe2o3_as_former_ratio) * amphoteric_moles.get('Fe2O3', 0)
    
    # 4. 计算网络形成体与修饰体的摩尔比
    network_former_ratio = adjusted_former_moles / (adjusted_former_moles + adjusted_modifier_moles)
    
    # 5. 计算粘度 (基于温度和网络形成体比例)
    # 参考: Song W. et al. "Viscosity and Structure of Silicate Melts"
    
    # 获取温度数据 (如果有)
    temperature_c = 1600  # 默认温度
    if 'feature_steel_temperature_c' in df_result.columns:
        temperature_c = df_result['feature_steel_temperature_c']
    elif COL_HM_TEMP_C in df_result.columns:
        # 如果有铁水温度，假设钢水温度比铁水温度高100°C
        temperature_c = df_result[COL_HM_TEMP_C] + 100
    
    temperature_k = temperature_c + 273.15  # 转换为开尔文
    
    # 修正的Arrhenius-Andrade方程
    # ln(η) = A + B/T + C*NWF
    # 其中NWF是网络形成体比例
    A = -6.0  # 基础常数
    B = 30000  # 温度相关系数
    C = 10.0   # 网络形成体相关系数
    
    ln_viscosity = A + B / temperature_k + C * network_former_ratio
    viscosity = np.exp(ln_viscosity)  # 单位: Pa·s
    
    # 转换为poise (1 Pa·s = 10 poise)
    viscosity_poise = viscosity * 10
    
    # 6. 修正粘度计算，考虑液相百分比的影响
    if 'feature_slag_liquid_percent' in df_result.columns:
        liquid_percent = df_result['feature_slag_liquid_percent']
        
        # 当液相百分比接近100%时，使用计算的粘度
        # 当液相百分比较低时，粘度急剧增加
        liquid_fraction = liquid_percent / 100.0
        
        # 全液相时的粘度为计算值，固相增加时粘度增加
        # 使用指数模型反映固相对粘度的急剧影响
        effective_viscosity = viscosity_poise * (1 + 10 * (1 - liquid_fraction) ** 3)
        
        # 限制粘度上限
        effective_viscosity = np.minimum(effective_viscosity, 1000.0)
        
        df_result['feature_slag_viscosity'] = effective_viscosity
    else:
        df_result['feature_slag_viscosity'] = viscosity_poise
    
    # 7. 保存网络结构特征
    df_result['feature_slag_network_former_ratio'] = network_former_ratio
    
    # 计算网络连接度指数
    # 连接度与网络形成体比例和碱度相关
    # connectivity_index = network_former_ratio * (1 + 0.2 * (2.0 - basicity).clip(0, 2))
    # MODIFIED to handle scalar basicity
    clipped_basicity_effect = np.clip(2.0 - basicity, 0, 2) 
    connectivity_index = network_former_ratio * (1 + 0.2 * clipped_basicity_effect)
    df_result['feature_slag_connectivity_index'] = connectivity_index
    
    logger.info(f"炉渣粘度范围: {df_result['feature_slag_viscosity'].min():.2f}-{df_result['feature_slag_viscosity'].max():.2f} poise")
    logger.info(f"网络形成体比例范围: {df_result['feature_slag_network_former_ratio'].min():.2f}-{df_result['feature_slag_network_former_ratio'].max():.2f}")
    
    return df_result

def add_lance_dynamics_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    添加喷枪动态特征，基于喷枪位置、角度和供氧参数。
    
    参考《转炉氧枪枪位对熔池作用规律研究》的研究结果。
    
    Args:
        df: 输入数据，包含喷枪位置和供氧参数
        
    Returns:
        增加喷枪动态特征的DataFrame
    """
    logger.info("添加喷枪动态特征...")
    df_result = df.copy()
    
    # 检查必要的输入列
    lance_height_col = None
    if COL_LANCE_HEIGHT_MM in df_result.columns:
        lance_height_col = COL_LANCE_HEIGHT_MM
    elif '枪位' in df_result.columns:
        lance_height_col = '枪位'
    
    oxygen_flow_col = None
    if COL_OXYGEN_TOTAL_NM3 in df_result.columns:
        oxygen_flow_col = COL_OXYGEN_TOTAL_NM3
    elif '累氧实际' in df_result.columns:
        oxygen_flow_col = '累氧实际'
    
    blowing_time_col = None
    if COL_BLOWING_TIME_SEC in df_result.columns:
        blowing_time_col = COL_BLOWING_TIME_SEC
    elif '吹氧时间s' in df_result.columns:
        blowing_time_col = '吹氧时间s'
    
    lance_angle_col = None
    if COL_LANCE_ANGLE_MAX in df_result.columns:
        lance_angle_col = COL_LANCE_ANGLE_MAX
    elif '最大角度' in df_result.columns:
        lance_angle_col = '最大角度'
    
    # 检查必要列是否存在
    if lance_height_col is None:
        logger.warning("缺少喷枪高度列，无法计算喷枪动态特征")
        return df_result
    
    # 1. 计算喷射动量指数 (Jet Momentum Index)
    # 动量与供氧速率和喷枪高度的平方成反比
    if oxygen_flow_col is not None and blowing_time_col is not None:
        # 计算供氧速率 (Nm³/min)
        oxygen_flow_rate = df_result[oxygen_flow_col] / (df_result[blowing_time_col] / 60)
        df_result['feature_lance_oxygen_flow_rate'] = oxygen_flow_rate
        
        # 计算喷射动量指数
        # 动量 ∝ (供氧速率) / (喷枪高度)²
        lance_height_m = df_result[lance_height_col] / 1000  # 转换为米
        jet_momentum = oxygen_flow_rate / (lance_height_m ** 2)
        df_result['feature_lance_jet_momentum_index'] = jet_momentum
        
        logger.info(f"喷射动量指数范围: {jet_momentum.min():.1f}-{jet_momentum.max():.1f}")
    else:
        logger.warning("缺少供氧量或吹氧时间列，无法计算喷射动量指数")
    
    # 2. 计算冲击深度指数 (Impact Depth Index)
    # 冲击深度与动量成正比，与熔池阻力成反比
    if 'feature_lance_jet_momentum_index' in df_result.columns:
        # 简化模型，假设熔池阻力为常数
        impact_depth = 0.05 * df_result['feature_lance_jet_momentum_index'] ** 0.5
        df_result['feature_lance_impact_depth_index'] = impact_depth
        
        logger.info(f"冲击深度指数范围: {impact_depth.min():.2f}-{impact_depth.max():.2f}")
    
    # 3. 计算表面搅拌强度指数 (Surface Stirring Intensity)
    # 表面搅拌强度与动量和喷枪高度成正比
    if 'feature_lance_jet_momentum_index' in df_result.columns:
        surface_stirring = df_result['feature_lance_jet_momentum_index'] * (df_result[lance_height_col] / 1000)
        df_result['feature_lance_surface_stirring_index'] = surface_stirring
        
        logger.info(f"表面搅拌强度指数范围: {surface_stirring.min():.1f}-{surface_stirring.max():.1f}")
    
    # 4. 计算表面剪切应力指数 (Surface Shear Stress)
    # 表面剪切应力与动量成正比，与喷枪高度的平方成反比
    if 'feature_lance_jet_momentum_index' in df_result.columns:
        surface_shear_stress = df_result['feature_lance_jet_momentum_index'] / (df_result[lance_height_col] / 1000)
        df_result['feature_lance_surface_shear_stress'] = surface_shear_stress
        
        logger.info(f"表面剪切应力指数范围: {surface_shear_stress.min():.1f}-{surface_shear_stress.max():.1f}")
    
    # 5. 考虑喷枪角度的影响 (如果有角度数据)
    if lance_angle_col is not None:
        # 有效动量系数 = cos(角度) - 垂直分量
        angle_rad = np.radians(df_result[lance_angle_col])
        effective_momentum_factor = np.cos(angle_rad)
        
        # 修正各指数，考虑角度影响
        if 'feature_lance_jet_momentum_index' in df_result.columns:
            df_result['feature_lance_effective_momentum'] = df_result['feature_lance_jet_momentum_index'] * effective_momentum_factor
            
        if 'feature_lance_impact_depth_index' in df_result.columns:
            df_result['feature_lance_effective_impact_depth'] = df_result['feature_lance_impact_depth_index'] * effective_momentum_factor
            
        logger.info(f"考虑了喷枪角度的影响，角度范围: {df_result[lance_angle_col].min():.1f}°-{df_result[lance_angle_col].max():.1f}°")
    
    # 6. 计算氧气利用率估算值 (Oxygen Utilization)
    # 氧气利用率与喷枪位置、供氧强度和熔池状态有关
    if oxygen_flow_col is not None and lance_height_col is not None:
        # 简化模型：氧气利用率随喷枪高度增加而降低，但有上下限
        # 标准喷枪高度范围通常为800-1200mm
        norm_height = (df_result[lance_height_col] - 800) / 400  # 归一化到[0,1]范围
        norm_height = norm_height.clip(0, 1)  # 限制在[0,1]范围内
        
        # 氧气利用率估算值 (90%-75%范围，随喷枪高度增加而降低)
        oxygen_utilization = 0.90 - 0.15 * norm_height
        
        # 如果有FeO含量数据，可以进一步修正
        if 'feature_slag_FeO_percent' in df_result.columns:
            # FeO含量高表示氧气利用率较低
            feo_factor = df_result['feature_slag_FeO_percent'] / 30.0  # 归一化到典型值30%
            feo_factor = feo_factor.clip(0.5, 1.5)  # 限制修正系数范围
            
            # 修正氧气利用率
            oxygen_utilization = oxygen_utilization / feo_factor
            
        df_result['feature_lance_oxygen_utilization'] = oxygen_utilization
        logger.info(f"氧气利用率估算范围: {oxygen_utilization.min():.2f}-{oxygen_utilization.max():.2f}")
    
    # 7. 添加底吹搅拌强度特征 (如果有底吹数据)
    if COL_BOTTOM_BLOWING_INTENSITY in df_result.columns:
        # 底吹搅拌功率 ∝ 底吹强度
        bottom_stirring_power = df_result[COL_BOTTOM_BLOWING_INTENSITY] * 10  # 假设系数为10
        df_result['feature_bottom_stirring_power'] = bottom_stirring_power
        
        # 如果有顶吹特征，计算顶底复合搅拌指数
        if 'feature_lance_surface_stirring_index' in df_result.columns:
            combined_stirring = (
                df_result['feature_lance_surface_stirring_index'] + 
                bottom_stirring_power
            )
            df_result['feature_combined_stirring_index'] = combined_stirring
            
            logger.info(f"顶底复合搅拌指数范围: {combined_stirring.min():.1f}-{combined_stirring.max():.1f}")
    
    return df_result

def add_slag_process_interactions(df: pd.DataFrame) -> pd.DataFrame:
    """
    创建炉渣特性与工艺参数之间的交互特征。
    
    这些交互特征可以捕捉炉渣与工艺相互作用对钢水温度的影响。
    
    Args:
        df: 输入数据，包含炉渣特性和工艺参数
        
    Returns:
        增加炉渣-工艺交互特征的DataFrame
    """
    logger.info("创建炉渣-工艺参数交互特征...")
    df_result = df.copy()
    
    # 定义可能的炉渣特性列和工艺参数列
    slag_features = [
        'feature_slag_basicity_R2',           # 炉渣碱度
        'feature_slag_FeO_percent',           # FeO含量
        'feature_slag_liquid_percent',        # 液相百分比
        'feature_slag_viscosity',             # 炉渣粘度
        'feature_slag_liquidus_temp_c',       # 液相线温度
        'feature_slag_optical_basicity',      # 光学碱度
        'feature_slag_phosphate_capacity',    # 磷容量系数
        'feature_slag_network_former_ratio'   # 网络形成体比例
    ]
    
    process_features = [
        'feature_lance_jet_momentum_index',    # 喷射动量指数
        'feature_lance_oxygen_flow_rate',      # 供氧速率
        'feature_lance_surface_stirring_index',# 表面搅拌强度
        'feature_lance_oxygen_utilization',    # 氧气利用率
        'feature_bottom_stirring_power',       # 底吹搅拌功率
        COL_LANCE_HEIGHT_MM,                   # 喷枪高度
        '吹氧时间s',                           # 吹氧时间
        '单位铁水供氧量'                       # 单位铁水供氧量
    ]
    
    # 过滤出实际存在的特征列
    available_slag_features = [col for col in slag_features if col in df_result.columns]
    available_process_features = [col for col in process_features if col in df_result.columns]
    
    if not available_slag_features:
        logger.warning("未找到任何炉渣特性列，无法创建交互特征")
        return df_result
    
    if not available_process_features:
        logger.warning("未找到任何工艺参数列，无法创建交互特征")
        return df_result
    
    # 创建有意义的交互特征
    interaction_pairs = [
        # 1. 炉渣碱度与供氧量/喷枪位置的交互 - 影响脱碳、脱磷速率和热量释放
        ('feature_slag_basicity_R2', '单位铁水供氧量', 'slag_basicity_x_oxygen_per_ton'),
        ('feature_slag_basicity_R2', COL_LANCE_HEIGHT_MM, 'slag_basicity_x_lance_height'),
        
        # 2. 炉渣FeO含量与供氧量/喷枪位置的交互 - 影响氧平衡和氧化反应速率
        ('feature_slag_FeO_percent', '单位铁水供氧量', 'slag_feo_x_oxygen_per_ton'),
        ('feature_slag_FeO_percent', COL_LANCE_HEIGHT_MM, 'slag_feo_x_lance_height'),
        
        # 3. 炉渣液相百分比与搅拌强度的交互 - 影响界面反应速率
        ('feature_slag_liquid_percent', 'feature_lance_surface_stirring_index', 'slag_liquid_x_stirring'),
        
        # 4. 炉渣粘度与搅拌强度的交互 - 影响质量传递效率
        ('feature_slag_viscosity', 'feature_lance_surface_stirring_index', 'slag_viscosity_x_stirring'),
        
        # 5. 炉渣磷容量与供氧利用率的交互 - 影响脱磷效率与放热量
        ('feature_slag_phosphate_capacity', 'feature_lance_oxygen_utilization', 'slag_pcap_x_o2_util'),
        
        # 6. 炉渣光学碱度与喷射动量的交互 - 影响界面反应速率
        ('feature_slag_optical_basicity', 'feature_lance_jet_momentum_index', 'slag_basicity_x_jet_momentum')
    ]
    
    # 过滤出可以实际计算的交互对
    valid_pairs = []
    for feature1, feature2, name in interaction_pairs:
        if feature1 in df_result.columns and feature2 in df_result.columns:
            valid_pairs.append((feature1, feature2, name))
    
    # 创建交互特征
    for feature1, feature2, name in valid_pairs:
        # 创建交互特征，命名为"feature_interact_XXXX"
        interaction_name = f'feature_interact_{name}'
        df_result[interaction_name] = df_result[feature1] * df_result[feature2]
        logger.info(f"创建交互特征: {interaction_name} = {feature1} × {feature2}")
    
    # 创建复合交互特征，涉及多个参数
    
    # 1. 炉渣流动性指数 (Slag Fluidity Index)
    # 该指数与液相百分比成正比，与粘度成反比，与搅拌强度成正比
    if all(col in df_result.columns for col in ['feature_slag_liquid_percent', 'feature_slag_viscosity']):
        fluidity_index = (
            df_result['feature_slag_liquid_percent'] / 
            df_result['feature_slag_viscosity']
        )
        
        # 如果有搅拌强度数据，考虑其影响
        if 'feature_lance_surface_stirring_index' in df_result.columns:
            stirring_factor = 1 + 0.1 * df_result['feature_lance_surface_stirring_index'] / df_result['feature_lance_surface_stirring_index'].mean()
            fluidity_index = fluidity_index * stirring_factor
        
        df_result['feature_interact_slag_fluidity_index'] = fluidity_index
        logger.info("创建了炉渣流动性指数 (液相百分比/粘度/搅拌强度)")
    
    # 2. 热传递效率指数 (Heat Transfer Efficiency Index)
    # 该指数与界面特性和搅拌强度相关
    if 'feature_slag_interface_index' in df_result.columns:
        heat_transfer_index = df_result['feature_slag_interface_index']
        
        # 如果有搅拌数据，考虑其影响
        if 'feature_lance_surface_stirring_index' in df_result.columns:
            heat_transfer_index = heat_transfer_index * (
                1 + 0.2 * df_result['feature_lance_surface_stirring_index'] / 
                df_result['feature_lance_surface_stirring_index'].mean()
            )
        
        # 如果有FeO含量数据，考虑其影响 (FeO含量高会增加热传递)
        if 'feature_slag_FeO_percent' in df_result.columns:
            heat_transfer_index = heat_transfer_index * (
                1 + 0.1 * (df_result['feature_slag_FeO_percent'] - 20) / 10
            )
        
        df_result['feature_interact_heat_transfer_index'] = heat_transfer_index
        logger.info("创建了热传递效率指数 (界面特性/搅拌强度/FeO含量)")
    
    # 3. 炉渣修饰反应指数 (Slag Modification Reaction Index)
    # 该指数衡量炉渣对钢水温度的综合影响
    if all(col in df_result.columns for col in ['feature_slag_basicity_R2', '单位铁水供氧量']):
        # 基础指数基于碱度和供氧量
        slag_mod_index = df_result['feature_slag_basicity_R2'] * df_result['单位铁水供氧量'] / 100
        
        # 如果有液相百分比数据，考虑其影响
        if 'feature_slag_liquid_percent' in df_result.columns:
            slag_mod_index = slag_mod_index * df_result['feature_slag_liquid_percent'] / 100
        
        # 如果有氧气利用率数据，考虑其影响
        if 'feature_lance_oxygen_utilization' in df_result.columns:
            slag_mod_index = slag_mod_index * df_result['feature_lance_oxygen_utilization']
        
        df_result['feature_interact_slag_modification_index'] = slag_mod_index
        logger.info("创建了炉渣修饰反应指数 (碱度/供氧量/液相百分比/氧利用率)")
    
    # 记录创建的特征数量
    new_features = [col for col in df_result.columns if col.startswith('feature_interact_')]
    logger.info(f"共创建了 {len(new_features)} 个炉渣-工艺交互特征")
    
    return df_result

def _calculate_slag_oxide_percentages(df: pd.DataFrame) -> pd.DataFrame:
    """
    基于各氧化物质量和总炉渣质量，计算各氧化物的百分比。
    确保在调用此函数前，COL_SLAG_TOTAL_MASS_KG ('feature_slag_total_mass_kg') 和
    各氧化物的质量列 (如 'feature_slag_SiO2_from_Si_kg') 已被计算。
    """
    df_result = df.copy()
    logger.info("开始计算炉渣各氧化物百分比...")

    oxide_mass_cols_map = {
        'feature_slag_SiO2_percent': 'feature_slag_SiO2_from_Si_kg',
        'feature_slag_MnO_percent': 'feature_slag_MnO_from_Mn_kg',
        'feature_slag_P2O5_percent': 'feature_slag_P2O5_from_P_kg',
        'feature_slag_CaO_percent': 'feature_slag_CaO_total_kg',
        'feature_slag_MgO_percent': 'feature_slag_MgO_total_kg',
        'feature_slag_Al2O3_percent': 'feature_slag_Al2O3_total_kg',
        # FeO 和 Fe2O3 的百分比应该已经在 calculate_fe2o3_content 中直接计算并赋值了，
        # 这里不需要根据质量反算，但如果需要也可以加入。
        # 'feature_slag_FeO_percent': 'feature_slag_FeO_kg', # 这通常不这么做
        # 'feature_slag_Fe2O3_percent': 'feature_slag_Fe2O3_kg', # 这通常不这么做
    }

    if COL_SLAG_TOTAL_MASS_KG not in df_result.columns or df_result[COL_SLAG_TOTAL_MASS_KG].isna().all():
        logger.error(f"列 '{COL_SLAG_TOTAL_MASS_KG}' 缺失或全为NaN，无法计算氧化物百分比。")
        # 为避免下游函数出错，可以尝试为百分比列填充0或默认值
        for percent_col in oxide_mass_cols_map.keys():
            if percent_col not in df_result.columns:
                df_result[percent_col] = 0
        return df_result
    
    # 替换除以0的情况
    total_slag_mass_safe = df_result[COL_SLAG_TOTAL_MASS_KG].replace(0, np.nan) # 使用np.nan使得除法结果为nan，后续可fillna

    for percent_col, mass_col in oxide_mass_cols_map.items():
        if mass_col in df_result.columns:
            df_result[percent_col] = (df_result[mass_col] / total_slag_mass_safe) * 100
            df_result[percent_col].fillna(0, inplace=True) # 将除以0或NaN导致的NaN填充为0
            logger.info(f"计算了 '{percent_col}', 平均值: {df_result[percent_col].mean():.2f}%")
        else:
            logger.warning(f"质量列 '{mass_col}' 未找到，无法计算 '{percent_col}'。已填充为0。")
            df_result[percent_col] = 0
            
    # 验证所有百分比特征都已创建
    # for percent_col in oxide_mass_cols_map.keys():
    #     if percent_col not in df_result.columns:
    #         logger.warning(f"氧化物百分比列 '{percent_col}' 在计算后仍未创建！")
    #         df_result[percent_col] = 0 # 确保存在

    logger.info("炉渣各氧化物百分比计算完成。")
    return df_result

def add_advanced_slag_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    整合所有基于FactSage的高级炉渣特征计算。
    """
    logger.info("计算基于FactSage的高级炉渣特征")
    df_result = df.copy()
    logger.info(f"[add_advanced_slag_features START] df_result (df copy) columns: {df_result.columns.tolist()}")

    # 关键步骤1: 确保 FeO 和 Fe2O3 百分比已计算 (由 calculate_fe2o3_content 完成)
    # 这个函数现在也负责更新总炉渣质量 COL_SLAG_TOTAL_MASS_KG
    logger.info(f"[BEFORE calculate_fe2o3_content in add_advanced_slag_features] df_result columns: {df_result.columns.tolist()}")
    df_result = calculate_fe2o3_content(df_result) 
    logger.info(f"[AFTER calculate_fe2o3_content in add_advanced_slag_features] df_result columns: {df_result.columns.tolist()}")

    # 关键步骤2: 基于各氧化物质量和更新后的总炉渣质量，计算其他氧化物百分比
    logger.info(f"[BEFORE _calculate_slag_oxide_percentages] df_result columns: {df_result.columns.tolist()}")
    df_result = _calculate_slag_oxide_percentages(df_result)
    logger.info(f"[AFTER _calculate_slag_oxide_percentages] df_result columns: {df_result.columns.tolist()}")

    # 现在，其他高级特征计算函数应该可以找到它们需要的百分比列了
    logger.info(f"[BEFORE calculate_phosphate_capacity] df_result columns: {df_result.columns.tolist()}")
    df_result = calculate_phosphate_capacity(df_result)
    logger.info(f"[AFTER calculate_phosphate_capacity] df_result columns: {df_result.columns.tolist()}")
    
    logger.info(f"[BEFORE calculate_slag_phase_properties] df_result columns: {df_result.columns.tolist()}")
    df_result = calculate_slag_phase_properties(df_result)
    logger.info(f"[AFTER calculate_slag_phase_properties] df_result columns: {df_result.columns.tolist()}")
    
    logger.info(f"[BEFORE calculate_viscosity_from_factsage] df_result columns: {df_result.columns.tolist()}")
    df_result = calculate_viscosity_from_factsage(df_result)
    logger.info(f"[AFTER calculate_viscosity_from_factsage] df_result columns: {df_result.columns.tolist()}")
    
    logger.info(f"[add_advanced_slag_features END] df_result columns: {df_result.columns.tolist()}")
    return df_result

def _calculate_individual_oxide_masses_and_total_slag(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算各种氧化物的质量以及总炉渣量。
    这个函数现在更专注于计算氧化物质量，总炉渣量的精确计算和FeO的估算/分配会更依赖后续步骤或专门的函数。
    """
    logger.info("开始计算各个氧化物的质量...")
    df_result = df.copy()

    # --- Helper function for numeric conversion and logging ---
    def convert_to_numeric_and_log(df_ref, col_name_original, col_name_numeric_suffix='_numeric'):
        col_name_numeric = f"{col_name_original}{col_name_numeric_suffix}"
        if col_name_original in df_ref.columns:
            logger.info(f"原始 '{col_name_original}' 列 dtype: {df_ref[col_name_original].dtype}")
            logger.info(f"原始 '{col_name_original}' 列 head:\n{df_ref[col_name_original].head()}")
            df_ref[col_name_numeric] = pd.to_numeric(df_ref[col_name_original], errors='coerce').fillna(0)
            logger.info(f"'{col_name_numeric}' 列 (转换后) head:\n{df_ref[col_name_numeric].head()}")
        else:
            logger.warning(f"'{col_name_original}' 列不存在，将创建全零的 '{col_name_numeric}' 列。")
            df_ref[col_name_numeric] = 0 # Assign scalar 0, pandas will broadcast
        return col_name_numeric
    # --- End helper function ---

    # 确保铁水质量列存在且可用
    iron_mass_col = COL_HM_MASS_KG if COL_HM_MASS_KG in df_result.columns else None
    steel_mass_col = COL_STEEL_MASS_KG if COL_STEEL_MASS_KG in df_result.columns else None

    if iron_mass_col is None or df_result[iron_mass_col].isnull().all():
        logger.warning(f"铁水质量列 '{COL_HM_MASS_KG}' 缺失或全为NaN.")
        if COL_SCRAP_MASS_KG in df_result.columns and df_result.shape[0] > 0:
            df_result[DEFAULT_IRON_MASS_COL] = 80000 
            iron_mass_col = DEFAULT_IRON_MASS_COL
            logger.warning(f"使用默认铁水质量列 '{DEFAULT_IRON_MASS_COL}' 和值 80000kg")
        else:
            df_result[DEFAULT_IRON_MASS_COL] = 0
            iron_mass_col = DEFAULT_IRON_MASS_COL
            logger.error(f"无法估算铁水质量，炉渣计算将不准确。使用 '{DEFAULT_IRON_MASS_COL}' 并填充0。")
    
    # 1. 计算元素的氧化过程 (Si, Mn, P)
    element_oxide_map = {
        '铁水SI': ('钢水SI', 'Si_to_SiO2', 'feature_slag_SiO2_from_Si_kg', 0.05), # (steel_col, ratio_key, feature_name, default_steel_factor)
        '铁水MN': ('钢水MN', 'Mn_to_MnO', 'feature_slag_MnO_from_Mn_kg', 0.4),
        '铁水P': ('钢水P', 'P_to_P2O5', 'feature_slag_P2O5_from_P_kg', 0.1)
    }

    for iron_col, (steel_col_name, ratio_key, feature_name, default_steel_factor) in element_oxide_map.items():
        if iron_col in df_result.columns and iron_mass_col is not None and df_result[iron_mass_col].notna().any():
            element_in_iron_pct_numeric = convert_to_numeric_and_log(df_result, iron_col, '_pct_numeric')
            element_in_iron = df_result[element_in_iron_pct_numeric] * df_result[iron_mass_col] / 100
            
            element_in_steel = pd.Series(0, index=df_result.index)
            if steel_col_name in df_result.columns:
                steel_col_pct_numeric = convert_to_numeric_and_log(df_result, steel_col_name, '_pct_numeric')
                mass_for_steel_calc = df_result[iron_mass_col] # Default to iron mass for safety
                if steel_mass_col and df_result[steel_mass_col].notna().any():
                     mass_for_steel_calc = pd.to_numeric(df_result[steel_mass_col], errors='coerce').fillna(df_result[iron_mass_col])

                element_in_steel = df_result[steel_col_pct_numeric] * mass_for_steel_calc / 100
            else:
                logger.warning(f"钢水成分列 '{steel_col_name}' 未找到，使用默认氧化率估算钢中残余 {iron_col.replace('铁水','')}")
                element_in_steel = element_in_iron * default_steel_factor
            
            oxidized_element = (element_in_iron - element_in_steel).clip(lower=0)
            df_result[feature_name] = oxidized_element * ELEMENT_TO_OXIDE_MASS_RATIO[ratio_key]
            logger.info(f"计算了 {feature_name}: {df_result[feature_name].mean():.2f} kg (平均值)")
        else:
            logger.warning(f"缺少计算 {feature_name} 所需的数据 ('{iron_col}' 或铁水质量)，将填充为0")
            df_result[feature_name] = 0

    # 2. 辅料中氧化物的计算
    # 使用辅助函数转换辅料列
    lime_col_numeric = convert_to_numeric_and_log(df_result, '石灰')
    dolomite_col_numeric = convert_to_numeric_and_log(df_result, '白云石')
    bauxite_col_numeric = convert_to_numeric_and_log(df_result, '铝矾土')

    # 2.1 CaO (来自石灰和白云石)
    df_result['feature_slag_CaO_from_lime_kg'] = df_result[lime_col_numeric] * FLUX_COMPONENT_RATIO.get('CaO_in_Lime', 0.90)
    logger.info(f"石灰贡献的CaO质量: {df_result['feature_slag_CaO_from_lime_kg'].mean():.2f} kg (平均值)")
    
    df_result['feature_slag_CaO_from_dolomite_kg'] = df_result[dolomite_col_numeric] * FLUX_COMPONENT_RATIO.get('CaO_in_Dolomite', 0.58)
    logger.info(f"白云石贡献的CaO质量: {df_result['feature_slag_CaO_from_dolomite_kg'].mean():.2f} kg (平均值)")
    
    cao_cols = [col for col in df_result.columns if col.startswith('feature_slag_CaO_from_')]
    df_result['feature_slag_CaO_total_kg'] = df_result[cao_cols].sum(axis=1)
    logger.info(f"总CaO质量: {df_result['feature_slag_CaO_total_kg'].mean():.2f} kg (平均值)")
    
    # 2.2 MgO (主要来自白云石)
    df_result['feature_slag_MgO_total_kg'] = df_result[dolomite_col_numeric] * FLUX_COMPONENT_RATIO.get('MgO_in_Dolomite', 0.40)
    logger.info(f"MgO质量: {df_result['feature_slag_MgO_total_kg'].mean():.2f} kg (平均值)")
    
    # 2.3 Al2O3 (可能来自铝矾土或其他辅料)
    if '铝矾土' in df.columns: # Check original df, as df_result[bauxite_col_numeric] will always exist due to helper
        df_result['feature_slag_Al2O3_total_kg'] = df_result[bauxite_col_numeric] * FLUX_COMPONENT_RATIO.get('Al2O3_in_Bauxite', 0.70)
    else:
        df_result['feature_slag_Al2O3_total_kg'] = FLUX_COMPONENT_RATIO.get('Al2O3_default_kg', 50) # Default to 50kg if not present
        logger.info("'铝矾土' 列未在原始数据中找到，Al2O3质量将使用默认值或为0。")
    logger.info(f"Al2O3质量: {df_result['feature_slag_Al2O3_total_kg'].mean():.2f} kg (平均值)")
    
    # 清理临时的 _numeric 和 _pct_numeric 列
    temp_numeric_cols = [col for col in df_result.columns if col.endswith('_numeric') or col.endswith('_pct_numeric')]
    if temp_numeric_cols:
        df_result.drop(columns=temp_numeric_cols, inplace=True, errors='ignore')
        logger.info(f"已移除临时转换的 _numeric 和 _pct_numeric 列: {temp_numeric_cols}")

    # 4. 估算总炉渣质量 (在FeO计算之前)
    known_oxide_cols_pre_feo = [
        'feature_slag_SiO2_from_Si_kg',
        'feature_slag_MnO_from_Mn_kg',
        'feature_slag_P2O5_from_P_kg',
        'feature_slag_CaO_total_kg',
        'feature_slag_MgO_total_kg',
        'feature_slag_Al2O3_total_kg'
    ]
    df_result['feature_slag_known_oxides_kg'] = df_result[known_oxide_cols_pre_feo].sum(axis=1)
    logger.info(f"已计算各氧化物质量（FeO除外）。Known oxides sum: {df_result['feature_slag_known_oxides_kg'].mean():.2f} kg (平均值)")

    # 3. 计算 FeO 百分比和质量 (Revised Logic)

    # 3.1. 获取或设定 feature_slag_FeO_percent
    if COL_SLAG_FEO_MEASURED_PERCENT in df_result.columns:
        logger.info(f"发现实测炉渣FeO列: '{COL_SLAG_FEO_MEASURED_PERCENT}'")
        feo_measured_numeric = pd.to_numeric(df_result[COL_SLAG_FEO_MEASURED_PERCENT], errors='coerce')
        
        # 初始化为默认值，然后用有效测量值覆盖
        df_result['feature_slag_FeO_percent'] = DEFAULT_SLAG_FEO_PERCENT
        
        # 有效的测量值：非NaN且 > 0 (假设FeO百分比不能为0或负)
        valid_measured_mask = feo_measured_numeric.notna() & (feo_measured_numeric > 0)
        
        if valid_measured_mask.any():
            df_result.loc[valid_measured_mask, 'feature_slag_FeO_percent'] = feo_measured_numeric[valid_measured_mask]
            num_invalid = (~valid_measured_mask).sum()
            logger.info(f"使用了 '{COL_SLAG_FEO_MEASURED_PERCENT}' 中的 {valid_measured_mask.sum()} 个有效实测值. {num_invalid} 个无效值/零值使用了默认 {DEFAULT_SLAG_FEO_PERCENT}%.")
        else:
            logger.warning(f"'{COL_SLAG_FEO_MEASURED_PERCENT}' 列中无有效实测值. 所有行将使用默认FeO百分比: {DEFAULT_SLAG_FEO_PERCENT}%.")
    else:
        logger.warning(f"未找到实测炉渣FeO列 '{COL_SLAG_FEO_MEASURED_PERCENT}'. 所有行将使用默认FeO百分比: {DEFAULT_SLAG_FEO_PERCENT}%.")
        df_result['feature_slag_FeO_percent'] = DEFAULT_SLAG_FEO_PERCENT

    logger.info(f"feature_slag_FeO_percent 确定. 平均值: {df_result['feature_slag_FeO_percent'].mean():.2f}%")

    # 3.2. 计算 feature_slag_known_oxides_kg_pre_feo (除FeO外的已知氧化物总质量)
    known_oxide_cols_pre_feo = [
        'feature_slag_SiO2_from_Si_kg', 'feature_slag_MnO_from_Mn_kg', 'feature_slag_P2O5_from_P_kg',
        'feature_slag_CaO_total_kg', 'feature_slag_MgO_total_kg', 'feature_slag_Al2O3_total_kg'
    ]
    for col in known_oxide_cols_pre_feo: # Ensure columns exist
        if col not in df_result.columns:
            logger.warning(f"列 '{col}' 在计算 known_oxides_kg_pre_feo 前未找到，填充为0.")
            df_result[col] = 0
    df_result['feature_slag_known_oxides_kg_pre_feo'] = df_result[known_oxide_cols_pre_feo].sum(axis=1)
    logger.info(f"feature_slag_known_oxides_kg_pre_feo (不含FeO的氧化物总和) 计算完成. 平均值: {df_result['feature_slag_known_oxides_kg_pre_feo'].mean():.2f} kg")

    # 3.3. 计算 feature_slag_FeO_kg 
    # Formula: FeO_kg = known_oxides_pre_feo / (1 - FeO_percent/100) * (FeO_percent/100)
    # This is equivalent to: FeO_kg = known_oxides_pre_feo * FeO_fraction / (1 - FeO_fraction)
    feo_fraction = df_result['feature_slag_FeO_percent'] / 100.0
    
    # Denominator: (1 - FeO_fraction)
    denominator_for_feo_calc = 1.0 - feo_fraction
    
    df_result['feature_slag_FeO_kg'] = 0.0  # Initialize

    # Valid calculation where FeO_fraction is not 1 (denominator is not 0)
    # And where FeO_fraction is not > 1 (FeO_percent > 100, which is unlikely but handled by cap earlier for measured)
    valid_calc_mask = (denominator_for_feo_calc > 1e-6) & (feo_fraction >= 0) # Avoid division by zero and negative fractions

    df_result.loc[valid_calc_mask, 'feature_slag_FeO_kg'] = \
        (df_result.loc[valid_calc_mask, 'feature_slag_known_oxides_kg_pre_feo'] * feo_fraction[valid_calc_mask]) / denominator_for_feo_calc[valid_calc_mask]

    # Handle cases where FeO_percent is 100% or very close (denominator_for_feo_calc is zero or negative)
    # This implies known_oxides_kg_pre_feo should be zero. If not, it's an inconsistency.
    # If FeO is 100%, then total slag = FeO_kg. Estimate total slag based on e.g. 10-15% of iron mass.
    problematic_feo_mask = ~valid_calc_mask
    if problematic_feo_mask.any():
        logger.warning(f"{problematic_feo_mask.sum()} 行的 FeO 百分比导致计算 FeO_kg 的分母为零或负数 (e.g., FeO % >= 100%).")
        # Estimate FeO_kg based on a fraction of iron mass, assuming it's the entirety of slag.
        # This is a simplified handling for extreme/inconsistent cases.
        estimated_total_slag_for_extreme_feo = (df_result[iron_mass_col] * 0.12) if iron_mass_col in df_result.columns and df_result[iron_mass_col].notna().any() else 10000
        df_result.loc[problematic_feo_mask, 'feature_slag_FeO_kg'] = estimated_total_slag_for_extreme_feo * feo_fraction[problematic_feo_mask].clip(0,1) # Use capped fraction
        # In such cases, ideally known_oxides_kg_pre_feo should be 0. Log if it's not.
        if (df_result.loc[problematic_feo_mask, 'feature_slag_known_oxides_kg_pre_feo'] > 1e-3).any(): # Check for non-trivial other oxides
            logger.warning(f"  For some rows with FeO % >= 100, 'feature_slag_known_oxides_kg_pre_feo' is not zero, which is inconsistent.")
            # Optionally, set known_oxides_kg_pre_feo to 0 for these rows:
            # df_result.loc[problematic_feo_mask, 'feature_slag_known_oxides_kg_pre_feo'] = 0

    logger.info(f"feature_slag_FeO_kg 计算完成. 平均值: {df_result['feature_slag_FeO_kg'].mean():.2f} kg")

    # 3.4. 计算最终的总炉渣质量 COL_SLAG_TOTAL_MASS_KG
    df_result[COL_SLAG_TOTAL_MASS_KG] = df_result['feature_slag_known_oxides_kg_pre_feo'] + df_result['feature_slag_FeO_kg']
    logger.info(f"最终总炉渣质量 ({COL_SLAG_TOTAL_MASS_KG}) 计算完成. 平均值: {df_result[COL_SLAG_TOTAL_MASS_KG].mean():.2f} kg")

    # 3.5. 确保 ACTUAL_MODEL_INPUT_FEATURES 中的 'feature_slag_known_oxides_kg' 存在且正确
    # This should be the sum of non-Fe oxides (SiO2, MnO, P2O5, CaO, MgO, Al2O3)
    # It is 'feature_slag_known_oxides_kg_pre_feo'
    if 'feature_slag_known_oxides_kg' in ACTUAL_MODEL_INPUT_FEATURES:
        df_result['feature_slag_known_oxides_kg'] = df_result['feature_slag_known_oxides_kg_pre_feo']
        logger.info(f"特征 'feature_slag_known_oxides_kg' (sum of non-Fe oxides) 已确保/创建. 平均值: {df_result['feature_slag_known_oxides_kg'].mean():.2f} kg")
    
    # 3.6. 清理临时的 pre_feo 列
    if 'feature_slag_known_oxides_kg_pre_feo' in df_result.columns:
        df_result.drop(columns=['feature_slag_known_oxides_kg_pre_feo'], inplace=True)

    # 3.7. 处理 feature_slag_Fe2O3_percent 和 feature_slag_iron_oxides_percent
    # These are in ACTUAL_MODEL_INPUT_FEATURES based on the provided list.
    # This is a simplified handling. A proper Fe2O3 calculation is complex.
    if 'feature_slag_Fe2O3_percent' in ACTUAL_MODEL_INPUT_FEATURES:
        if 'feature_slag_Fe2O3_percent' not in df_result.columns:
            df_result['feature_slag_Fe2O3_percent'] = 1.0  # Default to 1% if not calculated
            logger.info("特征 'feature_slag_Fe2O3_percent' 未找到, 已默认设置为 1.0%.")
        else:
            logger.info(f"特征 'feature_slag_Fe2O3_percent' 已存在. 平均值: {df_result['feature_slag_Fe2O3_percent'].mean():.2f}%")

    if 'feature_slag_iron_oxides_percent' in ACTUAL_MODEL_INPUT_FEATURES:
        # Simplistic sum: FeO % + Fe2O3 %
        # Note: This doesn't convert Fe2O3 to FeO equivalent in terms of mass or oxygen.
        fe2o3_for_sum = df_result.get('feature_slag_Fe2O3_percent', 0) # Use 0 if Fe2O3% column is missing
        df_result['feature_slag_iron_oxides_percent'] = df_result['feature_slag_FeO_percent'] + fe2o3_for_sum
        logger.info(f"特征 'feature_slag_iron_oxides_percent' (FeO% + Fe2O3%) 计算完成. 平均值: {df_result['feature_slag_iron_oxides_percent'].mean():.2f}%")
    
    logger.info("炉渣 FeO 和总质量相关计算已更新。")
    return df_result

# --- 主特征工程函数 ---
def engineer_all_features(
    df_input: pd.DataFrame,
    # --- 各模块启用开关 ---
    enable_slag: bool = True,
    enable_process_params: bool = True,
    enable_time_series: bool = True, # 时间序列特征默认启用，但需正确配置下方参数
    enable_interactions: bool = True,
    enable_physicochem_props: bool = True, # 物理化学属性默认启用，但需用户自定义实现
    # --- 新增高级特征启用开关 ---
    enable_advanced_slag: bool = True,  # 基于FactSage的高级炉渣特征
    enable_lance_dynamics: bool = True, # 枪位动态特征
    # --- 时间序列特征配置 ---
    ts_group_col: str = COL_TS_HEAT_ID,
    ts_time_col: str = COL_TS_TIMESTAMP,
    ts_target_cols_list: Optional[List[str]] = None, # 例如 [COL_TS_SENSOR_A_READING]
    ts_lag_periods_list: Optional[List[int]] = None, # 例如 [1, 2, 3]
    ts_rolling_windows_list: Optional[List[int]] = None, # 例如 [3, 5]
    # --- 交互特征配置 ---
    interactions_manual_pairs: Optional[List[Tuple[str, str]]] = None,
    interactions_poly_config: Optional[Dict] = None # 例如 {'degree': 2, 'target_cols': ['col_a', 'col_b']}
) -> pd.DataFrame:
    """
    主函数，按顺序调用各个特征工程模块。
    用户需要根据自己的数据和需求调整这里的调用和参数。
    """
    logger.info("开始运行 engineer_all_features...")
    df_processed = df_input.copy()
    logger.info(f"[engineer_all_features START] df_processed (df_input copy) columns: {df_processed.columns.tolist()}")

    if enable_slag:
        logger.info("--- 正在处理炉渣相关特征 ---")
        logger.info(f"[BEFORE _calculate_individual_oxide_masses_and_total_slag] df_processed columns: {df_processed.columns.tolist()}")
        df_processed = _calculate_individual_oxide_masses_and_total_slag(df_processed)
        logger.info(f"[AFTER _calculate_individual_oxide_masses_and_total_slag] df_processed columns: {df_processed.columns.tolist()}")
        
        if enable_advanced_slag:
            logger.info("--- 正在处理基于FactSage的高级炉渣特征 (此部分已被简化/禁用) ---")
            # df_processed = add_advanced_slag_features(df_processed) # 已注释掉，因为其依赖的 calculate_fe2o3_content 也被注释
        
        slag_features_created = [col for col in df_processed.columns if col.startswith('feature_slag_')]
        if slag_features_created:
            logger.info(f"已创建 {len(slag_features_created)} 个炉渣相关特征")
        else:
            logger.warning("未能创建任何炉渣相关特征，请检查输入数据")

    if enable_process_params:
        logger.info("--- 正在处理工艺参数特征 ---")
        df_processed = add_quantified_process_parameters(df_processed)
        
        if enable_lance_dynamics:
            logger.info("--- 正在处理枪位动态特征 ---")
            df_processed = add_lance_dynamics_features(df_processed)

    if enable_time_series:
        logger.info("--- 正在处理时间序列特征 ---")
        if not ts_target_cols_list:
            logger.warning("未提供时间序列目标列 (ts_target_cols_list)，跳过时间序列特征生成。")
        else:
            df_processed = add_time_series_derived_features(
                df_processed,
                group_by_col=ts_group_col,
                time_col=ts_time_col,
                target_ts_cols=ts_target_cols_list,
                lag_periods=ts_lag_periods_list if ts_lag_periods_list else [],
                rolling_windows=ts_rolling_windows_list if ts_rolling_windows_list else []
            )

    if enable_interactions:
        logger.info("--- 正在处理交互特征 ---")
        # Default interactions (same as before)
        if enable_slag and not interactions_manual_pairs:
            slag_basicity_col = 'feature_slag_basicity_R2'
            slag_feo_col = 'feature_slag_FeO_percent'
            default_interactions = []
            if slag_basicity_col in df_processed.columns:
                if COL_OXYGEN_TOTAL_NM3 in df_processed.columns:
                    default_interactions.append((slag_basicity_col, COL_OXYGEN_TOTAL_NM3))
                elif '累氧实际' in df_processed.columns:
                     default_interactions.append((slag_basicity_col, '累氧实际'))
                if COL_LANCE_HEIGHT_MM in df_processed.columns:
                    default_interactions.append((slag_basicity_col, COL_LANCE_HEIGHT_MM))
                elif '枪位' in df_processed.columns:
                    default_interactions.append((slag_basicity_col, '枪位'))
            if slag_feo_col in df_processed.columns:
                if COL_OXYGEN_TOTAL_NM3 in df_processed.columns:
                    default_interactions.append((slag_feo_col, COL_OXYGEN_TOTAL_NM3))
                elif '累氧实际' in df_processed.columns:
                    default_interactions.append((slag_feo_col, '累氧实际'))
            if default_interactions:
                interactions_manual_pairs = default_interactions
                logger.info(f"添加了 {len(default_interactions)} 个默认的炉渣-工艺参数交互对")

        poly_degree_val = None
        poly_target_cols_val = None
        poly_interaction_only_val = False
        poly_include_bias_val = False
        if interactions_poly_config:
            poly_degree_val = interactions_poly_config.get('degree')
            poly_target_cols_val = interactions_poly_config.get('target_cols')
            poly_interaction_only_val = interactions_poly_config.get('interaction_only', False)
            poly_include_bias_val = interactions_poly_config.get('include_bias', False)
            
        df_processed = add_interaction_features(
            df_processed,
            manual_interaction_pairs=interactions_manual_pairs,
            poly_degree=poly_degree_val,
            poly_target_cols=poly_target_cols_val,
            poly_interaction_only=poly_interaction_only_val,
            poly_include_bias=poly_include_bias_val
        )
        
        if enable_slag and enable_process_params and (enable_advanced_slag or enable_lance_dynamics):
            logger.info("--- 正在处理炉渣-工艺参数交互特征 ---")
            df_processed = add_slag_process_interactions(df_processed)

    if enable_physicochem_props:
        logger.info("--- 正在处理物理化学派生属性 ---")
        df_processed = add_physicochemical_derived_properties(df_processed)

    # --- Final step: Align with ACTUAL_MODEL_INPUT_FEATURES (89 features) ---
    logger.info(f"特征工程初步完成。当前特征数: {len(df_processed.columns)}. 名称: {df_processed.columns.tolist()[:10]}...")
    logger.info(f"开始对齐到期望的 {len(ACTUAL_MODEL_INPUT_FEATURES)} 个输入特征: {ACTUAL_MODEL_INPUT_FEATURES[:5]}...")

    # 确保最终输出的特征与 ACTUAL_MODEL_INPUT_FEATURES 一致，不足的补0
    final_feature_columns = list(ACTUAL_MODEL_INPUT_FEATURES) # 使用副本
    
    # 记录在 df_processed 中实际存在的原始特征列（在进行任何填充之前）
    original_columns_in_df_processed = df_processed.columns.tolist()

    missing_features_filled = []
    present_features_used = []

    # 创建一个空的DataFrame，索引与df_processed一致，列为期望的特征
    # 这样可以保证顺序和完整性
    df_aligned = pd.DataFrame(index=df_processed.index)

    for col in final_feature_columns:
        if col in original_columns_in_df_processed:
            df_aligned[col] = df_processed[col]
            present_features_used.append(col)
        else:
            df_aligned[col] = 0  # 填充为0
            missing_features_filled.append(col)

    if missing_features_filled:
        logger.warning(f"在 engineer_all_features 结束时，以下 {len(missing_features_filled)} 个期望特征在生成步骤中缺失，已自动填充为0:")
        batch_size = 10
        for i in range(0, len(missing_features_filled), batch_size):
            batch = missing_features_filled[i:i + batch_size]
            logger.warning(f"  缺失特征批次 (填充为0): {batch}")
    
    logger.info(f"从原始生成特征中采纳了 {len(present_features_used)} 个特征。名单如下:") # MODIFIED LINE
    # Log present_features_used in batches
    batch_size = 10
    for i in range(0, len(present_features_used), batch_size):
        batch = present_features_used[i:i + batch_size]
        logger.info(f"  采纳的特征批次: {batch}") # MODIFIED LINE

    # 验证最终的df_aligned是否完全符合final_feature_columns的定义（名称和顺序）
    if not df_aligned.columns.equals(pd.Index(final_feature_columns)):
        logger.error("CRITICAL: df_aligned 的列与 final_feature_columns 不完全匹配或顺序不一致！")
        logger.error(f"df_aligned.columns: {df_aligned.columns.tolist()}")
        logger.error(f"final_feature_columns: {final_feature_columns}")
        # 尝试强制重排，但这可能掩盖根本问题
        try:
            df_aligned = df_aligned[final_feature_columns]
            logger.warning("尝试通过重排强制对齐列顺序。")
        except KeyError as e:
            logger.error(f"强制重排失败: {e}. 特征集可能不完整。")
            # 列出在 final_feature_columns 中但不在 df_aligned 中的列
            still_missing_after_align_attempt = [c for c in final_feature_columns if c not in df_aligned.columns]
            if still_missing_after_align_attempt:
                 logger.error(f"重排尝试后仍然缺失的特征: {still_missing_after_align_attempt}")


    # 记录生成过程中被丢弃的额外特征 (在 df_processed 中但不在 final_feature_columns 中)
    # 这些是在对齐到 ACTUAL_MODEL_INPUT_FEATURES 之前生成的，但最终不需要的特征
    generated_but_not_in_target = set(original_columns_in_df_processed) - set(final_feature_columns)
    if generated_but_not_in_target:
        logger.info(f"以下 {len(generated_but_not_in_target)} 个特征在工程化中生成，但不是期望的 {len(ACTUAL_MODEL_INPUT_FEATURES)} 个输入特征之一，因此被舍弃（或未被明确选中）:")
        batch_size = 10
        temp_list_dropped = list(generated_but_not_in_target)
        for i in range(0, len(temp_list_dropped), batch_size):
            batch = temp_list_dropped[i:i + batch_size]
            logger.info(f"  舍弃特征批次: {batch}")
            
    logger.info(f"特征工程处理完毕。最终返回的特征数量: {df_aligned.shape[1]}.")
    
    # 统计NaN和全零列
    if not df_aligned.empty:
        nan_cols = df_aligned.isnull().any()
        num_nan_cols = nan_cols.sum()
        if num_nan_cols > 0:
            logger.warning(f"  最终特征集有 {num_nan_cols} 列包含NaN值: {df_aligned.columns[nan_cols].tolist()}")
        
        all_zero_cols = (df_aligned == 0).all()
        num_all_zero_cols = all_zero_cols.sum()
        if num_all_zero_cols > 0:
            logger.warning(f"  最终特征集有 {num_all_zero_cols} 列所有值都为0: {df_aligned.columns[all_zero_cols].tolist()}")
    else:
        logger.warning("特征工程结果为空 DataFrame。")

    return df_aligned

# --- 使用示例 (将此部分放在您的数据加载和预处理脚本中) ---
if __name__ == '__main__':
    # 设置日志记录 (可选，但推荐)
    # import logging
    # logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # logger = logging.getLogger(__name__) # 如果使用logger, 则上面print替换为logger.info/warning

    # 1. 准备一个示例DataFrame (用户需要替换为自己的实际数据加载逻辑)
    sample_data = {
        COL_TS_HEAT_ID: ['H1', 'H1', 'H1', 'H2', 'H2', 'H2'],
        COL_TS_TIMESTAMP: [10, 20, 30, 10, 20, 30], # 假设是秒
        COL_SLAG_CAO_KG: [100, 110, 105, 120, 125, 115],
        COL_SLAG_SIO2_KG: [50, 55, 52, 60, 62, 58],
        COL_SLAG_MGO_KG: [10, 12, 11, 13, 14, 12],
        COL_SLAG_FEO_PERCENT: [15, 16, 14, 17, 18, 16],
        COL_SLAG_WEIGHT_ESTIMATED_KG: [1000, 1050, 1020, 1100, 1150, 1120],
        COL_PROCESS_OXYGEN_RATE: [300, 310, 305, 320, 325, 315],
        COL_PROCESS_OXYGEN_VOLUME: [15000, 15500, 15200, 16000, 16500, 16200],
        COL_PROCESS_LANCE_HEIGHT: [1200, 1210, 1205, 1220, 1225, 1215],
        COL_PROCESS_STIRRING_POWER: [15, 16, 15.5, 17, 18, 17.5],
        COL_INTERACT_STEEL_TEMP_INITIAL: [1600, 1600, 1600, 1610, 1610, 1610], # 炉次内可能不变
        COL_INTERACT_ALLOY_X_CONTENT: [0.5, 0.5, 0.5, 0.6, 0.6, 0.6],   # 炉次内可能不变
        COL_TS_SENSOR_A_READING: [200, 210, 205, 220, 225, 215], # 某个传感器读数
        'target_temperature': [1650, 1645, 1640, 1660, 1655, 1650] # 假设的目标变量
    }
    df_sample = pd.DataFrame(sample_data)

    print("--- 原始数据 ---")
    print(df_sample.head(6))

    # 2. 配置特征工程参数
    manual_interactions = [
        (COL_INTERACT_STEEL_TEMP_INITIAL, COL_INTERACT_ALLOY_X_CONTENT),
        ('feature_slag_basicity_R2', COL_PROCESS_OXYGEN_VOLUME) # 交互一个已生成的特征和一个原始特征
    ]
    poly_config_example = {
        'degree': 2,
        'target_cols': [COL_PROCESS_OXYGEN_RATE, 'feature_slag_basicity_R2'], # 使用原始和已生成特征
        'interaction_only': True, # 只生成交互项，如 A*B，不生成 A^2, B^2
        'include_bias': False
    }
    
    # 3. 执行特征工程
    df_featured = engineer_all_features(
        df_sample,
        enable_slag=True,
        enable_process_params=True,
        enable_time_series=True,
        ts_target_cols_list=[COL_TS_SENSOR_A_READING], # 指定要处理的时间序列列
        ts_lag_periods_list=[1, 2],                   # 计算1期和2期滞后
        ts_rolling_windows_list=[2],                  # 计算大小为2的滑动窗口统计
        enable_interactions=True,
        interactions_manual_pairs=manual_interactions,
        interactions_poly_config=poly_config_example,
        enable_physicochem_props=True # 虽然是占位符，但调用一下
    )

    print("\n--- 带新特征的数据 (部分列) ---")
    # 为了简洁显示，只选一部分新特征
    new_feature_cols_to_show = [col for col in df_featured.columns if col.startswith('feature_')]
    print(df_featured[['heat_id', 'timestamp_sec'] + new_feature_cols_to_show].head(6))
    
    print(f"\n原始列数: {len(df_sample.columns)}, 新特征后列数: {len(df_featured.columns)}")

    # 在实际应用中，您会将 df_featured 用于后续的模型训练和评估。
    # X = df_featured.drop(columns=['target_temperature', COL_TS_HEAT_ID, COL_TS_TIMESTAMP]) # 移除ID、时间戳和目标
    # y = df_featured['target_temperature']
    # (进行训练/测试集划分等)
