"""
高级钢水温度预测模型 - 目标95%命中率
基于30年炼钢经验的深度特征工程和模型优化
移除SVR，重新加入Ridge和Lasso，专注提升泛化性
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import pickle
import warnings
from typing import Dict, List, Tuple, Union, Any
from datetime import datetime
from scipy import stats
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, StratifiedKFold
from sklearn.preprocessing import StandardScaler, RobustScaler, QuantileTransformer
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet, HuberRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.feature_selection import SelectKBest, f_regression, RFE
from sklearn.pipeline import Pipeline
from sklearn.compose import TransformedTargetRegressor
import xgboost as xgb
import lightgbm as lgb

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"advanced_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdvancedSteelTempPredictor:
    """高级钢水温度预测器 - 目标95%命中率"""

    def __init__(self):
        # 冶金学常数和参数
        self.metallurgical_constants = {
            # 反应热 (kJ/kg)
            'C_to_CO': 10100, 'C_to_CO2': 32800,
            'Si_oxidation': 30800, 'Mn_oxidation': 7200,
            'P_oxidation': 24000, 'Fe_oxidation': 4800,
            'Al_oxidation': 31000, 'Cr_oxidation': 23000,

            # 比热容 (kJ/kg·K)
            'cp_iron': 0.75, 'cp_steel': 0.65, 'cp_slag': 1.2,

            # 密度 (kg/m³)
            'density_iron': 7000, 'density_slag': 3500,

            # 氧化反应系数
            'Si_to_SiO2': 2.14, 'Mn_to_MnO': 1.29,
            'P_to_P2O5': 2.29, 'Fe_to_FeO': 1.29,
            'Al_to_Al2O3': 1.89, 'Cr_to_Cr2O3': 1.46
        }

        # 造渣材料成分 (质量分数)
        self.flux_compositions = {
            'lime': {'CaO': 0.88, 'MgO': 0.02, 'SiO2': 0.03, 'Fe2O3': 0.01},
            'dolomite': {'CaO': 0.32, 'MgO': 0.20, 'SiO2': 0.02, 'Fe2O3': 0.01},
            'limestone': {'CaO': 0.52, 'MgO': 0.02, 'SiO2': 0.04, 'Fe2O3': 0.01},
            'fluorspar': {'CaF2': 0.95, 'SiO2': 0.02},
            'iron_ore': {'Fe2O3': 0.85, 'SiO2': 0.08, 'Al2O3': 0.03}
        }

        # 钢种分组 (基于碳含量和合金元素)
        self.steel_groups = {
            '超低碳钢': ['IF', 'SPCC', 'DC01', 'ST12'],
            '低碳钢': ['Q235', 'Q345', 'Q355', 'Q245', 'Q420', 'Q460', 'A', 'B', 'C', 'D', 'SPHC'],
            '中碳钢': ['20', '35', '45', '50', '55', '60', '20Mn2A', '16Mn', '20MnSi'],
            '高碳钢': ['65', '70', '75', '80', '85', '65Mn', '70Mn', 'C72DA', 'SWRCH'],
            '合金结构钢': ['40Cr', '20CrMo', '35CrMo', '42CrMo', '20CrMnTi'],
            '不锈钢': ['304', '316', '321', '310', '430'],
            '工具钢': ['T8', 'T10', 'T12', '9SiCr', 'Cr12MoV'],
            '弹簧钢': ['65Mn', '60Si2Mn', '50CrVA'],
            '轴承钢': ['GCr15', 'GCr18Mo'],
            '耐热钢': ['12Cr1MoV', '15CrMo', '12CrMoV'],
            '管线钢': ['X60', 'X65', 'X70', 'X80'],
            '船板钢': ['AH32', 'AH36', 'DH32', 'DH36', 'EH32', 'EH36'],
            '压力容器钢': ['Q345R', 'Q245R', '16MnR', '15MnVR'],
            '焊接结构钢': ['Q690', 'Q960', 'ML40Cr'],
            '冷镦钢': ['SWRCH35K', 'SWRCH45K', 'ML08Al'],
            '帘线钢': ['ER50-6WT', 'ER70S-6']
        }

        # 模型和预处理器
        self.models = {}
        self.scalers = {}
        self.feature_selectors = {}
        self.feature_names = []

    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default

    def advanced_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """高级数据清理 - 基于冶金学原理"""
        logger.info("开始高级数据清理")

        df_cleaned = df.copy()

        # 1. 铁水成分异常值检测和修正
        composition_limits = {
            '铁水C': (3.0, 5.0),      # 碳含量正常范围
            '铁水SI': (0.1, 1.2),     # 硅含量正常范围
            '铁水MN': (0.05, 1.0),    # 锰含量正常范围
            '铁水P': (0.05, 0.25),    # 磷含量正常范围
            '铁水S': (0.01, 0.15),    # 硫含量正常范围
            '铁水温度': (1250, 1500)   # 铁水温度正常范围
        }

        for col, (min_val, max_val) in composition_limits.items():
            if col in df_cleaned.columns:
                # 检测异常值
                outliers = (df_cleaned[col] < min_val) | (df_cleaned[col] > max_val)
                outlier_count = outliers.sum()
                if outlier_count > 0:
                    logger.info(f"{col}: 发现{outlier_count}个异常值")
                    # 使用正常范围的中位数替换异常值
                    normal_values = df_cleaned[col][(df_cleaned[col] >= min_val) & (df_cleaned[col] <= max_val)]
                    if len(normal_values) > 0:
                        replacement_value = normal_values.median()
                        df_cleaned.loc[outliers, col] = replacement_value
                        logger.info(f"{col}: 异常值已替换为{replacement_value:.3f}")

        # 2. 工艺参数异常值检测
        process_limits = {
            '吹氧时间s': (300, 1200),     # 吹氧时间正常范围
            '累氧实际': (3000, 6500),     # 供氧量正常范围
            '最大角度': (100, 200),       # 氧枪角度正常范围
            '废钢': (0, 50),             # 废钢量正常范围
            '铁水': (60, 120)            # 铁水量正常范围
        }

        for col, (min_val, max_val) in process_limits.items():
            if col in df_cleaned.columns:
                # 确保列是数值类型
                df_cleaned[col] = pd.to_numeric(df_cleaned[col], errors='coerce')

                outliers = (df_cleaned[col] < min_val) | (df_cleaned[col] > max_val)
                outlier_count = outliers.sum()
                if outlier_count > 0:
                    logger.info(f"{col}: 发现{outlier_count}个工艺参数异常值")
                    # 使用正常范围的中位数替换
                    normal_values = df_cleaned[col][(df_cleaned[col] >= min_val) & (df_cleaned[col] <= max_val)]
                    if len(normal_values) > 0:
                        replacement_value = normal_values.median()
                        df_cleaned.loc[outliers, col] = replacement_value

        # 3. 目标变量异常值处理 (钢水温度)
        if '钢水温度' in df_cleaned.columns:
            # 使用3σ原则检测异常值
            mean_temp = df_cleaned['钢水温度'].mean()
            std_temp = df_cleaned['钢水温度'].std()
            lower_bound = mean_temp - 3 * std_temp
            upper_bound = mean_temp + 3 * std_temp

            # 但要确保在物理合理范围内
            lower_bound = max(lower_bound, 1500)  # 最低1500°C
            upper_bound = min(upper_bound, 1750)  # 最高1750°C

            temp_outliers = (df_cleaned['钢水温度'] < lower_bound) | (df_cleaned['钢水温度'] > upper_bound)
            outlier_count = temp_outliers.sum()
            if outlier_count > 0:
                logger.info(f"钢水温度: 发现{outlier_count}个异常值")
                # 移除异常值而不是替换
                df_cleaned = df_cleaned[~temp_outliers]
                logger.info(f"移除异常值后数据量: {len(df_cleaned)}")

        # 4. 填充缺失值
        numeric_columns = df_cleaned.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if df_cleaned[col].isnull().sum() > 0:
                # 使用分组中位数填充
                if '钢种' in df_cleaned.columns:
                    df_cleaned[col] = df_cleaned.groupby('钢种')[col].transform(
                        lambda x: x.fillna(x.median())
                    )
                # 如果还有缺失值，使用全局中位数
                df_cleaned[col].fillna(df_cleaned[col].median(), inplace=True)

        logger.info(f"数据清理完成，最终数据形状: {df_cleaned.shape}")
        return df_cleaned

    def create_steel_type_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建钢种特征"""
        logger.info("创建钢种特征")

        df_steel = df.copy()

        # 1. 钢种分组
        df_steel['钢种分组'] = '其他'
        df_steel['碳含量等级'] = '未知'
        df_steel['合金等级'] = '普通钢'

        for group_name, steel_types in self.steel_groups.items():
            for steel_type in steel_types:
                mask = df_steel['钢种'].str.contains(steel_type, na=False, case=False)
                df_steel.loc[mask, '钢种分组'] = group_name

        # 2. 基于钢种推断碳含量等级
        carbon_mapping = {
            '超低碳钢': '超低碳', '低碳钢': '低碳', '中碳钢': '中碳',
            '高碳钢': '高碳', '工具钢': '高碳', '轴承钢': '高碳'
        }

        for group, carbon_level in carbon_mapping.items():
            mask = df_steel['钢种分组'] == group
            df_steel.loc[mask, '碳含量等级'] = carbon_level

        # 3. 合金等级判断
        alloy_groups = ['合金结构钢', '不锈钢', '工具钢', '弹簧钢', '轴承钢', '耐热钢']
        for group in alloy_groups:
            mask = df_steel['钢种分组'] == group
            df_steel.loc[mask, '合金等级'] = '合金钢'

        # 4. 钢种频次编码
        steel_counts = df_steel['钢种'].value_counts()
        df_steel['钢种频次'] = df_steel['钢种'].map(steel_counts)

        # 5. 目标温度范围 (基于经验)
        temp_ranges = {
            '超低碳钢': 1580, '低碳钢': 1600, '中碳钢': 1620,
            '高碳钢': 1640, '合金结构钢': 1630, '不锈钢': 1650,
            '工具钢': 1650, '弹簧钢': 1640, '轴承钢': 1640,
            '耐热钢': 1650, '管线钢': 1610, '船板钢': 1610,
            '压力容器钢': 1610, '焊接结构钢': 1620, '冷镦钢': 1590,
            '帘线钢': 1630
        }

        df_steel['目标温度范围'] = df_steel['钢种分组'].map(temp_ranges).fillna(1610)

        logger.info("钢种特征创建完成")
        return df_steel

    def create_advanced_metallurgical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建高级冶金特征 - 基于30年炼钢经验"""
        logger.info("创建高级冶金特征")

        df_metal = df.copy()

        # 1. 热平衡特征 (关键!)
        for idx, row in df_metal.iterrows():
            try:
                # 基础数据
                hot_metal_mass = self.safe_convert(row['铁水'], 90) * 1000  # kg
                scrap_mass = self.safe_convert(row.get('废钢', 0), 20) * 1000  # kg
                hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)

                # 铁水成分
                c_content = self.safe_convert(row['铁水C'], 4.2) / 100
                si_content = self.safe_convert(row['铁水SI'], 0.4) / 100
                mn_content = self.safe_convert(row['铁水MN'], 0.17) / 100
                p_content = self.safe_convert(row['铁水P'], 0.13) / 100

                # 氧化反应热计算 (精确计算)
                # 脱碳反应热 (考虑CO/CO2比例)
                c_oxidized = hot_metal_mass * c_content * 0.88  # 88%脱碳率
                co_ratio = 0.7  # 70% CO
                co2_ratio = 0.3  # 30% CO2
                decarb_heat = (c_oxidized * co_ratio * self.metallurgical_constants['C_to_CO'] +
                              c_oxidized * co2_ratio * self.metallurgical_constants['C_to_CO2'])

                # 硅氧化反应热
                si_oxidized = hot_metal_mass * si_content * 0.95  # 95%氧化率
                si_heat = si_oxidized * self.metallurgical_constants['Si_oxidation']

                # 锰氧化反应热
                mn_oxidized = hot_metal_mass * mn_content * 0.80  # 80%氧化率
                mn_heat = mn_oxidized * self.metallurgical_constants['Mn_oxidation']

                # 磷氧化反应热
                p_oxidized = hot_metal_mass * p_content * 0.85  # 85%氧化率
                p_heat = p_oxidized * self.metallurgical_constants['P_oxidation']

                # 铁氧化反应热 (铁损)
                fe_loss = hot_metal_mass * 0.015  # 1.5%铁损
                fe_heat = fe_loss * self.metallurgical_constants['Fe_oxidation']

                # 总氧化反应热
                total_oxidation_heat = decarb_heat + si_heat + mn_heat + p_heat + fe_heat

                # 废钢加热耗热
                scrap_heating_heat = scrap_mass * (
                    self.metallurgical_constants['cp_steel'] * (1600 - 25) + 1200  # 熔化潜热
                )

                # 净热量
                net_heat = total_oxidation_heat - scrap_heating_heat

                # 理论温升
                total_steel_mass = hot_metal_mass + scrap_mass
                theoretical_temp_rise = net_heat / (total_steel_mass * self.metallurgical_constants['cp_iron'])

                # 理论终点温度
                theoretical_end_temp = hot_metal_temp + theoretical_temp_rise

                # 保存特征
                df_metal.loc[idx, 'total_oxidation_heat'] = total_oxidation_heat
                df_metal.loc[idx, 'decarb_heat'] = decarb_heat
                df_metal.loc[idx, 'si_oxidation_heat'] = si_heat
                df_metal.loc[idx, 'scrap_heating_heat'] = scrap_heating_heat
                df_metal.loc[idx, 'net_heat_balance'] = net_heat
                df_metal.loc[idx, 'theoretical_temp_rise'] = theoretical_temp_rise
                df_metal.loc[idx, 'theoretical_end_temp'] = theoretical_end_temp

                # 2. 氧气利用效率特征
                oxygen_consumed = self.safe_convert(row.get('累氧实际', 4800))
                blow_time = self.safe_convert(row.get('吹氧时间s', 800))

                # 氧气强度
                df_metal.loc[idx, 'oxygen_intensity'] = oxygen_consumed / (blow_time / 60)

                # 单位铁水供氧量
                df_metal.loc[idx, 'oxygen_per_hotmetal'] = oxygen_consumed / (hot_metal_mass / 1000)

                # 氧气利用率 (基于碳氧化)
                theoretical_oxygen = c_oxidized * 1.33 + si_oxidized * 1.14  # 理论耗氧量
                df_metal.loc[idx, 'oxygen_efficiency'] = theoretical_oxygen / oxygen_consumed if oxygen_consumed > 0 else 0

                # 3. 炉渣特征 (精确计算)
                # 造渣材料
                lime_mass = self.safe_convert(row.get('石灰', 0))
                dolomite_mass = self.safe_convert(row.get('白云石', 0))

                # CaO来源
                cao_from_lime = lime_mass * self.flux_compositions['lime']['CaO']
                cao_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['CaO']
                total_cao = cao_from_lime + cao_from_dolomite

                # SiO2来源
                sio2_from_si = si_oxidized * self.metallurgical_constants['Si_to_SiO2']
                sio2_from_flux = (lime_mass * self.flux_compositions['lime']['SiO2'] +
                                 dolomite_mass * self.flux_compositions['dolomite']['SiO2'])
                total_sio2 = sio2_from_si + sio2_from_flux

                # 炉渣碱度
                df_metal.loc[idx, 'slag_basicity'] = total_cao / total_sio2 if total_sio2 > 0 else 2.8

                # 炉渣量
                total_slag = total_cao + total_sio2 + fe_loss * 1.29  # FeO
                df_metal.loc[idx, 'slag_amount'] = total_slag
                df_metal.loc[idx, 'slag_rate'] = total_slag / hot_metal_mass * 100

                # 4. 工艺强度特征
                # 吹炼强度
                df_metal.loc[idx, 'blow_intensity'] = oxygen_consumed / blow_time * 60  # Nm³/min

                # 枪位相关 (基于最大角度)
                max_angle = self.safe_convert(row.get('最大角度', 150))
                df_metal.loc[idx, 'lance_height_index'] = max_angle / 180  # 归一化

                # 5. 成分平衡特征
                # 碳氧比
                df_metal.loc[idx, 'carbon_oxygen_ratio'] = c_content * hot_metal_mass / oxygen_consumed * 1000

                # 硅锰比
                df_metal.loc[idx, 'si_mn_ratio'] = si_content / (mn_content + 1e-6)

                # 磷硫比
                s_content = self.safe_convert(row.get('铁水S', 0.03)) / 100
                df_metal.loc[idx, 'p_s_ratio'] = p_content / (s_content + 1e-6)

            except Exception as e:
                logger.warning(f"计算第{idx}行冶金特征时出错: {e}")
                continue

        # 填充可能的NaN值
        metallurgical_features = [
            'total_oxidation_heat', 'decarb_heat', 'si_oxidation_heat', 'scrap_heating_heat',
            'net_heat_balance', 'theoretical_temp_rise', 'theoretical_end_temp',
            'oxygen_intensity', 'oxygen_per_hotmetal', 'oxygen_efficiency',
            'slag_basicity', 'slag_amount', 'slag_rate', 'blow_intensity',
            'lance_height_index', 'carbon_oxygen_ratio', 'si_mn_ratio', 'p_s_ratio'
        ]

        for feature in metallurgical_features:
            if feature in df_metal.columns:
                df_metal[feature].fillna(df_metal[feature].median(), inplace=True)

        logger.info("高级冶金特征创建完成")
        return df_metal

    def create_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建交互特征"""
        logger.info("创建交互特征")

        df_interaction = df.copy()

        # 关键交互特征 (基于冶金学原理)
        interaction_pairs = [
            # 热平衡相关
            ('theoretical_end_temp', '目标温度范围'),
            ('net_heat_balance', 'slag_rate'),
            ('oxygen_efficiency', 'blow_intensity'),

            # 成分相关
            ('铁水C', 'oxygen_per_hotmetal'),
            ('铁水SI', 'slag_basicity'),
            ('carbon_oxygen_ratio', 'blow_intensity'),

            # 工艺相关
            ('废钢比', '铁水温度'),
            ('lance_height_index', 'oxygen_intensity'),
            ('slag_basicity', 'theoretical_temp_rise'),

            # 钢种相关
            ('钢种频次', 'theoretical_end_temp'),
            ('目标温度范围', 'net_heat_balance')
        ]

        for feature1, feature2 in interaction_pairs:
            if feature1 in df_interaction.columns and feature2 in df_interaction.columns:
                # 乘积交互
                df_interaction[f'{feature1}_x_{feature2}'] = (
                    df_interaction[feature1] * df_interaction[feature2]
                )

                # 比值交互
                df_interaction[f'{feature1}_div_{feature2}'] = (
                    df_interaction[feature1] / (df_interaction[feature2] + 1e-6)
                )

        logger.info(f"交互特征创建完成，新增{len(interaction_pairs) * 2}个特征")
        return df_interaction

    def advanced_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """高级特征选择"""
        logger.info("开始高级特征选择")

        # 1. 移除低方差特征
        from sklearn.feature_selection import VarianceThreshold
        variance_selector = VarianceThreshold(threshold=0.01)
        X_variance = pd.DataFrame(
            variance_selector.fit_transform(X),
            columns=X.columns[variance_selector.get_support()],
            index=X.index
        )

        logger.info(f"方差筛选后特征数: {X_variance.shape[1]}")

        # 2. 相关性筛选
        correlation_matrix = X_variance.corr().abs()
        upper_triangle = correlation_matrix.where(
            np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool)
        )

        # 找出高相关性特征对
        high_corr_features = [column for column in upper_triangle.columns
                             if any(upper_triangle[column] > 0.95)]

        X_corr = X_variance.drop(columns=high_corr_features)
        logger.info(f"相关性筛选后特征数: {X_corr.shape[1]}")

        # 3. 基于重要性的特征选择
        rf_selector = RandomForestRegressor(n_estimators=100, random_state=42)
        rf_selector.fit(X_corr, y)

        # 选择重要性前80%的特征
        feature_importance = pd.Series(rf_selector.feature_importances_, index=X_corr.columns)
        importance_threshold = feature_importance.quantile(0.2)  # 保留前80%
        selected_features = feature_importance[feature_importance >= importance_threshold].index

        X_selected = X_corr[selected_features]
        logger.info(f"重要性筛选后特征数: {X_selected.shape[1]}")

        return X_selected

    def train_advanced_models(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, Any]:
        """训练高级模型 - 重新加入Ridge和Lasso，移除SVR"""
        logger.info("开始训练高级模型")

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # 定义模型配置 - 重新加入Ridge和Lasso，移除SVR
        models_config = {
            # 线性模型 (重新加入)
            'Ridge': Ridge(alpha=1.0, random_state=42),
            'Lasso': Lasso(alpha=0.1, random_state=42, max_iter=2000),
            'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42, max_iter=2000),
            'HuberRegressor': HuberRegressor(epsilon=1.35, max_iter=200),

            # 树模型
            'RandomForest': RandomForestRegressor(
                n_estimators=300, max_depth=15, min_samples_split=5,
                min_samples_leaf=2, random_state=42, n_jobs=-1
            ),
            'ExtraTrees': ExtraTreesRegressor(
                n_estimators=300, max_depth=15, min_samples_split=5,
                min_samples_leaf=2, random_state=42, n_jobs=-1
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=300, max_depth=8, learning_rate=0.1,
                subsample=0.8, random_state=42
            ),

            # 梯度提升模型
            'XGBoost': xgb.XGBRegressor(
                n_estimators=500, max_depth=8, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8, random_state=42,
                objective='reg:squarederror', eval_metric='mae'
            ),
            'LightGBM': lgb.LGBMRegressor(
                n_estimators=500, max_depth=8, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8, random_state=42,
                objective='regression', metric='mae', verbose=-1
            ),

            # 神经网络
            'NeuralNetwork': MLPRegressor(
                hidden_layer_sizes=(200, 100, 50), max_iter=500,
                learning_rate_init=0.001, random_state=42,
                early_stopping=True, validation_fraction=0.1
            )
        }

        results = {}

        for name, model in models_config.items():
            try:
                logger.info(f"训练{name}模型...")

                # 使用不同的预处理策略
                if name in ['Ridge', 'Lasso', 'ElasticNet', 'NeuralNetwork']:
                    # 线性模型和神经网络使用标准化
                    scaler = StandardScaler()
                    X_train_scaled = scaler.fit_transform(X_train)
                    X_test_scaled = scaler.transform(X_test)

                    model.fit(X_train_scaled, y_train)
                    y_pred_train = model.predict(X_train_scaled)
                    y_pred_test = model.predict(X_test_scaled)

                    self.scalers[name] = scaler
                else:
                    # 树模型不需要标准化
                    model.fit(X_train, y_train)
                    y_pred_train = model.predict(X_train)
                    y_pred_test = model.predict(X_test)

                    self.scalers[name] = None

                # 评估指标
                train_mae = mean_absolute_error(y_train, y_pred_train)
                test_mae = mean_absolute_error(y_test, y_pred_test)
                train_rmse = np.sqrt(mean_squared_error(y_train, y_pred_train))
                test_rmse = np.sqrt(mean_squared_error(y_test, y_pred_test))
                train_r2 = r2_score(y_train, y_pred_train)
                test_r2 = r2_score(y_test, y_pred_test)

                # 计算±20°C精度
                train_accuracy_20 = np.mean(np.abs(y_train - y_pred_train) <= 20) * 100
                test_accuracy_20 = np.mean(np.abs(y_test - y_pred_test) <= 20) * 100

                # 计算1590-1670°C范围内的精度
                temp_range_mask = (y_test >= 1590) & (y_test <= 1670)
                if temp_range_mask.sum() > 0:
                    range_accuracy_20 = np.mean(
                        np.abs(y_test[temp_range_mask] - y_pred_test[temp_range_mask]) <= 20
                    ) * 100
                else:
                    range_accuracy_20 = 0

                # 交叉验证
                cv_scores = cross_val_score(model, X_train, y_train, cv=5,
                                          scoring='neg_mean_absolute_error')
                cv_mae = -cv_scores.mean()
                cv_std = cv_scores.std()

                results[name] = {
                    'model': model,
                    'train_mae': train_mae,
                    'test_mae': test_mae,
                    'train_rmse': train_rmse,
                    'test_rmse': test_rmse,
                    'train_r2': train_r2,
                    'test_r2': test_r2,
                    'train_accuracy_20': train_accuracy_20,
                    'test_accuracy_20': test_accuracy_20,
                    'range_accuracy_20': range_accuracy_20,
                    'cv_mae': cv_mae,
                    'cv_std': cv_std,
                    'predictions_test': y_pred_test,
                    'y_test': y_test
                }

                logger.info(f"{name} - 测试MAE: {test_mae:.1f}°C, "
                           f"±20°C精度: {test_accuracy_20:.1f}%, "
                           f"范围精度: {range_accuracy_20:.1f}%")

            except Exception as e:
                logger.error(f"训练{name}模型失败: {e}")
                continue

        self.models = results
        return results

    def create_advanced_ensemble(self, X: pd.DataFrame, y: pd.Series) -> Any:
        """创建高级集成模型"""
        logger.info("创建高级集成模型")

        if len(self.models) < 3:
            logger.warning("模型数量不足，无法创建集成模型")
            return None

        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # 选择表现最好的模型作为基础模型
        model_performance = [(name, info['test_mae']) for name, info in self.models.items()]
        model_performance.sort(key=lambda x: x[1])  # 按MAE排序

        # 选择前5个最佳模型
        best_models = [name for name, _ in model_performance[:5]]
        logger.info(f"选择的基础模型: {best_models}")

        # 构建元特征
        meta_features_train = []
        meta_features_test = []

        for name in best_models:
            model_info = self.models[name]
            model = model_info['model']
            scaler = self.scalers[name]

            if scaler is not None:
                X_train_scaled = scaler.transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                pred_train = model.predict(X_train_scaled)
                pred_test = model.predict(X_test_scaled)
            else:
                pred_train = model.predict(X_train)
                pred_test = model.predict(X_test)

            meta_features_train.append(pred_train)
            meta_features_test.append(pred_test)

        # 构建元学习器的输入
        meta_X_train = np.column_stack(meta_features_train)
        meta_X_test = np.column_stack(meta_features_test)

        # 多层集成策略
        ensemble_models = {
            'Ridge_Meta': Ridge(alpha=1.0),
            'XGBoost_Meta': xgb.XGBRegressor(
                n_estimators=100, max_depth=4, learning_rate=0.1, random_state=42
            ),
            'LightGBM_Meta': lgb.LGBMRegressor(
                n_estimators=100, max_depth=4, learning_rate=0.1, random_state=42, verbose=-1
            )
        }

        best_ensemble = None
        best_mae = float('inf')

        for name, meta_model in ensemble_models.items():
            try:
                meta_model.fit(meta_X_train, y_train)
                ensemble_pred = meta_model.predict(meta_X_test)

                ensemble_mae = mean_absolute_error(y_test, ensemble_pred)
                ensemble_r2 = r2_score(y_test, ensemble_pred)
                ensemble_accuracy_20 = np.mean(np.abs(y_test - ensemble_pred) <= 20) * 100

                # 计算1590-1670°C范围内的精度
                temp_range_mask = (y_test >= 1590) & (y_test <= 1670)
                if temp_range_mask.sum() > 0:
                    range_accuracy_20 = np.mean(
                        np.abs(y_test[temp_range_mask] - ensemble_pred[temp_range_mask]) <= 20
                    ) * 100
                else:
                    range_accuracy_20 = 0

                logger.info(f"{name} - MAE: {ensemble_mae:.1f}°C, "
                           f"±20°C精度: {ensemble_accuracy_20:.1f}%, "
                           f"范围精度: {range_accuracy_20:.1f}%")

                if ensemble_mae < best_mae:
                    best_mae = ensemble_mae
                    best_ensemble = {
                        'meta_learner': meta_model,
                        'base_models': best_models,
                        'mae': ensemble_mae,
                        'r2': ensemble_r2,
                        'accuracy_20': ensemble_accuracy_20,
                        'range_accuracy_20': range_accuracy_20,
                        'predictions': ensemble_pred,
                        'y_test': y_test
                    }

            except Exception as e:
                logger.error(f"训练{name}集成模型失败: {e}")
                continue

        if best_ensemble:
            logger.info(f"最佳集成模型 - MAE: {best_ensemble['mae']:.1f}°C, "
                       f"±20°C精度: {best_ensemble['accuracy_20']:.1f}%, "
                       f"范围精度: {best_ensemble['range_accuracy_20']:.1f}%")

        return best_ensemble

def create_advanced_visualizations(models_results: Dict[str, Any], ensemble_result: Any,
                                 output_dir: str = "advanced_results"):
    """创建高级可视化"""
    logger.info("创建高级可视化")

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 1. 模型性能对比 (包含范围精度)
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    model_names = list(models_results.keys())
    test_mae = [models_results[name]['test_mae'] for name in model_names]
    test_accuracy_20 = [models_results[name]['test_accuracy_20'] for name in model_names]
    range_accuracy_20 = [models_results[name]['range_accuracy_20'] for name in model_names]
    cv_mae = [models_results[name]['cv_mae'] for name in model_names]

    # MAE对比
    bars1 = ax1.bar(model_names, test_mae, color='skyblue', edgecolor='black')
    ax1.set_title('模型测试MAE对比')
    ax1.set_ylabel('平均绝对误差 (°C)')
    ax1.tick_params(axis='x', rotation=45)
    for i, v in enumerate(test_mae):
        ax1.text(i, v + 0.5, f'{v:.1f}', ha='center', va='bottom')

    # ±20°C精度对比
    bars2 = ax2.bar(model_names, test_accuracy_20, color='lightgreen', edgecolor='black')
    ax2.set_title('模型±20°C精度对比')
    ax2.set_ylabel('精度 (%)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.axhline(y=95, color='r', linestyle='--', label='目标95%')
    for i, v in enumerate(test_accuracy_20):
        ax2.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    ax2.legend()

    # 1590-1670°C范围精度对比
    bars3 = ax3.bar(model_names, range_accuracy_20, color='lightcoral', edgecolor='black')
    ax3.set_title('1590-1670°C范围±20°C精度对比')
    ax3.set_ylabel('精度 (%)')
    ax3.tick_params(axis='x', rotation=45)
    ax3.axhline(y=95, color='r', linestyle='--', label='目标95%')
    for i, v in enumerate(range_accuracy_20):
        ax3.text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    ax3.legend()

    # 交叉验证MAE对比
    bars4 = ax4.bar(model_names, cv_mae, color='gold', edgecolor='black')
    ax4.set_title('交叉验证MAE对比')
    ax4.set_ylabel('CV MAE (°C)')
    ax4.tick_params(axis='x', rotation=45)
    for i, v in enumerate(cv_mae):
        ax4.text(i, v + 0.5, f'{v:.1f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/advanced_model_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 最佳模型预测散点图
    best_model_name = min(models_results.keys(), key=lambda x: models_results[x]['test_mae'])
    best_result = models_results[best_model_name]

    plt.figure(figsize=(12, 10))

    # 子图1: 预测vs实际
    plt.subplot(2, 2, 1)
    y_test = best_result['y_test']
    y_pred = best_result['predictions_test']

    plt.scatter(y_test, y_pred, alpha=0.6, s=30)
    min_temp = min(y_test.min(), y_pred.min())
    max_temp = max(y_test.max(), y_pred.max())
    plt.plot([min_temp, max_temp], [min_temp, max_temp], 'r--', lw=2, label='完美预测')
    plt.fill_between([min_temp, max_temp], [min_temp-20, max_temp-20],
                     [min_temp+20, max_temp+20], alpha=0.2, color='gray', label='±20°C误差带')
    plt.xlabel('实际温度 (°C)')
    plt.ylabel('预测温度 (°C)')
    plt.title(f'{best_model_name} 预测vs实际')
    plt.legend()
    plt.grid(alpha=0.3)

    # 子图2: 残差分布
    plt.subplot(2, 2, 2)
    residuals = y_test - y_pred
    plt.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(0, color='r', linestyle='--', label='零残差')
    plt.axvline(-20, color='orange', linestyle='--', label='±20°C')
    plt.axvline(20, color='orange', linestyle='--')
    plt.xlabel('残差 (°C)')
    plt.ylabel('频次')
    plt.title('残差分布')
    plt.legend()
    plt.grid(alpha=0.3)

    # 子图3: 1590-1670°C范围分析
    plt.subplot(2, 2, 3)
    range_mask = (y_test >= 1590) & (y_test <= 1670)
    if range_mask.sum() > 0:
        y_test_range = y_test[range_mask]
        y_pred_range = y_pred[range_mask]
        plt.scatter(y_test_range, y_pred_range, alpha=0.6, s=30, color='red')
        plt.plot([1590, 1670], [1590, 1670], 'r--', lw=2, label='完美预测')
        plt.fill_between([1590, 1670], [1570, 1650], [1610, 1690],
                         alpha=0.2, color='gray', label='±20°C误差带')
        plt.xlabel('实际温度 (°C)')
        plt.ylabel('预测温度 (°C)')
        plt.title('1590-1670°C范围预测')
        plt.legend()
        plt.grid(alpha=0.3)

    # 子图4: 集成模型对比
    plt.subplot(2, 2, 4)
    if ensemble_result:
        ensemble_pred = ensemble_result['predictions']
        ensemble_y_test = ensemble_result['y_test']
        plt.scatter(ensemble_y_test, ensemble_pred, alpha=0.6, s=30, color='green')
        min_temp = min(ensemble_y_test.min(), ensemble_pred.min())
        max_temp = max(ensemble_y_test.max(), ensemble_pred.max())
        plt.plot([min_temp, max_temp], [min_temp, max_temp], 'r--', lw=2, label='完美预测')
        plt.xlabel('实际温度 (°C)')
        plt.ylabel('预测温度 (°C)')
        plt.title(f'集成模型预测 (MAE: {ensemble_result["mae"]:.1f}°C)')
        plt.legend()
        plt.grid(alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/advanced_prediction_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

    logger.info(f"高级可视化已保存到 {output_dir}")

def save_advanced_results(predictor: AdvancedSteelTempPredictor, models_results: Dict[str, Any],
                         ensemble_result: Any, processed_data: pd.DataFrame,
                         output_dir: str = "advanced_results"):
    """保存高级训练结果"""
    logger.info("保存高级训练结果")

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存各个模型
    for name, result in models_results.items():
        model_path = os.path.join(output_dir, f"{name.lower()}_model.pkl")
        with open(model_path, 'wb') as f:
            pickle.dump(result['model'], f)

        # 保存对应的scaler
        if name in predictor.scalers and predictor.scalers[name] is not None:
            scaler_path = os.path.join(output_dir, f"{name.lower()}_scaler.pkl")
            with open(scaler_path, 'wb') as f:
                pickle.dump(predictor.scalers[name], f)

    # 保存集成模型
    if ensemble_result:
        ensemble_path = os.path.join(output_dir, "advanced_ensemble_model.pkl")
        with open(ensemble_path, 'wb') as f:
            pickle.dump(ensemble_result, f)

    # 保存特征名称
    features_path = os.path.join(output_dir, "advanced_feature_names.pkl")
    with open(features_path, 'wb') as f:
        pickle.dump(predictor.feature_names, f)

    # 保存处理后的数据
    data_path = os.path.join(output_dir, "advanced_processed_data.xlsx")
    processed_data.to_excel(data_path, index=False)

    # 生成详细报告
    report_path = os.path.join(output_dir, "advanced_performance_report.txt")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("高级钢水温度预测模型性能报告\n")
        f.write("=" * 60 + "\n\n")
        f.write("目标: 1590-1670°C范围内±20°C命中率达到95%\n\n")

        f.write("模型性能对比:\n")
        f.write("-" * 50 + "\n")
        for name, result in models_results.items():
            f.write(f"{name}:\n")
            f.write(f"  测试MAE: {result['test_mae']:.2f}°C\n")
            f.write(f"  测试RMSE: {result['test_rmse']:.2f}°C\n")
            f.write(f"  测试R²: {result['test_r2']:.4f}\n")
            f.write(f"  ±20°C精度: {result['test_accuracy_20']:.1f}%\n")
            f.write(f"  1590-1670°C范围精度: {result['range_accuracy_20']:.1f}%\n")
            f.write(f"  交叉验证MAE: {result['cv_mae']:.2f}±{result['cv_std']:.2f}°C\n\n")

        if ensemble_result:
            f.write("集成模型性能:\n")
            f.write("-" * 30 + "\n")
            f.write(f"  测试MAE: {ensemble_result['mae']:.2f}°C\n")
            f.write(f"  测试R²: {ensemble_result['r2']:.4f}\n")
            f.write(f"  ±20°C精度: {ensemble_result['accuracy_20']:.1f}%\n")
            f.write(f"  1590-1670°C范围精度: {ensemble_result['range_accuracy_20']:.1f}%\n\n")

        # 找出最佳模型
        best_model_name = min(models_results.keys(), key=lambda x: models_results[x]['test_mae'])
        best_range_model = max(models_results.keys(), key=lambda x: models_results[x]['range_accuracy_20'])

        f.write(f"最佳整体模型: {best_model_name}\n")
        f.write(f"最佳MAE: {models_results[best_model_name]['test_mae']:.2f}°C\n")
        f.write(f"最佳范围精度模型: {best_range_model}\n")
        f.write(f"最佳范围精度: {models_results[best_range_model]['range_accuracy_20']:.1f}%\n\n")

        # 目标达成情况
        best_range_accuracy = max([result['range_accuracy_20'] for result in models_results.values()])
        if ensemble_result and ensemble_result['range_accuracy_20'] > best_range_accuracy:
            best_range_accuracy = ensemble_result['range_accuracy_20']

        f.write("目标达成情况:\n")
        f.write("-" * 20 + "\n")
        f.write(f"目标: 95%范围精度\n")
        f.write(f"实际: {best_range_accuracy:.1f}%范围精度\n")
        if best_range_accuracy >= 95:
            f.write("✅ 目标已达成!\n")
        else:
            f.write(f"❌ 距离目标还差{95 - best_range_accuracy:.1f}%\n")

    logger.info(f"高级训练结果已保存到 {output_dir}")

def main():
    """主函数 - 高级钢水温度预测模型训练"""
    logger.info("=== 开始高级钢水温度预测模型训练 ===")
    logger.info("目标: 1590-1670°C范围内±20°C命中率达到95%")

    # 读取训练数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        logger.info(f"成功读取训练数据，共{len(df)}条记录，{df.shape[1]}个特征")
    except Exception as e:
        logger.error(f"读取训练数据失败：{e}")
        return

    # 创建高级预测器
    predictor = AdvancedSteelTempPredictor()

    # 高级数据清理
    logger.info("\n=== 阶段1: 高级数据清理 ===")
    df_cleaned = predictor.advanced_data_cleaning(df)

    # 创建钢种特征
    logger.info("\n=== 阶段2: 钢种特征工程 ===")
    df_steel = predictor.create_steel_type_features(df_cleaned)

    # 创建高级冶金特征
    logger.info("\n=== 阶段3: 高级冶金特征工程 ===")
    df_metallurgical = predictor.create_advanced_metallurgical_features(df_steel)

    # 创建交互特征
    logger.info("\n=== 阶段4: 交互特征工程 ===")
    df_interaction = predictor.create_interaction_features(df_metallurgical)

    logger.info(f"特征工程完成，最终特征数: {df_interaction.shape[1]}")

    # 准备建模数据
    logger.info("\n=== 阶段5: 特征选择和数据准备 ===")
    exclude_cols = ['炉号', '钢种', '出钢重量估算', 'Unnamed: 4', '钢水温度']
    feature_cols = [col for col in df_interaction.columns if col not in exclude_cols]

    X = df_interaction[feature_cols].copy()
    y = df_interaction['钢水温度'].copy()

    # 处理分类特征
    categorical_cols = X.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        X[col] = pd.Categorical(X[col]).codes

    # 高级特征选择
    X_selected = predictor.advanced_feature_selection(X, y)
    predictor.feature_names = list(X_selected.columns)

    # 训练高级模型
    logger.info("\n=== 阶段6: 高级模型训练 ===")
    models_results = predictor.train_advanced_models(X_selected, y)

    # 创建高级集成模型
    logger.info("\n=== 阶段7: 高级集成学习 ===")
    ensemble_result = predictor.create_advanced_ensemble(X_selected, y)

    # 创建可视化
    logger.info("\n=== 阶段8: 结果可视化 ===")
    create_advanced_visualizations(models_results, ensemble_result)

    # 保存结果
    logger.info("\n=== 阶段9: 保存结果 ===")
    save_advanced_results(predictor, models_results, ensemble_result, df_interaction)

    # 生成最终报告
    logger.info("\n=== 最终结果总结 ===")
    logger.info(f"训练数据: {len(df_interaction)}条记录")
    logger.info(f"最终特征数: {len(predictor.feature_names)}")
    logger.info(f"训练模型数: {len(models_results)}")

    # 显示最佳性能
    best_model_name = min(models_results.keys(), key=lambda x: models_results[x]['test_mae'])
    best_result = models_results[best_model_name]
    best_range_model = max(models_results.keys(), key=lambda x: models_results[x]['range_accuracy_20'])
    best_range_result = models_results[best_range_model]

    logger.info(f"最佳整体模型: {best_model_name}")
    logger.info(f"  MAE: {best_result['test_mae']:.1f}°C")
    logger.info(f"  ±20°C精度: {best_result['test_accuracy_20']:.1f}%")
    logger.info(f"  范围精度: {best_result['range_accuracy_20']:.1f}%")

    logger.info(f"最佳范围精度模型: {best_range_model}")
    logger.info(f"  范围精度: {best_range_result['range_accuracy_20']:.1f}%")

    if ensemble_result:
        logger.info(f"集成模型性能:")
        logger.info(f"  MAE: {ensemble_result['mae']:.1f}°C")
        logger.info(f"  ±20°C精度: {ensemble_result['accuracy_20']:.1f}%")
        logger.info(f"  范围精度: {ensemble_result['range_accuracy_20']:.1f}%")

    # 目标达成检查
    best_range_accuracy = max([result['range_accuracy_20'] for result in models_results.values()])
    if ensemble_result and ensemble_result['range_accuracy_20'] > best_range_accuracy:
        best_range_accuracy = ensemble_result['range_accuracy_20']

    logger.info(f"\n目标达成情况:")
    logger.info(f"目标: 95%范围精度")
    logger.info(f"实际: {best_range_accuracy:.1f}%范围精度")
    if best_range_accuracy >= 95:
        logger.info("🎉 目标已达成!")
    else:
        logger.info(f"⚠️ 距离目标还差{95 - best_range_accuracy:.1f}%")
        logger.info("建议: 1) 收集更多数据 2) 优化特征工程 3) 调整模型参数")

    logger.info("=== 高级模型训练完成 ===")

if __name__ == "__main__":
    main()
