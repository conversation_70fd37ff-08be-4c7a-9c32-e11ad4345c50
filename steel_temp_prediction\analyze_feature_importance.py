import os
import joblib
import pandas as pd
import logging
import sys

# 配置日志 (与 train_model.py 类似)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='feature_analysis.log' # 保存到单独的日志文件
)
logger = logging.getLogger(__name__)
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logger.addHandler(console)

# 确保可以导入 SequentialThinkingModel (如果它不在标准路径或已安装的包中)
# 假设 analyze_feature_importance.py 与 model_development.py 在同一目录层级 (steel_temp_prediction)
# 或者 steel_temp_prediction 包在 PYTHONPATH 中
try:
    from model_development import SequentialThinkingModel # 假设模型类定义在 model_development.py
except ImportError:
    logger.warning("Failed to import SequentialThinkingModel directly. Ensure model_development.py is accessible.")
    # 如果脚本不在 steel_temp_prediction 内部，或者包结构复杂，可能需要调整路径
    # 例如，如果脚本在项目根目录，而模型类在 steel_temp_prediction/model_development.py:
    # from steel_temp_prediction.model_development import SequentialThinkingModel
    # 这个try-except只是一个基本的尝试，实际可能需要根据你的项目结构调整
    # 对于当前 Cursor 环境，假设它可以找到
    pass


def display_feature_importances(model_path: str):
    logger.info(f"Loading model from: {model_path}")
    try:
        model = joblib.load(model_path)
        logger.info(f"Model loaded successfully: {type(model)}")
    except FileNotFoundError:
        logger.error(f"Model file not found at {model_path}. Please ensure train_model.py has run successfully and saved the model.")
        return
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return

    # 1. Meta-learner feature importances
    if hasattr(model, 'meta_model') and model.meta_model is not None:
        logger.info("\\n--- Meta-Learner Feature Importances ---")
        if hasattr(model, 'feature_importances_') and model.feature_importances_ is not None and \
           hasattr(model, 'meta_feature_names_') and model.meta_feature_names_:
            
            if len(model.feature_importances_) == len(model.meta_feature_names_):
                importances_df = pd.DataFrame({
                    'feature': model.meta_feature_names_,
                    'importance': model.feature_importances_
                })
                importances_df = importances_df.sort_values(by='importance', ascending=False).reset_index(drop=True)
                
                logger.info(f"Meta-learner ({type(model.meta_model).__name__}) feature importances (Top 30 or all):")
                pd.set_option('display.max_rows', max(30, len(importances_df)))
                logger.info(f"\\n{importances_df.head(min(30, len(importances_df)))}")
                if len(importances_df) > 30:
                    logger.info("... (and more)")
                
                # 保存元学习器特征重要性到CSV
                meta_importance_path = os.path.join(os.path.dirname(model_path), "meta_learner_feature_importances.csv")
                try:
                    importances_df.to_csv(meta_importance_path, index=False, encoding='utf-8-sig')
                    logger.info(f"Meta-learner feature importances saved to: {meta_importance_path}")
                except Exception as e_csv:
                    logger.error(f"Error saving meta-learner feature importances to CSV: {e_csv}")

            else:
                logger.warning("Meta-learner feature_importances_ and meta_feature_names_ lengths do not match.")
                logger.info(f"  Length of feature_importances_: {len(model.feature_importances_)}")
                logger.info(f"  Length of meta_feature_names_: {len(model.meta_feature_names_)}")
        else:
            logger.warning("The loaded SequentialThinkingModel does not have 'feature_importances_' or 'meta_feature_names_' attributes, "
                           "or they are None. This might happen if _calculate_feature_importances was not called or failed, "
                           "or if the meta-learner does not support feature importance.")
    else:
        logger.warning("No meta_model found in the loaded SequentialThinkingModel object.")

    # 2. Base model feature importances (more complex, depends on how X_train was structured for them)
    #    The SequentialThinkingModel trains base models on X_original_train (which has original feature names)
    #    or generic names if train_model.py renames them before calling train_sequential_thinking_model.
    #    In our train_model.py, X is renamed to generic feature_0, feature_1... *before* splitting and
    #    then X_train (with generic names) is passed to train_sequential_thinking_model.
    #    So, base models in SequentialThinkingModel are trained on these generic names.
    
    if hasattr(model, 'base_models') and model.base_models and hasattr(model, 'base_model_names'):
        logger.info("\\n--- Base Model Feature Importances ---")
        # Assuming X_train passed to SequentialThinkingModel.fit() had columns like 'feature_0', 'feature_1', etc.
        # We need to know the number of such features.
        # This is tricky as the model object itself might not store the original X_train.
        # Let's try to infer from the first base model if it has feature_importances_
        
        # Attempt to get the actual feature names used for training the base models.
        # This information is not directly stored in the SequentialThinkingModel in a straightforward way
        # for base models after they are trained.
        # We rely on the fact that X_train passed to `model.fit` had generic names
        # and the number of features can be inferred if the base model has `n_features_in_`.
        
        # We will load the `final_model_feature_names.json` which should map generic names back to original names.
        project_root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Assumes this script is in steel_temp_prediction
        if os.path.basename(project_root_dir) == "steel_temp_prediction": # If script is in steel_temp_prediction
            project_root_dir = os.path.dirname(project_root_dir) # Go one level up to project root

        feature_names_map_path = os.path.join(project_root_dir, "results", "final_model_feature_names.json")
        actual_feature_names = None
        if os.path.exists(feature_names_map_path):
            try:
                import json
                with open(feature_names_map_path, 'r', encoding='utf-8') as f:
                    actual_feature_names = json.load(f)
                logger.info(f"Loaded {len(actual_feature_names)} actual feature names from {feature_names_map_path}")
            except Exception as e_json:
                logger.error(f"Error loading actual feature names from JSON: {e_json}")
        else:
            logger.warning(f"Actual feature names map not found at: {feature_names_map_path}. Base model feature names will be generic.")

        for i, base_model_instance in enumerate(model.base_models):
            model_name = model.base_model_names[i]
            logger.info(f"\\n-- Base Model: {model_name} ({type(base_model_instance).__name__}) --")
            if hasattr(base_model_instance, 'feature_importances_'):
                importances = base_model_instance.feature_importances_
                num_base_features = len(importances)
                
                # Determine feature names for base models
                current_feature_names = [f'feature_{j}' for j in range(num_base_features)] # Default to generic
                if actual_feature_names and len(actual_feature_names) == num_base_features:
                    current_feature_names = actual_feature_names
                    logger.info(f"Using actual feature names for base model {model_name}.")
                elif actual_feature_names:
                    logger.warning(f"Mismatch in length between actual_feature_names ({len(actual_feature_names)}) and "
                                   f"importances for base model {model_name} ({num_base_features}). Using generic names.")

                base_importances_df = pd.DataFrame({
                    'feature': current_feature_names,
                    'importance': importances
                })
                base_importances_df = base_importances_df.sort_values(by='importance', ascending=False).reset_index(drop=True)
                
                logger.info(f"Feature importances (Top 30 or all):")
                logger.info(f"\\n{base_importances_df.head(min(30, len(base_importances_df)))}")
                if len(base_importances_df) > 30:
                    logger.info("... (and more)")

                # 保存基础模型特征重要性到CSV
                base_importance_path = os.path.join(
                    os.path.dirname(model_path), 
                    f"base_model_{model_name}_feature_importances.csv"
                )
                try:
                    base_importances_df.to_csv(base_importance_path, index=False, encoding='utf-8-sig')
                    logger.info(f"Base model {model_name} feature importances saved to: {base_importance_path}")
                except Exception as e_csv:
                    logger.error(f"Error saving base model {model_name} feature importances to CSV: {e_csv}")
            else:
                logger.warning(f"Base model {model_name} does not have 'feature_importances_' attribute.")
    else:
        logger.warning("No base_models found or base_model_names missing in the loaded SequentialThinkingModel object.")


if __name__ == "__main__":
    # 推断项目根目录，然后是模型路径
    # 假设此脚本位于 steel_temp_prediction 文件夹内
    current_script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_script_dir) 
    
    # 如果脚本本身就在项目根目录下（例如，如果 steel_temp_prediction 是一个包，而这个脚本在包外）
    # 这个简单的路径逻辑可能需要根据实际的项目结构调整。
    # 为了更通用，我们直接查找 'results' 文件夹相对于脚本的位置。
    # 我们假设 'results' 文件夹与 'steel_temp_prediction' 文件夹同级，即在项目根目录。
    
    # 更可靠的定位模型路径的方式是基于脚本相对于项目根目录的已知位置。
    # 如果 `analyze_feature_importance.py` 在 `steel_temp_prediction/` 中：
    model_file_path = os.path.join(project_root, "results", "sequential_thinking_model_v2.pkl")

    # 如果 `project_root` 的判断不准确 (例如，如果 `steel_temp_prediction` 不是一个直接的子目录)
    # 可以尝试更直接的相对路径，但这不够鲁棒
    # model_file_path = "../results/sequential_thinking_model_v2.pkl" 

    logger.info(f"Attempting to load model from resolved path: {os.path.abspath(model_file_path)}")

    if not os.path.exists(model_file_path):
        logger.error(f"Model file not found at resolved path: {os.path.abspath(model_file_path)}")
        logger.error("Please ensure the model path is correct. If train_model.py saved the model elsewhere, update the path.")
        # Fallback for common case where script might be run from project root
        alt_model_path = os.path.join("results", "sequential_thinking_model_v2.pkl")
        if os.path.exists(alt_model_path):
            logger.info(f"Found model at alternative path: {os.path.abspath(alt_model_path)}")
            model_file_path = alt_model_path
        else:
            logger.error(f"Also not found at alternative path: {os.path.abspath(alt_model_path)}")
            sys.exit(1)
            
    display_feature_importances(model_file_path) 