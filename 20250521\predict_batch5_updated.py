"""
预测第五批测试数据的钢水温度（更新版本）
- 移除Ridge和Lasso两个模型，保留SVR模型
- 添加最后2分钟添加材料的校正因子：每100kg降低5°C
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any

# 添加项目目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from steel_temp_prediction.data_preprocessing import preprocess_data
from steel_temp_prediction.feature_engineering import engineer_features
from steel_temp_prediction.utils import create_directory, save_model, load_model

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch5_prediction_corrected.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_batch5_data(file_path: str) -> pd.DataFrame:
    """
    加载第五批测试数据并统一列名

    Args:
        file_path: 数据文件路径

    Returns:
        加载的数据DataFrame
    """
    logger.info(f"加载第五批测试数据: {file_path}")
    try:
        data = pd.read_excel(file_path)
        logger.info(f"数据加载成功，原始形状: {data.shape}")

        # 统一列名，使其与训练数据一致
        column_mapping = {
            '炉子最大倾角': '最大角度',
            '平均流速M3/h': '气体流量流速平均',
            '最小流速': '最低流速',
            '最后2分钟加料': '最后2分钟',
            '总氧化碳(kg)': '气体总C'
        }

        # 重命名列
        for old_col, new_col in column_mapping.items():
            if old_col in data.columns:
                data.rename(columns={old_col: new_col}, inplace=True)
                logger.info(f"列名重命名: {old_col} -> {new_col}")

        logger.info(f"数据处理后形状: {data.shape}")
        return data
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        raise

def load_trained_models(models_dir: str = "results") -> Dict[str, Any]:
    """
    加载训练好的模型，排除Ridge和Lasso两个模型

    Args:
        models_dir: 模型保存目录

    Returns:
        模型字典 {模型名称: 模型对象}
    """
    logger.info(f"从 {models_dir} 加载训练好的模型")
    models = {}

    # 加载顺序思维模型
    try:
        sequential_model_path = os.path.join(models_dir, "sequential_thinking_model.pkl")
        if os.path.exists(sequential_model_path):
            models["Sequential Thinking Model"] = load_model(sequential_model_path)
            logger.info("顺序思维模型加载成功")
    except Exception as e:
        logger.error(f"加载顺序思维模型失败: {e}")

    # 加载基础模型，排除Ridge和Lasso两个模型
    # 根据用户指示，我们需要移除Ridge和Lasso模型，保留SVR模型
    base_model_names = ["xgboost", "lightgbm", "random_forest", "svr"]  # 移除了"ridge"和"lasso"
    for name in base_model_names:
        try:
            model_path = os.path.join(models_dir, f"{name}_model.pkl")
            if os.path.exists(model_path):
                models[name] = load_model(model_path)
                logger.info(f"{name} 模型加载成功")
        except Exception as e:
            logger.error(f"加载 {name} 模型失败: {e}")

    logger.info(f"共加载 {len(models)} 个模型")
    return models

def apply_material_correction(df: pd.DataFrame, predictions: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """
    应用最后2分钟添加材料的校正因子：每100kg降低5°C

    Args:
        df: 原始数据DataFrame
        predictions: 模型预测结果字典

    Returns:
        校正后的预测结果字典
    """
    logger.info("应用最后2分钟添加材料的校正因子")

    corrected_predictions = {}

    # 检查是否有"最后2分钟"列
    if '最后2分钟' in df.columns:
        # 计算校正值：每100kg降低5°C
        correction = df['最后2分钟'] * (-5/100)
        logger.info(f"校正范围: {correction.min():.2f}°C 到 {correction.max():.2f}°C")

        # 应用校正到每个模型的预测结果
        for name, pred in predictions.items():
            corrected_predictions[name] = pred + correction.values
            logger.info(f"应用校正到 {name} 模型，校正前范围: {np.min(pred):.2f}°C - {np.max(pred):.2f}°C, "
                       f"校正后范围: {np.min(corrected_predictions[name]):.2f}°C - {np.max(corrected_predictions[name]):.2f}°C")
    else:
        logger.warning("数据中没有'最后2分钟'列，无法应用校正因子")
        corrected_predictions = predictions

    return corrected_predictions

def predict_batch5(batch5_data: pd.DataFrame, models: Dict[str, Any]) -> pd.DataFrame:
    """
    对第五批数据进行预测

    Args:
        batch5_data: 第五批测试数据
        models: 模型字典

    Returns:
        包含预测结果的DataFrame
    """
    logger.info("开始预处理第五批数据")

    # 预处理数据
    batch5_processed = batch5_data.copy()

    # 工程化特征
    logger.info("开始特征工程")
    try:
        # 使用与训练时相同的特征工程流程
        from steel_temp_prediction.feature_engineering import create_basic_features, create_metallurgical_features
        from steel_temp_prediction.feature_engineering import create_time_series_features, create_interaction_features
        from steel_temp_prediction.feature_engineering import create_polynomial_features, select_best_features

        # 创建基础特征
        batch5_processed = create_basic_features(batch5_processed)

        # 创建冶金特征
        batch5_processed = create_metallurgical_features(batch5_processed)

        # 创建时序特征
        batch5_processed = create_time_series_features(batch5_processed)

        # 创建交互特征
        batch5_processed = create_interaction_features(batch5_processed)

        # 创建多项式特征
        key_features = [
            '铁水温度', '铁水SI', '铁水C', '铁水废钢比', '单位铁水供氧量',
            '吹氧强度', '总处理时间min'
        ]
        key_features = [f for f in key_features if f in batch5_processed.columns]
        batch5_processed = create_polynomial_features(batch5_processed, degree=2, feature_names=key_features)

        logger.info("特征工程完成")
    except Exception as e:
        logger.error(f"特征工程失败: {e}")
        raise

    # 准备预测特征
    logger.info("准备预测特征")
    try:
        # 移除非数值列
        numeric_cols = batch5_processed.select_dtypes(include=['float64', 'int64']).columns
        X_batch5 = batch5_processed[numeric_cols]

        # 保存原始数据，用于结果展示
        original_data = batch5_data.copy()

        logger.info(f"预测特征准备完成，特征数量: {X_batch5.shape[1]}")
    except Exception as e:
        logger.error(f"准备预测特征失败: {e}")
        raise

    # 使用各个模型进行预测
    logger.info("开始使用各模型进行预测")
    predictions = {}

    for name, model in models.items():
        try:
            # 对于顺序思维模型，需要特殊处理
            if name == "Sequential Thinking Model" and hasattr(model, 'base_models'):
                # 获取顺序思维模型的基础模型预测
                base_predictions = np.zeros((X_batch5.shape[0], len(model.base_models)))

                for i, base_model in enumerate(model.base_models):
                    # 对于每个基础模型，确保特征匹配
                    if hasattr(base_model, 'feature_names_'):
                        # 获取模型需要的特征
                        model_features = base_model.feature_names_
                        # 找出共同特征
                        common_features = [f for f in model_features if f in X_batch5.columns]
                        if len(common_features) < len(model_features):
                            logger.warning(f"基础模型 {i} 期望 {len(model_features)} 个特征，但只找到 {len(common_features)} 个匹配特征")
                        # 使用共同特征进行预测
                        base_predictions[:, i] = base_model.predict(X_batch5[common_features])
                    else:
                        # 对于没有feature_names_属性的模型，尝试直接预测
                        try:
                            base_predictions[:, i] = base_model.predict(X_batch5)
                        except Exception as e:
                            logger.error(f"基础模型 {i} 预测失败: {e}")
                            # 使用平均值填充
                            base_predictions[:, i] = np.mean(base_predictions[:, :i], axis=1) if i > 0 else 1600  # 默认值

                # 使用基础模型预测结果的平均值作为最终预测
                predictions[name] = np.mean(base_predictions, axis=1)
                logger.info(f"{name} 模型预测完成，预测范围: {np.min(predictions[name]):.2f}°C - {np.max(predictions[name]):.2f}°C")

            # 对于其他模型，尝试直接预测
            else:
                try:
                    if hasattr(model, 'feature_names_'):
                        # 对于有feature_names_属性的模型（如XGBoost）
                        model_features = model.feature_names_
                        common_features = [f for f in model_features if f in X_batch5.columns]
                        if len(common_features) < len(model_features):
                            logger.warning(f"{name} 模型期望 {len(model_features)} 个特征，但只找到 {len(common_features)} 个匹配特征")
                        pred = model.predict(X_batch5[common_features])
                    else:
                        # 对于没有feature_names_属性的模型
                        pred = model.predict(X_batch5)

                    predictions[name] = pred
                    logger.info(f"{name} 模型预测完成，预测范围: {np.min(pred):.2f}°C - {np.max(pred):.2f}°C")
                except Exception as e:
                    logger.error(f"{name} 模型预测失败: {e}")
                    # 如果有其他成功的预测，使用它们的平均值
                    if predictions:
                        predictions[name] = np.mean(list(predictions.values()), axis=0)
                        logger.info(f"{name} 模型使用其他模型的平均值，预测范围: {np.min(predictions[name]):.2f}°C - {np.max(predictions[name]):.2f}°C")
        except Exception as e:
            logger.error(f"{name} 模型预测失败: {e}")

    # 如果没有成功的预测，创建一个基于理论终点温度的预测
    if not predictions and '理论终点温度' in X_batch5.columns:
        logger.warning("所有模型预测失败，使用理论终点温度作为预测")
        predictions["理论模型"] = X_batch5['理论终点温度'].values
    elif not predictions:
        logger.warning("所有模型预测失败，使用铁水温度加上固定值作为预测")
        predictions["基础模型"] = X_batch5['铁水温度'].values + 50  # 假设升温50°C

    # 应用最后2分钟添加材料的校正因子
    predictions = apply_material_correction(original_data, predictions)

    # 创建结果DataFrame
    logger.info("整合预测结果")
    results = pd.DataFrame()

    # 添加原始数据的关键列
    if '炉号' in original_data.columns:
        results['炉号'] = original_data['炉号']
    if '钢种' in original_data.columns:
        results['钢种'] = original_data['钢种']
    if '铁水温度' in original_data.columns:
        results['铁水温度'] = original_data['铁水温度']
    if '最后2分钟' in original_data.columns:
        results['最后2分钟添加量'] = original_data['最后2分钟']

    # 添加各模型的预测结果
    for name, pred in predictions.items():
        results[f'{name}_预测温度'] = pred

    # 计算综合预测温度（如果有顺序思维模型，使用它的结果，否则使用所有模型的平均值）
    if "Sequential Thinking Model" in predictions:
        results['综合预测温度'] = predictions["Sequential Thinking Model"]
    else:
        results['综合预测温度'] = np.mean([pred for pred in predictions.values()], axis=0)

    logger.info("预测完成")
    return results

def save_prediction_results(results: pd.DataFrame, output_path: str = "batch5_predictions_corrected.xlsx") -> None:
    """
    保存预测结果

    Args:
        results: 预测结果DataFrame
        output_path: 输出文件路径
    """
    logger.info(f"保存预测结果到: {output_path}")
    try:
        results.to_excel(output_path, index=False)
        logger.info("预测结果保存成功")
    except Exception as e:
        logger.error(f"保存预测结果失败: {e}")
        raise

def analyze_prediction_results(results: pd.DataFrame) -> None:
    """
    分析预测结果

    Args:
        results: 预测结果DataFrame
    """
    logger.info("分析预测结果")

    # 计算综合预测温度的统计信息
    mean_temp = results['综合预测温度'].mean()
    std_temp = results['综合预测温度'].std()
    min_temp = results['综合预测温度'].min()
    max_temp = results['综合预测温度'].max()

    logger.info(f"综合预测温度统计:")
    logger.info(f"  平均值: {mean_temp:.2f}°C")
    logger.info(f"  标准差: {std_temp:.2f}°C")
    logger.info(f"  最小值: {min_temp:.2f}°C")
    logger.info(f"  最大值: {max_temp:.2f}°C")

    # 如果有钢种信息，按钢种分析
    if '钢种' in results.columns:
        logger.info("按钢种分析预测温度:")
        for steel_type, group in results.groupby('钢种'):
            logger.info(f"  钢种 {steel_type}:")
            logger.info(f"    样本数: {len(group)}")
            logger.info(f"    平均预测温度: {group['综合预测温度'].mean():.2f}°C")
            logger.info(f"    标准差: {group['综合预测温度'].std():.2f}°C")

    # 创建预测温度分布图
    plt.figure(figsize=(10, 6))
    plt.hist(results['综合预测温度'], bins=20, alpha=0.7)
    plt.axvline(mean_temp, color='r', linestyle='--', label=f'平均值: {mean_temp:.2f}°C')
    plt.title('第五批数据预测温度分布')
    plt.xlabel('预测温度 (°C)')
    plt.ylabel('频次')
    plt.legend()
    plt.grid(alpha=0.3)
    plt.savefig('batch5_temperature_distribution_corrected.png', dpi=300, bbox_inches='tight')
    logger.info("预测温度分布图已保存")

    # 如果有铁水温度，分析铁水温度与预测温度的关系
    if '铁水温度' in results.columns:
        plt.figure(figsize=(10, 6))
        plt.scatter(results['铁水温度'], results['综合预测温度'], alpha=0.5)
        plt.title('铁水温度与预测出钢温度关系')
        plt.xlabel('铁水温度 (°C)')
        plt.ylabel('预测出钢温度 (°C)')
        plt.grid(alpha=0.3)
        plt.savefig('batch5_hotmetal_vs_prediction_corrected.png', dpi=300, bbox_inches='tight')
        logger.info("铁水温度与预测温度关系图已保存")

    # 比较不同模型的预测结果
    model_columns = [col for col in results.columns if col.endswith('_预测温度')]
    if len(model_columns) > 1:
        logger.info("比较不同模型的预测结果:")
        for col in model_columns:
            model_name = col.replace('_预测温度', '')
            logger.info(f"  {model_name}:")
            logger.info(f"    平均值: {results[col].mean():.2f}°C")
            logger.info(f"    标准差: {results[col].std():.2f}°C")
            logger.info(f"    与综合预测的平均差异: {(results[col] - results['综合预测温度']).abs().mean():.2f}°C")

def main():
    """主函数"""
    logger.info("开始处理第五批测试数据（更新版本）")

    # 第五批数据文件路径
    batch5_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"

    # 检查文件是否存在
    if not os.path.exists(batch5_file):
        logger.error(f"文件不存在: {batch5_file}")
        print(f"错误: 找不到文件 {batch5_file}")
        return

    # 创建结果目录
    create_directory("results")

    # 加载第五批数据
    batch5_data = load_batch5_data(batch5_file)

    # 加载训练好的模型（排除SVR和另一个高温预测模型）
    models = load_trained_models()

    # 预测第五批数据
    results = predict_batch5(batch5_data, models)

    # 保存预测结果
    save_prediction_results(results)

    # 分析预测结果
    analyze_prediction_results(results)

    logger.info("第五批数据处理完成（修正版本 - 保留SVR模型，移除Ridge和Lasso模型）")
    print("第五批数据预测完成，结果已保存到 batch5_predictions_corrected.xlsx")

if __name__ == "__main__":
    main()
