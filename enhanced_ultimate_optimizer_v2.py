"""
增强终极优化器 V2.0
基于CJS-SLLE降维与即时学习的转炉炼钢终点碳温软测量方法 - 改进版

核心改进：
1. 更保守的CJS-SLLE降维策略
2. 优化的LNN-DPC聚类参数
3. 结合现有高性能模型（CatBoost等）
4. 改进的集成权重策略
5. 增强的特征选择
6. 更好的数据预处理

目标：在现有75.5%基础上，提升命中率到82.6%以上
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime
import joblib
import pickle
from collections import deque
import math

# 核心机器学习库
from sklearn.model_selection import train_test_split, TimeSeriesSplit, StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler, MinMaxScaler
from sklearn.ensemble import RandomForestRegressor, VotingRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet, LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.feature_selection import SelectKBest, f_regression, RFE, SelectFromModel, mutual_info_regression
from sklearn.decomposition import PCA, KernelPCA
from sklearn.manifold import LocallyLinearEmbedding, Isomap
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.neighbors import NearestNeighbors, KNeighborsRegressor
from sklearn.svm import SVR
import xgboost as xgb
import lightgbm as lgb

# CatBoost
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
    print("✅ CatBoost successfully loaded")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("❌ CatBoost not available")

# 超参数优化
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
    print("✅ Optuna successfully loaded")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("❌ Optuna not available")

# 数据增强和科学计算
try:
    from scipy import stats
    from scipy.interpolate import interp1d
    from scipy.signal import savgol_filter
    from scipy.spatial.distance import pdist, squareform
    SCIPY_AVAILABLE = True
    print("✅ SciPy successfully loaded")
except ImportError:
    SCIPY_AVAILABLE = False
    print("❌ SciPy not available")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"enhanced_ultimate_v2_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedUltimateOptimizerV2:
    """增强终极优化器 V2.0"""

    def __init__(self):
        self.models = {}
        self.best_params = {}
        self.scalers = {}
        self.feature_selectors = {}
        self.dimensionality_reducers = {}
        self.cluster_models = {}
        self.ensemble_weights = {}
        self.performance_history = deque(maxlen=100)

        # 目标范围参数
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

        # 历史基准
        self.baseline_accuracy = 75.5  # 当前最佳
        self.target_accuracy = 82.6    # 目标精度

        # 改进的CJS-SLLE参数（更保守）
        self.cjs_slle_params = {
            'n_neighbors': [5, 8, 10, 12],  # 减少邻居数
            'n_components': [30, 35, 40],   # 保留更多维度
            'reg': [1e-3, 1e-4, 1e-5],     # 更大的正则化
            'eigen_solver': ['auto'],
            'method': ['standard']
        }

        # 改进的LNN-DPC参数
        self.lnn_dpc_params = {
            'density_threshold': 0.05,      # 提高密度阈值
            'distance_threshold': 0.15,     # 提高距离阈值
            'min_cluster_size': 10,         # 增加最小聚类大小
            'max_clusters': 12,             # 减少最大聚类数
            'local_k': [8, 10, 12],         # 减少局部邻居数
            'weight_decay': [0.9, 0.95]     # 更高的权重衰减
        }

        # 即时学习参数
        self.jit_learning_params = {
            'similarity_threshold': 0.85,   # 提高相似度阈值
            'local_model_size': 20,         # 减少局部模型大小
            'update_frequency': 3,
            'forgetting_factor': 0.95,
            'adaptation_rate': 0.05
        }

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def enhanced_data_preprocessing(self, df: pd.DataFrame) -> pd.DataFrame:
        """增强数据预处理"""
        logger.info("开始增强数据预处理")

        df_processed = df.copy()

        # 1. 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
                return float(value)
            except:
                return default

        # 2. 处理数值列
        numeric_columns = ['铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                          '铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s']

        for col in numeric_columns:
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].apply(safe_convert)

        # 3. 移除无穷大值和异常值
        df_processed = df_processed.replace([np.inf, -np.inf], np.nan)

        # 4. 使用更严格的约束范围
        constraints = {
            '铁水温度': (1300, 1460),
            '铁水C': (3.5, 5.0),
            '铁水SI': (0.2, 1.0),
            '铁水MN': (0.1, 0.7),
            '铁水P': (0.08, 0.22),
            '铁水': (70, 110),
            '废钢': (5, 40),
            '累氧实际': (4000, 6000),
            '吹氧时间s': (400, 1000)
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].clip(min_val, max_val)

        # 5. 目标变量处理
        if '钢水温度' in df_processed.columns:
            df_processed['钢水温度'] = df_processed['钢水温度'].apply(safe_convert)
            df_processed = df_processed[(df_processed['钢水温度'] >= 1540) & (df_processed['钢水温度'] <= 1700)]

        # 6. 更温和的异常值处理
        if SCIPY_AVAILABLE:
            for col in numeric_columns:
                if col in df_processed.columns:
                    # 使用更宽松的IQR方法
                    Q1 = df_processed[col].quantile(0.15)
                    Q3 = df_processed[col].quantile(0.85)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 2.0 * IQR  # 更宽松的边界
                    upper_bound = Q3 + 2.0 * IQR

                    outlier_mask = (df_processed[col] < lower_bound) | (df_processed[col] > upper_bound)

                    if outlier_mask.sum() > 0:
                        # 用分位数替换异常值
                        median_val = df_processed[col].median()
                        df_processed.loc[outlier_mask, col] = median_val
                        logger.info(f"处理{col}列的{outlier_mask.sum()}个异常值")

        logger.info(f"增强数据预处理完成，保留{len(df_processed)}条记录")
        return df_processed

    def advanced_feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """高级特征工程"""
        logger.info("开始高级特征工程")

        df_features = df.copy()

        # === 基础工程特征 ===
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # === 成分交互特征 ===
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['C_MN_interaction'] = df_features['铁水C'] * df_features['铁水MN']
        df_features['SI_MN_interaction'] = df_features['铁水SI'] * df_features['铁水MN']
        df_features['total_impurities'] = df_features['铁水SI'] + df_features['铁水MN'] + df_features['铁水P'] + df_features['铁水S']

        # === 温度相关特征 ===
        df_features['temp_carbon_ratio'] = df_features['铁水温度'] / (df_features['铁水C'] + 1e-6)
        df_features['temp_oxygen_ratio'] = df_features['铁水温度'] / (df_features['oxygen_intensity'] + 1e-6)

        # === 比率特征 ===
        df_features['C_to_SI_ratio'] = df_features['铁水C'] / (df_features['铁水SI'] + 1e-6)
        df_features['oxygen_to_carbon_ratio'] = df_features['oxygen_intensity'] / (df_features['铁水C'] + 1e-6)

        logger.info("高级特征工程完成")
        return df_features

    def intelligent_feature_selection(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, List[str]]:
        """智能特征选择"""
        logger.info("开始智能特征选择")

        # 1. 移除高相关性特征
        corr_matrix = X.corr().abs()
        upper_tri = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
        high_corr_features = [column for column in upper_tri.columns if any(upper_tri[column] > 0.95)]

        X_filtered = X.drop(columns=high_corr_features)
        logger.info(f"移除{len(high_corr_features)}个高相关性特征")

        # 2. 基于互信息的特征选择
        if len(X_filtered.columns) > 30:
            mi_scores = mutual_info_regression(X_filtered, y, random_state=42)
            mi_threshold = np.percentile(mi_scores, 25)  # 保留75%的特征
            selected_features = X_filtered.columns[mi_scores > mi_threshold].tolist()
            X_filtered = X_filtered[selected_features]
            logger.info(f"基于互信息选择{len(selected_features)}个特征")

        # 3. 基于方差的特征选择
        from sklearn.feature_selection import VarianceThreshold
        var_selector = VarianceThreshold(threshold=0.01)
        X_var_selected = var_selector.fit_transform(X_filtered)
        selected_features = X_filtered.columns[var_selector.get_support()].tolist()
        X_final = pd.DataFrame(X_var_selected, columns=selected_features, index=X_filtered.index)

        logger.info(f"最终选择{len(selected_features)}个特征")
        return X_final, selected_features

    def conservative_cjs_slle_reduction(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, Dict]:
        """保守的CJS-SLLE降维方法"""
        logger.info("开始保守CJS-SLLE降维")

        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        best_score = -np.inf
        best_reducer = None
        best_params = None
        best_X_reduced = None

        # 尝试不同的LLE参数组合
        for n_neighbors in self.cjs_slle_params['n_neighbors']:
            for n_components in self.cjs_slle_params['n_components']:
                for reg in self.cjs_slle_params['reg']:
                    try:
                        # 确保参数合理
                        n_comp = min(n_components, X_scaled.shape[0] - 1, X_scaled.shape[1])
                        n_neigh = min(n_neighbors, X_scaled.shape[0] - 1)

                        if n_comp <= 0 or n_neigh <= 0 or n_comp >= X_scaled.shape[1]:
                            continue

                        # 创建LLE降维器
                        lle = LocallyLinearEmbedding(
                            n_neighbors=n_neigh,
                            n_components=n_comp,
                            reg=reg,
                            eigen_solver='auto',
                            random_state=42
                        )

                        # 降维
                        X_reduced = lle.fit_transform(X_scaled)

                        # 评估降维效果
                        lr = LinearRegression()
                        scores = cross_val_score(lr, X_reduced, y, cv=3, scoring='neg_mean_absolute_error')
                        score = np.mean(scores)

                        if score > best_score:
                            best_score = score
                            best_reducer = lle
                            best_params = {
                                'n_neighbors': n_neigh,
                                'n_components': n_comp,
                                'reg': reg
                            }
                            best_X_reduced = X_reduced

                    except Exception as e:
                        continue

        # 如果降维效果不好，保留原始特征
        if best_reducer is not None and best_score > -25:  # 阈值调整
            # 转换为DataFrame
            feature_names = [f'CJS_SLLE_{i}' for i in range(best_X_reduced.shape[1])]
            X_reduced_df = pd.DataFrame(best_X_reduced, columns=feature_names, index=X.index)

            reducer_info = {
                'scaler': scaler,
                'reducer': best_reducer,
                'params': best_params,
                'score': best_score,
                'used': True
            }

            logger.info(f"CJS-SLLE降维完成: {X.shape[1]} -> {best_X_reduced.shape[1]} 维, 评分: {best_score:.4f}")
            return X_reduced_df, reducer_info
        else:
            logger.info("CJS-SLLE降维效果不佳，保留原始特征")
            return X, {'used': False}

    def optimized_lnn_dpc_clustering(self, X: pd.DataFrame) -> Tuple[np.ndarray, Dict]:
        """优化的LNN-DPC聚类方法"""
        logger.info("开始优化LNN-DPC聚类")

        if not SCIPY_AVAILABLE or len(X) < 50:
            logger.warning("使用KMeans聚类作为备选")
            kmeans = KMeans(n_clusters=min(8, len(X)//20), random_state=42)
            cluster_labels = kmeans.fit_predict(X)
            return cluster_labels, {'method': 'kmeans', 'model': kmeans, 'n_clusters': len(np.unique(cluster_labels))}

        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 计算距离矩阵
        distances = pdist(X_scaled, metric='euclidean')
        distance_matrix = squareform(distances)

        # 计算局部密度
        dc = np.percentile(distances, self.lnn_dpc_params['density_threshold'] * 100)
        rho = np.zeros(X_scaled.shape[0])

        for i in range(X_scaled.shape[0]):
            rho[i] = np.sum(distance_matrix[i] < dc) - 1

        # 计算相对距离
        delta = np.zeros(X_scaled.shape[0])
        nneigh = np.zeros(X_scaled.shape[0], dtype=int)

        # 按密度排序
        rho_sorted_idx = np.argsort(-rho)

        for i, idx in enumerate(rho_sorted_idx):
            if i == 0:
                delta[idx] = np.max(distance_matrix[idx])
            else:
                # 找到密度更高的最近邻
                higher_density_idx = rho_sorted_idx[:i]
                distances_to_higher = distance_matrix[idx][higher_density_idx]
                min_dist_idx = np.argmin(distances_to_higher)
                delta[idx] = distances_to_higher[min_dist_idx]
                nneigh[idx] = higher_density_idx[min_dist_idx]

        # 计算gamma值（密度*距离）
        gamma = rho * delta

        # 选择聚类中心（更保守的策略）
        gamma_threshold = np.percentile(gamma, 90)  # 降低阈值
        cluster_centers = np.where(gamma > gamma_threshold)[0]

        if len(cluster_centers) == 0:
            cluster_centers = [np.argmax(gamma)]
        elif len(cluster_centers) > self.lnn_dpc_params['max_clusters']:
            # 选择gamma值最大的几个点
            top_indices = np.argsort(-gamma)[:self.lnn_dpc_params['max_clusters']]
            cluster_centers = top_indices

        # 分配聚类标签
        cluster_labels = -1 * np.ones(X_scaled.shape[0], dtype=int)

        # 为聚类中心分配标签
        for i, center in enumerate(cluster_centers):
            cluster_labels[center] = i

        # 为其他点分配标签（按密度从高到低）
        for idx in rho_sorted_idx:
            if cluster_labels[idx] == -1:
                # 分配到最近的已标记点的聚类
                if nneigh[idx] != 0 and cluster_labels[nneigh[idx]] != -1:
                    cluster_labels[idx] = cluster_labels[nneigh[idx]]
                else:
                    # 分配到最近的聚类中心
                    distances_to_centers = distance_matrix[idx][cluster_centers]
                    nearest_center_idx = np.argmin(distances_to_centers)
                    cluster_labels[idx] = nearest_center_idx

        # 处理小聚类
        unique_labels = np.unique(cluster_labels)
        for label in unique_labels:
            if label != -1:
                cluster_size = np.sum(cluster_labels == label)
                if cluster_size < self.lnn_dpc_params['min_cluster_size']:
                    # 将小聚类合并到最近的大聚类
                    cluster_indices = np.where(cluster_labels == label)[0]
                    for idx in cluster_indices:
                        # 找到最近的大聚类
                        distances_to_others = []
                        for other_label in unique_labels:
                            if other_label != label and other_label != -1:
                                other_cluster_size = np.sum(cluster_labels == other_label)
                                if other_cluster_size >= self.lnn_dpc_params['min_cluster_size']:
                                    other_indices = np.where(cluster_labels == other_label)[0]
                                    min_dist = np.min(distance_matrix[idx][other_indices])
                                    distances_to_others.append((min_dist, other_label))

                        if distances_to_others:
                            distances_to_others.sort()
                            cluster_labels[idx] = distances_to_others[0][1]

        # 重新编号聚类标签
        unique_labels = np.unique(cluster_labels)
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        cluster_labels = np.array([label_mapping[label] for label in cluster_labels])

        cluster_info = {
            'method': 'lnn_dpc',
            'n_clusters': len(unique_labels),
            'cluster_centers': cluster_centers,
            'rho': rho,
            'delta': delta,
            'gamma': gamma,
            'scaler': scaler
        }

        logger.info(f"优化LNN-DPC聚类完成: {len(unique_labels)}个聚类")
        return cluster_labels, cluster_info

    def train_base_models(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict[str, Any]:
        """训练基础模型"""
        logger.info("开始训练基础模型")

        base_models = {}

        # 1. CatBoost（已知表现良好）
        if CATBOOST_AVAILABLE:
            try:
                catboost_model = cb.CatBoostRegressor(
                    iterations=500,
                    depth=8,
                    learning_rate=0.1,
                    random_seed=42,
                    verbose=False
                )
                catboost_model.fit(X_train, y_train)
                base_models['catboost'] = catboost_model
                logger.info("CatBoost模型训练完成")
            except Exception as e:
                logger.warning(f"CatBoost训练失败: {e}")

        # 2. XGBoost
        try:
            xgb_model = xgb.XGBRegressor(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.1,
                random_state=42,
                verbosity=0
            )
            xgb_model.fit(X_train, y_train)
            base_models['xgboost'] = xgb_model
            logger.info("XGBoost模型训练完成")
        except Exception as e:
            logger.warning(f"XGBoost训练失败: {e}")

        # 3. LightGBM
        try:
            lgb_model = lgb.LGBMRegressor(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.1,
                random_state=42,
                verbosity=-1
            )
            lgb_model.fit(X_train, y_train)
            base_models['lightgbm'] = lgb_model
            logger.info("LightGBM模型训练完成")
        except Exception as e:
            logger.warning(f"LightGBM训练失败: {e}")

        # 4. Random Forest
        try:
            rf_model = RandomForestRegressor(
                n_estimators=200,
                max_depth=12,
                random_state=42,
                n_jobs=-1
            )
            rf_model.fit(X_train, y_train)
            base_models['random_forest'] = rf_model
            logger.info("Random Forest模型训练完成")
        except Exception as e:
            logger.warning(f"Random Forest训练失败: {e}")

        logger.info(f"基础模型训练完成，共{len(base_models)}个模型")
        return base_models

    def enhanced_ensemble_prediction(self, base_models: Dict, X_test: pd.DataFrame,
                                   cluster_labels: np.ndarray, cluster_info: Dict) -> np.ndarray:
        """增强集成预测"""
        logger.info("开始增强集成预测")

        # 1. 获取基础模型预测
        base_predictions = {}
        for name, model in base_models.items():
            try:
                pred = model.predict(X_test)
                base_predictions[name] = pred
                logger.info(f"{name}预测完成")
            except Exception as e:
                logger.warning(f"{name}预测失败: {e}")

        if not base_predictions:
            logger.error("没有可用的基础模型预测")
            return np.full(len(X_test), 1620.0)  # 返回默认值

        # 2. 计算基础模型权重（基于历史性能）
        model_weights = {
            'catboost': 0.35,      # CatBoost表现最好
            'xgboost': 0.25,
            'lightgbm': 0.25,
            'random_forest': 0.15
        }

        # 3. 加权平均基础预测
        weighted_predictions = np.zeros(len(X_test))
        total_weight = 0

        for name, pred in base_predictions.items():
            weight = model_weights.get(name, 0.1)
            weighted_predictions += weight * pred
            total_weight += weight

        if total_weight > 0:
            weighted_predictions /= total_weight

        logger.info("增强集成预测完成")
        return weighted_predictions

    def adaptive_just_in_time_learning(self, X_train: pd.DataFrame, y_train: pd.Series,
                                     X_test: pd.DataFrame) -> np.ndarray:
        """自适应即时学习"""
        logger.info("开始自适应即时学习")

        predictions = []

        for i in range(len(X_test)):
            query_sample = X_test.iloc[i:i+1]

            # 计算相似度
            similarities = []
            for j in range(len(X_train)):
                train_sample = X_train.iloc[j:j+1]

                # 计算加权欧氏距离相似度
                distance = np.linalg.norm(query_sample.values - train_sample.values)
                similarity = np.exp(-distance / (2 * np.std(X_train.values)))
                similarities.append((similarity, j))

            # 选择最相似的样本
            similarities.sort(reverse=True)
            top_indices = [idx for _, idx in similarities[:self.jit_learning_params['local_model_size']]]

            # 获取局部数据
            local_X = X_train.iloc[top_indices]
            local_y = y_train.iloc[top_indices]
            local_weights = np.array([sim for sim, _ in similarities[:self.jit_learning_params['local_model_size']]])

            # 权重归一化
            local_weights = local_weights / np.sum(local_weights)

            # 训练局部模型（使用更简单的方法）
            try:
                # 加权平均预测
                prediction = np.average(local_y, weights=local_weights)
            except:
                prediction = local_y.mean()

            predictions.append(prediction)

        logger.info("自适应即时学习完成")
        return np.array(predictions)

    def train_enhanced_ultimate_model(self, X_train: pd.DataFrame, y_train: pd.Series,
                                    X_test: pd.DataFrame, y_test: pd.Series) -> Dict[str, Any]:
        """训练增强终极模型"""
        logger.info("开始训练增强终极模型")

        results = {}

        # 1. 智能特征选择
        X_selected, selected_features = self.intelligent_feature_selection(X_train, y_train)
        X_test_selected = X_test[selected_features]

        # 2. 保守CJS-SLLE降维
        X_reduced, reducer_info = self.conservative_cjs_slle_reduction(X_selected, y_train)

        if reducer_info.get('used', False):
            # 对测试集也进行降维
            scaler = reducer_info['scaler']
            reducer = reducer_info['reducer']
            X_test_scaled = scaler.transform(X_test_selected)
            X_test_reduced = reducer.transform(X_test_scaled)
            X_test_reduced = pd.DataFrame(X_test_reduced, columns=X_reduced.columns, index=X_test_selected.index)
        else:
            X_reduced = X_selected
            X_test_reduced = X_test_selected

        # 3. 优化LNN-DPC聚类
        cluster_labels, cluster_info = self.optimized_lnn_dpc_clustering(X_reduced)

        # 4. 训练基础模型
        base_models = self.train_base_models(X_reduced, y_train)

        # 5. 增强集成预测
        ensemble_predictions = self.enhanced_ensemble_prediction(
            base_models, X_test_reduced, cluster_labels, cluster_info
        )

        # 6. 自适应即时学习预测
        jit_predictions = self.adaptive_just_in_time_learning(X_reduced, y_train, X_test_reduced)

        # 7. 动态权重组合
        # 根据数据特点调整权重
        ensemble_weight = 0.8  # 更信任基础模型集成
        jit_weight = 0.2       # 即时学习作为补充

        final_predictions = ensemble_weight * ensemble_predictions + jit_weight * jit_predictions

        # 8. 后处理：确保预测值在合理范围内
        final_predictions = np.clip(final_predictions, 1520, 1720)

        # 9. 评估结果
        mae = mean_absolute_error(y_test, final_predictions)
        accuracy = self.calculate_target_accuracy(y_test.values, final_predictions)

        results = {
            'predictions': final_predictions,
            'ensemble_predictions': ensemble_predictions,
            'jit_predictions': jit_predictions,
            'mae': mae,
            'accuracy': accuracy,
            'selected_features': selected_features,
            'reducer_info': reducer_info,
            'cluster_info': cluster_info,
            'base_models': base_models,
            'ensemble_weight': ensemble_weight,
            'jit_weight': jit_weight
        }

        logger.info(f"增强终极模型训练完成: MAE={mae:.2f}°C, 命中率={accuracy:.1f}%")
        return results

def main():
    """主函数 - 增强终极优化系统 V2.0"""
    logger.info("=== 增强终极优化系统 V2.0 启动 ===")
    logger.info("目标：基于CJS-SLLE、大模型评估和LNN-DPC的改进优化")
    logger.info("当前基准：75.5% -> 目标：82.6%+")

    try:
        # 1. 环境检查
        logger.info("=== 环境检查 ===")
        logger.info(f"CatBoost可用: {CATBOOST_AVAILABLE}")
        logger.info(f"Optuna可用: {OPTUNA_AVAILABLE}")
        logger.info(f"SciPy可用: {SCIPY_AVAILABLE}")

        # 2. 数据加载
        logger.info("=== 数据加载 ===")
        train_df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        logger.info(f"训练数据: {train_df.shape}")

        # 3. 创建优化器
        optimizer = EnhancedUltimateOptimizerV2()

        # 4. 数据预处理
        logger.info("=== 增强数据预处理 ===")
        train_processed = optimizer.enhanced_data_preprocessing(train_df)
        logger.info(f"预处理后数据: {train_processed.shape}")

        # 5. 高级特征工程
        logger.info("=== 高级特征工程 ===")
        train_features = optimizer.advanced_feature_engineering(train_processed)
        logger.info(f"特征工程后: {train_features.shape}")

        # 6. 准备训练数据
        logger.info("=== 准备训练数据 ===")

        # 分离特征和目标
        target_col = '钢水温度'
        if target_col not in train_features.columns:
            logger.error(f"目标列 '{target_col}' 不存在")
            return

        # 选择数值特征
        feature_cols = train_features.select_dtypes(include=[np.number]).columns.tolist()
        if target_col in feature_cols:
            feature_cols.remove(target_col)

        X = train_features[feature_cols]
        y = train_features[target_col]

        # 处理缺失值
        X = X.fillna(X.median())
        y = y.fillna(y.median())

        logger.info(f"初始特征数量: {len(feature_cols)}")
        logger.info(f"样本数量: {len(X)}")

        # 7. 数据分割
        logger.info("=== 数据分割 ===")
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=None
        )

        logger.info(f"训练集: {X_train.shape}")
        logger.info(f"测试集: {X_test.shape}")

        # 8. 训练增强终极模型
        logger.info("=== 训练增强终极模型 ===")
        enhanced_results = optimizer.train_enhanced_ultimate_model(X_train, y_train, X_test, y_test)

        # 9. 结果分析
        logger.info("=== 结果分析 ===")
        mae = enhanced_results['mae']
        accuracy = enhanced_results['accuracy']

        logger.info(f"增强终极模型性能:")
        logger.info(f"  MAE: {mae:.2f}°C")
        logger.info(f"  目标范围±20°C精度: {accuracy:.1f}%")

        # 计算其他精度指标
        y_pred = enhanced_results['predictions']
        accuracy_15 = optimizer.calculate_target_accuracy(y_test.values, y_pred, tolerance=15)
        accuracy_10 = optimizer.calculate_target_accuracy(y_test.values, y_pred, tolerance=10)

        logger.info(f"  目标范围±15°C精度: {accuracy_15:.1f}%")
        logger.info(f"  目标范围±10°C精度: {accuracy_10:.1f}%")

        # 与基准比较
        improvement = accuracy - optimizer.baseline_accuracy
        logger.info(f"\n性能提升分析:")
        logger.info(f"  基准精度: {optimizer.baseline_accuracy:.1f}%")
        logger.info(f"  增强精度: {accuracy:.1f}%")
        logger.info(f"  绝对提升: {improvement:.1f}%")
        logger.info(f"  相对提升: {improvement/optimizer.baseline_accuracy*100:.1f}%")

        # 目标达成情况
        target_gap = optimizer.target_accuracy - accuracy
        logger.info(f"  目标精度: {optimizer.target_accuracy:.1f}%")
        logger.info(f"  距离目标: {target_gap:.1f}%")

        if accuracy >= optimizer.target_accuracy:
            logger.info("🎉 恭喜！已达到目标精度！")
        elif improvement > 0:
            logger.info("✅ 模型性能有所提升！")
        else:
            logger.info("⚠️ 模型性能需要进一步优化")

        # 10. 保存模型和结果
        logger.info("=== 保存模型和结果 ===")

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_filename = f"enhanced_ultimate_model_v2_{timestamp}.pkl"

        model_data = {
            'optimizer': optimizer,
            'results': enhanced_results,
            'performance': {
                'mae': mae,
                'accuracy_20': accuracy,
                'accuracy_15': accuracy_15,
                'accuracy_10': accuracy_10,
                'improvement': improvement
            }
        }

        joblib.dump(model_data, model_filename)
        logger.info(f"模型已保存: {model_filename}")

        # 保存结果报告
        report_filename = f"enhanced_ultimate_report_v2_{timestamp}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("增强终极优化系统 V2.0 报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"🎯 目标: 基于CJS-SLLE、大模型评估和LNN-DPC的改进优化\n\n")
            f.write(f"🔧 核心改进:\n")
            f.write(f"1. 保守的CJS-SLLE降维策略\n")
            f.write(f"2. 优化的LNN-DPC聚类参数\n")
            f.write(f"3. 结合现有高性能模型\n")
            f.write(f"4. 智能特征选择\n")
            f.write(f"5. 增强的集成权重策略\n\n")
            f.write(f"📊 增强终极模型性能:\n")
            f.write(f"  MAE: {mae:.2f}°C\n")
            f.write(f"  目标范围±20°C精度: {accuracy:.1f}%\n")
            f.write(f"  目标范围±15°C精度: {accuracy_15:.1f}%\n")
            f.write(f"  目标范围±10°C精度: {accuracy_10:.1f}%\n\n")
            f.write(f"📈 性能提升分析:\n")
            f.write(f"  基准精度: {optimizer.baseline_accuracy:.1f}%\n")
            f.write(f"  增强精度: {accuracy:.1f}%\n")
            f.write(f"  绝对提升: {improvement:.1f}%\n")
            f.write(f"  相对提升: {improvement/optimizer.baseline_accuracy*100:.1f}%\n")
            f.write(f"  目标精度: {optimizer.target_accuracy:.1f}%\n")
            f.write(f"  距离目标: {target_gap:.1f}%\n\n")

            f.write(f"🔬 技术细节:\n")
            f.write(f"  选择特征数: {len(enhanced_results['selected_features'])}\n")
            f.write(f"  降维使用: {enhanced_results['reducer_info'].get('used', False)}\n")
            f.write(f"  聚类数量: {enhanced_results['cluster_info']['n_clusters']}\n")
            f.write(f"  基础模型数: {len(enhanced_results['base_models'])}\n")
            f.write(f"  集成权重: {enhanced_results['ensemble_weight']:.2f}\n")
            f.write(f"  即时学习权重: {enhanced_results['jit_weight']:.2f}\n\n")

            f.write(f"💾 模型文件: {model_filename}\n")
            f.write(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        logger.info(f"报告已保存: {report_filename}")

        logger.info("=== 增强终极优化系统 V2.0 完成 ===")

        return {
            'model_filename': model_filename,
            'report_filename': report_filename,
            'performance': {
                'mae': mae,
                'accuracy': accuracy,
                'improvement': improvement
            }
        }

    except Exception as e:
        logger.error(f"增强终极优化系统执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
