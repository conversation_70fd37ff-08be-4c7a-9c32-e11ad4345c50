import joblib
import sys
import os

# Assuming this script is in the project root.
project_root = os.path.dirname(os.path.abspath(__file__))
package_path = os.path.join(project_root, "steel_temp_prediction")

# Add project root to sys.path to allow imports from steel_temp_prediction
if project_root not in sys.path:
    sys.path.insert(0, project_root)

SequentialThinkingModel = None
try:
    from steel_temp_prediction.model_development import SequentialThinkingModel
except ImportError as e_import:
    print(f"###ERROR_IMPORTING_STM###\n{e_import}")
    sys.exit(1)

MODEL_PATH = os.path.join(project_root, "results", "sequential_thinking_model.pkl")

if not os.path.exists(MODEL_PATH):
    print(f"###ERROR_MODEL_NOT_FOUND###\nModel file not found at: {MODEL_PATH}")
    sys.exit(1)

try:
    loaded_model = joblib.load(MODEL_PATH)
    found_features = False
    if isinstance(loaded_model, SequentialThinkingModel):
        if hasattr(loaded_model, 'base_models'):
            for bm in loaded_model.base_models:
                # Check if it's an XGBoost model by typical attributes
                if hasattr(bm, 'get_booster') and callable(bm.get_booster): 
                    booster = bm.get_booster()
                    if hasattr(booster, 'feature_names') and booster.feature_names:
                        print("###EXPECTED_FEATURES_START###")
                        for feature_name in booster.feature_names:
                            print(feature_name)
                        print("###EXPECTED_FEATURES_END###")
                        found_features = True
                        break # Found features from the first XGBoost base model
    
    if not found_features:
        if isinstance(loaded_model, SequentialThinkingModel):
             print("###ERROR_NO_XGB_FEATURES_FOUND_IN_STM###")
        else:
            print(f"###ERROR_MODEL_NOT_STM_INSTANCE###\nLoaded model type: {type(loaded_model)}")


except Exception as e:
    print(f"###ERROR_MODEL_LOAD_OR_ACCESS###\n{e}") 