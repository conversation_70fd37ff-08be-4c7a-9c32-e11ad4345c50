"""
阶段2：快速超参数优化（简化版）
目标：快速验证扩大超参数搜索空间的效果
预期提升：从74.4%提升到78-80%
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime

# 核心机器学习库
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb

# CatBoost
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
    print("✅ CatBoost successfully loaded")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("❌ CatBoost not available")

# 超参数优化
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
    print("✅ Optuna successfully loaded")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("❌ Optuna not available")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"step2_quick_opt_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Step2QuickOptimizer:
    """阶段2：快速超参数优化器"""

    def __init__(self):
        self.models = {}
        self.best_params = {}
        self.scalers = {}
        self.label_encoders = {}

        # 目标范围参数
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def multi_objective_loss(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """多目标优化损失函数（简化版）"""

        # 1. 精度损失（目标范围内的命中率）
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])
        if target_mask.sum() > 0:
            target_y_true = y_true[target_mask]
            target_y_pred = y_pred[target_mask]
            hit_rate = np.mean(np.abs(target_y_true - target_y_pred) <= self.target_tolerance)
            accuracy_loss = (1 - hit_rate) * 1000
        else:
            accuracy_loss = 1000

        # 2. 稳定性损失（预测方差）
        stability_loss = np.var(y_pred) / 100

        # 3. 物理一致性损失（预测值在合理范围内）
        physics_loss = np.mean((y_pred < 1500) | (y_pred > 1750)) * 500

        # 组合损失
        total_loss = 0.6 * accuracy_loss + 0.2 * stability_loss + 0.2 * physics_loss

        return total_loss

    def robust_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """稳健的数据清理"""
        logger.info("开始数据清理")

        df_clean = df.copy()

        # 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
                return float(value)
            except:
                return default

        # 处理数值列
        numeric_columns = ['铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                          '铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s']

        for col in numeric_columns:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].apply(safe_convert)

        # 移除无穷大值
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)

        # 基于物理约束的异常值检测
        constraints = {
            '铁水温度': (1250, 1500),
            '铁水C': (3.0, 5.5),
            '铁水SI': (0.1, 1.5),
            '铁水MN': (0.05, 1.0),
            '铁水P': (0.05, 0.3),
            '铁水': (60, 120),
            '废钢': (0, 50),
            '累氧实际': (3000, 7000),
            '吹氧时间s': (300, 1200)
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].clip(min_val, max_val)

        # 目标变量清理
        if '钢水温度' in df_clean.columns:
            df_clean['钢水温度'] = df_clean['钢水温度'].apply(safe_convert)
            df_clean = df_clean[(df_clean['钢水温度'] >= 1500) & (df_clean['钢水温度'] <= 1750)]

        logger.info(f"数据清理完成，保留{len(df_clean)}条记录")
        return df_clean

    def create_enhanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建增强特征"""
        logger.info("创建增强特征")

        df_features = df.copy()

        # 1. 基础工程特征
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # 2. 成分交互特征
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['C_MN_interaction'] = df_features['铁水C'] * df_features['铁水MN']
        df_features['SI_MN_interaction'] = df_features['铁水SI'] * df_features['铁水MN']
        df_features['C_P_interaction'] = df_features['铁水C'] * df_features['铁水P']

        # 3. 温度相关特征
        df_features['temp_carbon_ratio'] = df_features['铁水温度'] / (df_features['铁水C'] + 1e-6)
        df_features['temp_oxygen_ratio'] = df_features['铁水温度'] / (df_features['oxygen_intensity'] + 1e-6)
        df_features['temp_scrap_interaction'] = df_features['铁水温度'] * df_features['scrap_ratio']

        # 4. 高阶特征（新增）
        df_features['carbon_squared'] = df_features['铁水C'] ** 2
        df_features['silicon_squared'] = df_features['铁水SI'] ** 2
        df_features['oxygen_squared'] = df_features['oxygen_intensity'] ** 2

        # 5. 比率特征（新增）
        df_features['C_to_SI_ratio'] = df_features['铁水C'] / (df_features['铁水SI'] + 1e-6)
        df_features['MN_to_P_ratio'] = df_features['铁水MN'] / (df_features['铁水P'] + 1e-6)
        df_features['lime_to_scrap_ratio'] = df_features['石灰'] / (df_features['废钢'] + 1e-6)

        # 6. 物理约束特征
        for idx, row in df_features.iterrows():
            try:
                hot_metal_temp = row['铁水温度']
                c_content = row['铁水C'] / 100
                si_content = row['铁水SI'] / 100
                scrap_ratio = row['scrap_ratio']

                # 理论温升
                oxidation_heat = c_content * 15 + si_content * 25
                scrap_cooling = scrap_ratio * 50
                theoretical_temp_rise = oxidation_heat - scrap_cooling
                theoretical_temp_rise = np.clip(theoretical_temp_rise, 50, 400)

                df_features.loc[idx, 'theoretical_temp_rise'] = theoretical_temp_rise
                df_features.loc[idx, 'theoretical_end_temp'] = hot_metal_temp + theoretical_temp_rise

            except Exception as e:
                logger.warning(f"计算第{idx}行物理特征时出错: {e}")
                df_features.loc[idx, 'theoretical_temp_rise'] = 100
                df_features.loc[idx, 'theoretical_end_temp'] = row['铁水温度'] + 100

        # 7. 钢种分类特征
        if '钢种' in df_features.columns:
            def classify_steel_grade(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70', 'C72DA', 'SWRCH']):
                    return '高碳钢'
                elif any(x in steel_type for x in ['ER50', 'ML40CR', '40CR', '20CRMO']):
                    return '合金钢'
                elif any(x in steel_type for x in ['304', '316', '321', '430']):
                    return '不锈钢'
                else:
                    return '普通钢'

            df_features['steel_category'] = df_features['钢种'].apply(classify_steel_grade)

            def estimate_carbon_content(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70']):
                    return 'high_carbon'
                elif any(x in steel_type for x in ['20', '16MN']):
                    return 'low_carbon'
                else:
                    return 'medium_carbon'

            df_features['carbon_grade'] = df_features['钢种'].apply(estimate_carbon_content)

        # 填充缺失值
        numeric_cols = df_features.select_dtypes(include=[np.number]).columns
        df_features[numeric_cols] = df_features[numeric_cols].fillna(df_features[numeric_cols].median())

        logger.info("增强特征创建完成")
        return df_features

    def prepare_data_for_models(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List[str]]:
        """为模型准备数据"""
        logger.info("准备模型数据")

        exclude_cols = ['炉号', '出钢重量估算', 'Unnamed: 4', '钢水温度']

        feature_cols = [col for col in df.columns if col not in exclude_cols]
        X = df[feature_cols].copy()

        if '钢水温度' in df.columns:
            y = df['钢水温度'].copy()
        else:
            y = pd.Series([0] * len(df))

        # 识别分类特征
        categorical_features = []
        for col in X.columns:
            if X[col].dtype == 'object' or col in ['steel_category', 'carbon_grade']:
                categorical_features.append(col)

        # 处理分类特征
        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                X[col] = self.label_encoders[col].fit_transform(X[col].astype(str))
            else:
                try:
                    X[col] = self.label_encoders[col].transform(X[col].astype(str))
                except ValueError:
                    mode_value = X[col].mode()[0] if not X[col].mode().empty else 0
                    X[col] = X[col].apply(lambda x: self.label_encoders[col].transform([str(x)])[0]
                                         if str(x) in self.label_encoders[col].classes_ else mode_value)

        # 最终数据清理
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        categorical_cols = X.select_dtypes(include=['object']).columns

        if len(numeric_cols) > 0:
            X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())

        if len(categorical_cols) > 0:
            for col in categorical_cols:
                X[col] = X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'unknown')

        logger.info(f"数据准备完成：{X.shape[1]}个特征，{len(categorical_features)}个分类特征")
        return X, y, categorical_features

    def quick_hyperparameter_optimization(self, X: pd.DataFrame, y: pd.Series,
                                        categorical_features: List[str]) -> Dict[str, Any]:
        """快速超参数优化"""
        logger.info("开始快速超参数优化")

        if not OPTUNA_AVAILABLE:
            logger.warning("Optuna不可用，使用默认参数")
            return self.train_baseline_models(X, y, categorical_features)

        optimization_results = {}

        # 1. XGBoost快速优化
        def optimize_xgboost_quick(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 200, 1500),
                'max_depth': trial.suggest_int('max_depth', 4, 15),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.2, log=True),
                'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10, log=True),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'gamma': trial.suggest_float('gamma', 0, 5),
                'random_state': 42
            }

            model = xgb.XGBRegressor(**params)

            # 使用3折交叉验证
            tscv = TimeSeriesSplit(n_splits=3)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("快速优化XGBoost超参数...")
        study_xgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=10),
            pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
        study_xgb.optimize(optimize_xgboost_quick, n_trials=50, timeout=1800)  # 30分钟

        optimization_results['XGBoost_Quick'] = {
            'best_params': study_xgb.best_params,
            'best_score': study_xgb.best_value,
            'study': study_xgb
        }

        # 2. LightGBM快速优化
        def optimize_lightgbm_quick(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 200, 1500),
                'max_depth': trial.suggest_int('max_depth', 4, 15),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.2, log=True),
                'subsample': trial.suggest_float('subsample', 0.7, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.7, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10, log=True),
                'min_child_samples': trial.suggest_int('min_child_samples', 10, 100),
                'num_leaves': trial.suggest_int('num_leaves', 20, 200),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.7, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.7, 1.0),
                'random_state': 42,
                'verbose': -1
            }

            model = lgb.LGBMRegressor(**params)

            tscv = TimeSeriesSplit(n_splits=3)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("快速优化LightGBM超参数...")
        study_lgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=10),
            pruner=MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
        study_lgb.optimize(optimize_lightgbm_quick, n_trials=50, timeout=1800)

        optimization_results['LightGBM_Quick'] = {
            'best_params': study_lgb.best_params,
            'best_score': study_lgb.best_value,
            'study': study_lgb
        }

        # 3. CatBoost快速优化
        if CATBOOST_AVAILABLE:
            def optimize_catboost_quick(trial):
                params = {
                    'iterations': trial.suggest_int('iterations', 200, 1000),
                    'depth': trial.suggest_int('depth', 4, 10),
                    'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.2, log=True),
                    'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1e-8, 20, log=True),
                    'border_count': trial.suggest_int('border_count', 64, 255),
                    'bagging_temperature': trial.suggest_float('bagging_temperature', 0, 5),
                    'random_strength': trial.suggest_float('random_strength', 0, 5),
                    'random_state': 42,
                    'verbose': False
                }

                cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
                model = cb.CatBoostRegressor(cat_features=cat_features_idx, **params)

                tscv = TimeSeriesSplit(n_splits=3)
                scores = []

                for train_idx, val_idx in tscv.split(X):
                    X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                    y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                    model.fit(X_train_cv, y_train_cv)
                    y_pred_cv = model.predict(X_val_cv)

                    score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                    scores.append(score)

                return np.mean(scores)

            logger.info("快速优化CatBoost超参数...")
            study_cat = optuna.create_study(
                direction='minimize',
                sampler=TPESampler(seed=42, n_startup_trials=8),
                pruner=MedianPruner(n_startup_trials=4, n_warmup_steps=8)
            )
            study_cat.optimize(optimize_catboost_quick, n_trials=40, timeout=1500)

            optimization_results['CatBoost_Quick'] = {
                'best_params': study_cat.best_params,
                'best_score': study_cat.best_value,
                'study': study_cat
            }

        logger.info("快速超参数优化完成")
        return optimization_results

    def train_optimized_models(self, X: pd.DataFrame, y: pd.Series,
                             categorical_features: List[str],
                             optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """使用优化后的超参数训练模型"""
        logger.info("使用优化后的超参数训练模型")

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        models = {}

        # 1. 训练优化后的XGBoost
        if 'XGBoost_Quick' in optimization_results:
            logger.info("训练优化后的XGBoost模型...")
            best_params = optimization_results['XGBoost_Quick']['best_params']

            xgb_model = xgb.XGBRegressor(**best_params)
            xgb_model.fit(X_train, y_train)
            y_pred_xgb = xgb_model.predict(X_test)

            models['XGBoost_Optimized'] = {
                'model': xgb_model,
                'mae': mean_absolute_error(y_test, y_pred_xgb),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_xgb)),
                'r2': r2_score(y_test, y_pred_xgb),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 15),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_xgb
            }

        # 2. 训练优化后的LightGBM
        if 'LightGBM_Quick' in optimization_results:
            logger.info("训练优化后的LightGBM模型...")
            best_params = optimization_results['LightGBM_Quick']['best_params']

            lgb_model = lgb.LGBMRegressor(**best_params)
            lgb_model.fit(X_train, y_train)
            y_pred_lgb = lgb_model.predict(X_test)

            models['LightGBM_Optimized'] = {
                'model': lgb_model,
                'mae': mean_absolute_error(y_test, y_pred_lgb),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_lgb)),
                'r2': r2_score(y_test, y_pred_lgb),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 15),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_lgb
            }

        # 3. 训练优化后的CatBoost
        if 'CatBoost_Quick' in optimization_results and CATBOOST_AVAILABLE:
            logger.info("训练优化后的CatBoost模型...")
            best_params = optimization_results['CatBoost_Quick']['best_params']

            cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
            cat_model = cb.CatBoostRegressor(cat_features=cat_features_idx, **best_params)
            cat_model.fit(X_train, y_train)
            y_pred_cat = cat_model.predict(X_test)

            models['CatBoost_Optimized'] = {
                'model': cat_model,
                'mae': mean_absolute_error(y_test, y_pred_cat),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_cat)),
                'r2': r2_score(y_test, y_pred_cat),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_cat, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_cat, 15),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_cat
            }

        # 4. 创建集成模型
        if len(models) >= 2:
            logger.info("创建集成模型...")

            # 基于性能的权重分配
            weights = {}

            for name, result in models.items():
                accuracy = result['target_accuracy_20']
                mae = result['mae']

                # 综合权重：精度权重70% + MAE权重30%
                weight = (accuracy / 100) * 0.7 + (1 / (mae / 15)) * 0.3
                weights[name] = weight

            # 归一化权重
            total_weight = sum(weights.values())
            for name in weights:
                weights[name] /= total_weight

            # 集成预测
            ensemble_pred = np.zeros(len(y_test))
            for name, result in models.items():
                ensemble_pred += weights[name] * result['y_pred']

            models['Ensemble_Optimized'] = {
                'mae': mean_absolute_error(y_test, ensemble_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, ensemble_pred)),
                'r2': r2_score(y_test, ensemble_pred),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, ensemble_pred, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, ensemble_pred, 15),
                'weights': weights,
                'y_test': y_test,
                'y_pred': ensemble_pred
            }

        self.models = models
        return models

def main():
    """主函数 - 阶段2：快速超参数优化"""
    logger.info("=== 阶段2：快速超参数优化 ===")
    logger.info("目标：快速验证扩大超参数搜索空间的效果")
    logger.info("预期提升：从74.4%提升到78-80%")

    try:
        # 1. 检查环境
        logger.info("=== 环境检查 ===")
        logger.info(f"CatBoost可用: {CATBOOST_AVAILABLE}")
        logger.info(f"Optuna可用: {OPTUNA_AVAILABLE}")

        if not OPTUNA_AVAILABLE:
            logger.error("Optuna不可用，无法进行超参数优化")
            return

        # 2. 数据加载
        logger.info("=== 数据加载 ===")
        train_df = pd.read_excel('1-4521剔除重复20250514.xlsx')

        logger.info(f"训练数据: {train_df.shape}")

        # 3. 创建优化器
        optimizer = Step2QuickOptimizer()

        # 4. 数据清理
        logger.info("=== 数据清理 ===")
        train_cleaned = optimizer.robust_data_cleaning(train_df)

        logger.info(f"训练数据清理后: {train_cleaned.shape}")

        # 5. 增强特征工程
        logger.info("=== 增强特征工程 ===")
        train_features = optimizer.create_enhanced_features(train_cleaned)

        # 6. 准备建模数据
        logger.info("=== 数据准备 ===")
        X_train, y_train, categorical_features = optimizer.prepare_data_for_models(train_features)

        logger.info(f"最终特征数: {X_train.shape[1]}")
        logger.info(f"分类特征数: {len(categorical_features)}")
        logger.info(f"训练样本数: {len(X_train)}")

        # 7. 快速超参数优化
        logger.info("=== 快速超参数优化 ===")
        optimization_results = optimizer.quick_hyperparameter_optimization(X_train, y_train, categorical_features)

        if not optimization_results:
            logger.error("超参数优化失败")
            return

        logger.info(f"成功优化{len(optimization_results)}个模型")

        # 8. 训练优化后的模型
        logger.info("=== 训练优化后的模型 ===")
        model_results = optimizer.train_optimized_models(X_train, y_train, categorical_features, optimization_results)

        if not model_results:
            logger.error("没有成功训练的模型")
            return

        logger.info(f"成功训练{len(model_results)}个模型")

        # 9. 结果评估
        logger.info("=== 结果评估 ===")

        # 显示各模型性能
        logger.info("各模型性能对比:")
        for name, result in model_results.items():
            logger.info(f"  {name}:")
            logger.info(f"    MAE: {result['mae']:.1f}°C")
            logger.info(f"    RMSE: {result['rmse']:.1f}°C")
            logger.info(f"    R²: {result['r2']:.4f}")
            logger.info(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%")
            logger.info(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%")

        # 找出最佳模型
        best_model_name = max(model_results.keys(), key=lambda x: model_results[x]['target_accuracy_20'])
        best_accuracy = model_results[best_model_name]['target_accuracy_20']

        logger.info(f"\n最佳模型: {best_model_name}")
        logger.info(f"最佳精度: {best_accuracy:.1f}%")

        # 10. 生成报告
        logger.info("=== 生成报告 ===")

        report_file = f"step2_quick_opt_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("阶段2：快速超参数优化报告\n")
            f.write("=" * 50 + "\n\n")

            f.write("🎯 目标: 快速验证扩大超参数搜索空间的效果\n")
            f.write("预期提升：从74.4%提升到78-80%\n\n")

            f.write("🔧 优化技术:\n")
            f.write("1. 扩大超参数搜索范围\n")
            f.write("2. 增加新的超参数维度\n")
            f.write("3. 使用对数分布采样\n")
            f.write("4. 多目标优化损失函数\n")
            f.write("5. 时间序列交叉验证\n")
            f.write("6. 增强特征工程\n\n")

            f.write("📊 模型性能:\n")
            for name, result in model_results.items():
                f.write(f"  {name}:\n")
                f.write(f"    MAE: {result['mae']:.1f}°C\n")
                f.write(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%\n")
                f.write(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%\n\n")

            f.write(f"🏆 最佳模型: {best_model_name} ({best_accuracy:.1f}%)\n\n")

            # 计算提升幅度
            baseline_accuracy = 74.4  # 阶段1的最佳精度
            improvement = best_accuracy - baseline_accuracy

            f.write("📈 性能提升分析:\n")
            f.write(f"  阶段1最佳精度: {baseline_accuracy:.1f}%\n")
            f.write(f"  阶段2最佳精度: {best_accuracy:.1f}%\n")
            f.write(f"  绝对提升: +{improvement:.1f}%\n")
            f.write(f"  相对提升: +{improvement/baseline_accuracy*100:.1f}%\n\n")

            f.write("✅ 阶段2完成状态:\n")
            if best_accuracy >= 80:
                f.write("  🎉 超额完成目标！精度达到80%+\n")
                f.write("  ✅ 可以进入阶段3：实施多目标优化\n")
            elif best_accuracy >= 78:
                f.write("  🎯 成功达到目标！精度达到78%+\n")
                f.write("  ✅ 可以进入阶段3：实施多目标优化\n")
            elif improvement >= 2:
                f.write("  ⚡ 有显著提升！\n")
                f.write("  ✅ 可以进入阶段3，继续优化\n")
            else:
                f.write("  ⚠️ 提升有限，建议进一步调优\n")
                f.write("  🔧 可以尝试更多超参数组合\n")

        logger.info(f"报告已保存到: {report_file}")

        # 11. 最终总结
        logger.info("=== 阶段2总结 ===")
        logger.info(f"成功训练{len(model_results)}个优化模型")
        logger.info(f"最佳模型精度: {best_accuracy:.1f}%")
        logger.info(f"相比阶段1提升: +{improvement:.1f}%")

        if best_accuracy >= 80:
            logger.info("🎉🎉🎉 阶段2超额完成！可以进入阶段3！🎉🎉🎉")
        elif best_accuracy >= 78:
            logger.info("🎯 阶段2成功完成！可以进入阶段3！")
        elif improvement >= 2:
            logger.info("⚡ 阶段2有显著提升！可以进入阶段3！")
        else:
            logger.info("🔧 阶段2需要进一步优化")

        return model_results

    except Exception as e:
        logger.error(f"阶段2运行出错: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    main()
