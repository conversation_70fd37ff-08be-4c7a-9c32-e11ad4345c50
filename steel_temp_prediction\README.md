# 钢水温度预测系统

本系统使用机器学习方法预测转炉炼钢过程中的钢水终点温度。

## 系统特点

- **高级特征工程**：基于冶金原理，构建了全面的特征工程流程
- **顺序思维模型**：采用多层次模型结构，结合不同强度的模型进行精确预测
- **完善的评估体系**：提供多种评估指标和可视化方法

## 特征工程模块

### 1. 炉渣特征工程 (Slag Feature Engineering)

- **成分计算**: 
  - 基于铁水/钢水成分变化和辅料加入，计算SiO2, MnO, P2O5, CaO, MgO, Al2O3, FeO, Fe2O3等氧化物质量
  - 炉渣碱度(R, R2)计算，包括修正碱度

- **物理特性**:
  - 基于网络形成体和修饰体比例的粘度模型，考虑温度和液相百分比影响
  - 炉渣硅氧网络连接度指数(Connectivity Index)

- **相平衡特性**:
  - 基于FactSage热力学数据，计算液相百分比、熔点温度区间
  - 预测熔渣-钢水界面状态，界面指数(Interface Index)

- **化学反应特性**:
  - 炉渣磷容量系数和脱磷效率指数
  - 光学碱度(Optical Basicity)计算
  - 炉渣结晶性指数(Crystallinity Index)

### 2. 工艺参数特征 (Process Parameter Features)

- **喷枪动态特征**:
  - 喷射动量指数(Jet Momentum Index)
  - 表面搅拌强度(Surface Stirring Intensity)
  - 表面剪切应力(Surface Shear Stress)
  - 氧气利用率估算(Oxygen Utilization)
  - 冲击深度指数(Impact Depth Index)

- **搅拌特征**:
  - 综合搅拌指数(Combined Stirring Index)
  - 底吹搅拌功率(Bottom Stirring Power)

### 3. 炉渣-工艺交互特征 (Slag-Process Interactions)

- 炉渣碱度与供氧量/喷枪位置的交互
- 炉渣FeO含量与供氧量/喷枪位置的交互
- 炉渣液相百分比与搅拌强度的交互
- 炉渣粘度与搅拌强度的交互
- 炉渣磷容量与供氧利用率的交互
- 炉渣流动性指数(Slag Fluidity Index)
- 热传递效率指数(Heat Transfer Efficiency Index)
- 炉渣修饰反应指数(Slag Modification Reaction Index)

## 模型开发模块

- **基础模型**: XGBoost, LightGBM, RandomForest, Neural Network, Ridge
- **顺序思维模型**: 使用多级模型结构，结合基础模型的预测结果和原始特征
- **超参数优化**: 支持基础模型和元模型的超参数调优
- **评估指标**: MAE, RMSE, R², Hit Rate (±20°C)

## 使用方法

1. 安装依赖:
   ```
   pip install -r requirements.txt
   ```

2. 运行模型:
   ```
   python run_model.py
```

## 数据要求

系统需要以下输入数据:

- 铁水基本参数: 重量、温度、成分(Si, Mn, P, C等)
- 废钢加入量
- 辅料加入量: 石灰、白云石等
- 供氧参数: 供氧量、吹氧时间
- 喷枪参数: 喷枪高度、角度
- 钢水基本参数: 最终成分(可选)

## 文件说明

- `main.py` - 主程序入口
- `config.py` - 配置文件
- `data_preprocessing.py` - 数据预处理
- `feature_engineering.py` - 特征工程
- `model_development.py` - 模型开发
- `model_evaluation.py` - 模型评估
- `utils.py` - 工具函数
- `requirements.txt` - 依赖列表

## 版本更新

### v2.0.0 (2025/05/16)
- 新增基于FactSage的高级炉渣特征
- 强化枪位动态特征，增加氧气利用率模型
- 添加炉渣-工艺参数交互特征
- 升级模型超参数调优功能
