"""
查看预测结果
"""

import pandas as pd

# 加载预测结果
results = pd.read_excel('batch5_predictions_fixed.xlsx')

# 显示前10行
print("前10行预测结果:")
print(results.head(10))

# 显示统计信息
print("\n预测温度统计信息:")
print(results['综合预测温度'].describe())

# 按钢种分组统计
print("\n按钢种分组的预测温度统计:")
steel_stats = results.groupby('钢种')['综合预测温度'].agg(['count', 'mean', 'std', 'min', 'max'])
print(steel_stats)

# 各模型预测温度的比较
model_cols = [col for col in results.columns if col.endswith('_预测温度')]
print("\n各模型预测温度的比较:")
model_stats = results[model_cols].describe().T
model_stats['与综合预测的平均差异'] = [(results[col] - results['综合预测温度']).abs().mean() for col in model_cols]
print(model_stats[['mean', 'std', '与综合预测的平均差异']])

# 保存详细结果到CSV文件
results.to_csv('batch5_detailed_results.csv', index=False, encoding='utf-8-sig')
print("\n详细结果已保存到 batch5_detailed_results.csv")
