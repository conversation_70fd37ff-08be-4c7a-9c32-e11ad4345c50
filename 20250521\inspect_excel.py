import pandas as pd

excel_file_path = 'e:\\专业提升命中率20250516\\4523-4905筛选后完整数据20250519（第五批测试数据） .xlsx'

try:
    # 尝试读取Excel文件的第一个sheet
    df = pd.read_excel(excel_file_path, sheet_name=0)
    print(f"Successfully read Excel file: {excel_file_path}")
    print("\nFirst 5 rows of the dataframe:")
    print(df.head())
    print("\nDataFrame Info:")
    df.info()
    print("\nDescriptive statistics:")
    print(df.describe(include='all'))

    # 检查是否有多个sheet，并打印它们的名称
    xls = pd.ExcelFile(excel_file_path)
    sheet_names = xls.sheet_names
    print(f"\nSheet names in the Excel file: {sheet_names}")
    if len(sheet_names) > 1:
        print("Note: The script currently only processes the first sheet.")
        print("If other sheets are relevant, please specify.")

except FileNotFoundError:
    print(f"Error: The file {excel_file_path} was not found.")
except Exception as e:
    print(f"An error occurred while reading the Excel file: {e}")