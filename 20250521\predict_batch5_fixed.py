"""
预测第五批测试数据的钢水温度（修复版）
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any
import joblib
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Ridge, Lasso
from sklearn.svm import SVR
import xgboost as xgb
import lightgbm as lgb

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch5_prediction_fixed.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_batch5_data(file_path: str) -> pd.DataFrame:
    """
    加载第五批测试数据

    Args:
        file_path: 数据文件路径

    Returns:
        加载的数据DataFrame
    """
    logger.info(f"加载第五批测试数据: {file_path}")
    try:
        data = pd.read_excel(file_path)
        logger.info(f"数据加载成功，形状: {data.shape}")
        return data
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        raise

def create_directory(directory: str) -> None:
    """创建目录（如果不存在）"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")

def train_simple_models(train_data_path: str) -> Dict[str, Any]:
    """
    训练简单模型用于预测

    Args:
        train_data_path: 训练数据路径

    Returns:
        训练好的模型字典
    """
    logger.info(f"从 {train_data_path} 加载训练数据")

    # 加载训练数据
    try:
        train_data = pd.read_excel(train_data_path)
        logger.info(f"训练数据加载成功，形状: {train_data.shape}")
    except Exception as e:
        logger.error(f"训练数据加载失败: {e}")
        raise

    # 准备特征和目标变量
    X_train = train_data.drop(['钢水温度'], axis=1) if '钢水温度' in train_data.columns else train_data
    y_train = train_data['钢水温度'] if '钢水温度' in train_data.columns else None

    if y_train is None:
        logger.error("训练数据中没有目标变量 '钢水温度'")
        raise ValueError("训练数据中没有目标变量 '钢水温度'")

    # 只保留数值列
    X_train = X_train.select_dtypes(include=['float64', 'int64'])

    # 清理特征名称，移除特殊字符
    X_train.columns = [col.replace(' ', '_').replace('(', '').replace(')', '').replace('[', '').replace(']', '').replace(',', '') for col in X_train.columns]

    # 处理缺失值
    logger.info("处理训练数据中的缺失值")
    # 检查缺失值
    missing_values = X_train.isnull().sum()
    if missing_values.sum() > 0:
        logger.info(f"训练数据中有 {missing_values.sum()} 个缺失值")
        # 使用中位数填充缺失值
        X_train = X_train.fillna(X_train.median())
        logger.info("已使用中位数填充缺失值")

    # 检查无穷值
    inf_values = np.isinf(X_train).sum().sum()
    if inf_values > 0:
        logger.info(f"训练数据中有 {inf_values} 个无穷值")
        # 将无穷值替换为NaN，然后用中位数填充
        X_train = X_train.replace([np.inf, -np.inf], np.nan)
        X_train = X_train.fillna(X_train.median())
        logger.info("已处理无穷值")

    # 训练简单模型
    models = {}

    # 随机森林
    logger.info("训练随机森林模型")
    rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
    rf_model.fit(X_train, y_train)
    models['Random Forest'] = rf_model

    # XGBoost
    logger.info("训练XGBoost模型")
    xgb_model = xgb.XGBRegressor(n_estimators=100, random_state=42)
    xgb_model.fit(X_train, y_train)
    models['XGBoost'] = xgb_model

    # LightGBM - 跳过，因为它对特征名称有特殊要求
    logger.info("跳过LightGBM模型训练，因为它对特征名称有特殊要求")

    # Ridge
    logger.info("训练Ridge模型")
    ridge_model = Ridge(alpha=1.0)
    ridge_model.fit(X_train, y_train)
    models['Ridge'] = ridge_model

    # Lasso
    logger.info("训练Lasso模型")
    lasso_model = Lasso(alpha=0.1)
    lasso_model.fit(X_train, y_train)
    models['Lasso'] = lasso_model

    # SVR
    logger.info("训练SVR模型")
    svr_model = SVR(kernel='rbf', C=100, gamma=0.1)
    svr_model.fit(X_train, y_train)
    models['SVR'] = svr_model

    logger.info(f"共训练 {len(models)} 个模型")
    return models, X_train.columns

def create_basic_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    创建基础特征

    Args:
        df: 输入DataFrame

    Returns:
        添加了基础特征的DataFrame
    """
    logger.info("创建基础特征...")

    # 复制DataFrame以避免修改原始数据
    df_features = df.copy()

    # 计算铁水废钢比
    if '铁水' in df_features.columns and '废钢' in df_features.columns:
        df_features['铁水废钢比'] = df_features['铁水'] / df_features['废钢']
        logger.info("创建特征: 铁水废钢比")

    # 计算碱度（CaO+MgO）/（SiO2+P2O5）
    if '石灰' in df_features.columns and '白云石' in df_features.columns and '铁水SI' in df_features.columns and '铁水P' in df_features.columns:
        df_features['碱度估算'] = (df_features['石灰'] * 0.9 + df_features['白云石'] * 0.58) / (df_features['铁水SI'] * 2.14 + df_features['铁水P'] * 2.29)
        logger.info("创建特征: 碱度估算")

    # 计算总处理时间
    if '间隔时间min' in df_features.columns and '吹氧时间s' in df_features.columns:
        df_features['总处理时间min'] = df_features['间隔时间min'] + (df_features['吹氧时间s'] / 60)
        logger.info("创建特征: 总处理时间min")

    # 计算铁水温度与铁水SI的交互特征
    if '铁水温度' in df_features.columns and '铁水SI' in df_features.columns:
        df_features['铁水温度_铁水SI'] = df_features['铁水温度'] * df_features['铁水SI']
        logger.info("创建特征: 铁水温度_铁水SI")

    # 计算铁水废钢比与铁水温度的交互特征
    if '铁水废钢比' in df_features.columns and '铁水温度' in df_features.columns:
        df_features['铁水废钢比_铁水温度'] = df_features['铁水废钢比'] * df_features['铁水温度']
        logger.info("创建特征: 铁水废钢比_铁水温度")

    return df_features

def predict_batch5(batch5_data: pd.DataFrame, models: Dict[str, Any], train_columns: List[str]) -> pd.DataFrame:
    """
    对第五批数据进行预测

    Args:
        batch5_data: 第五批测试数据
        models: 模型字典
        train_columns: 训练数据的列名

    Returns:
        包含预测结果的DataFrame
    """
    logger.info("开始预处理第五批数据")

    # 预处理数据
    batch5_processed = batch5_data.copy()

    # 创建基础特征
    batch5_processed = create_basic_features(batch5_processed)

    # 准备预测特征
    logger.info("准备预测特征")
    try:
        # 只保留数值列
        numeric_cols = batch5_processed.select_dtypes(include=['float64', 'int64']).columns
        X_batch5 = batch5_processed[numeric_cols]

        # 清理特征名称，移除特殊字符
        X_batch5.columns = [col.replace(' ', '_').replace('(', '').replace(')', '').replace('[', '').replace(']', '').replace(',', '') for col in X_batch5.columns]

        # 处理缺失值
        logger.info("处理测试数据中的缺失值")
        # 检查缺失值
        missing_values = X_batch5.isnull().sum()
        if missing_values.sum() > 0:
            logger.info(f"测试数据中有 {missing_values.sum()} 个缺失值")
            # 使用中位数填充缺失值
            X_batch5 = X_batch5.fillna(X_batch5.median())
            logger.info("已使用中位数填充缺失值")

        # 检查无穷值
        inf_values = np.isinf(X_batch5).sum().sum()
        if inf_values > 0:
            logger.info(f"测试数据中有 {inf_values} 个无穷值")
            # 将无穷值替换为NaN，然后用中位数填充
            X_batch5 = X_batch5.replace([np.inf, -np.inf], np.nan)
            X_batch5 = X_batch5.fillna(X_batch5.median())
            logger.info("已处理无穷值")

        # 确保特征与训练数据匹配
        missing_cols = set(train_columns) - set(X_batch5.columns)
        for col in missing_cols:
            X_batch5[col] = 0  # 用0填充缺失的列
            logger.warning(f"填充缺失列: {col}")

        # 只保留训练数据中的列
        X_batch5 = X_batch5[train_columns]

        # 保存原始数据，用于结果展示
        original_data = batch5_data.copy()

        logger.info(f"预测特征准备完成，特征数量: {X_batch5.shape[1]}")
    except Exception as e:
        logger.error(f"准备预测特征失败: {e}")
        raise

    # 使用各个模型进行预测
    logger.info("开始使用各模型进行预测")
    predictions = {}

    for name, model in models.items():
        try:
            pred = model.predict(X_batch5)
            predictions[name] = pred
            logger.info(f"{name} 模型预测完成，预测范围: {np.min(pred):.2f}°C - {np.max(pred):.2f}°C")
        except Exception as e:
            logger.error(f"{name} 模型预测失败: {e}")

    # 创建结果DataFrame
    logger.info("整合预测结果")
    results = pd.DataFrame()

    # 添加原始数据的关键列
    if '炉号' in original_data.columns:
        results['炉号'] = original_data['炉号']
    if '钢种' in original_data.columns:
        results['钢种'] = original_data['钢种']
    if '铁水温度' in original_data.columns:
        results['铁水温度'] = original_data['铁水温度']

    # 添加各模型的预测结果
    for name, pred in predictions.items():
        results[f'{name}_预测温度'] = pred

    # 计算综合预测温度（所有模型的平均值）
    results['综合预测温度'] = np.mean([pred for pred in predictions.values()], axis=0)

    logger.info("预测完成")
    return results

def save_prediction_results(results: pd.DataFrame, output_path: str = "batch5_predictions_fixed.xlsx") -> None:
    """
    保存预测结果

    Args:
        results: 预测结果DataFrame
        output_path: 输出文件路径
    """
    logger.info(f"保存预测结果到: {output_path}")
    try:
        results.to_excel(output_path, index=False)
        logger.info("预测结果保存成功")
    except Exception as e:
        logger.error(f"保存预测结果失败: {e}")
        raise

def analyze_prediction_results(results: pd.DataFrame) -> None:
    """
    分析预测结果

    Args:
        results: 预测结果DataFrame
    """
    logger.info("分析预测结果")

    # 计算综合预测温度的统计信息
    mean_temp = results['综合预测温度'].mean()
    std_temp = results['综合预测温度'].std()
    min_temp = results['综合预测温度'].min()
    max_temp = results['综合预测温度'].max()

    logger.info(f"综合预测温度统计:")
    logger.info(f"  平均值: {mean_temp:.2f}°C")
    logger.info(f"  标准差: {std_temp:.2f}°C")
    logger.info(f"  最小值: {min_temp:.2f}°C")
    logger.info(f"  最大值: {max_temp:.2f}°C")

    # 如果有钢种信息，按钢种分析
    if '钢种' in results.columns:
        logger.info("按钢种分析预测温度:")
        for steel_type, group in results.groupby('钢种'):
            logger.info(f"  钢种 {steel_type}:")
            logger.info(f"    样本数: {len(group)}")
            logger.info(f"    平均预测温度: {group['综合预测温度'].mean():.2f}°C")
            logger.info(f"    标准差: {group['综合预测温度'].std():.2f}°C")

    # 创建预测温度分布图
    plt.figure(figsize=(10, 6))
    plt.hist(results['综合预测温度'], bins=20, alpha=0.7)
    plt.axvline(mean_temp, color='r', linestyle='--', label=f'平均值: {mean_temp:.2f}°C')
    plt.title('第五批数据预测温度分布')
    plt.xlabel('预测温度 (°C)')
    plt.ylabel('频次')
    plt.legend()
    plt.grid(alpha=0.3)
    plt.savefig('batch5_temperature_distribution.png', dpi=300, bbox_inches='tight')
    logger.info("预测温度分布图已保存")

    # 如果有铁水温度，分析铁水温度与预测温度的关系
    if '铁水温度' in results.columns:
        plt.figure(figsize=(10, 6))
        plt.scatter(results['铁水温度'], results['综合预测温度'], alpha=0.5)
        plt.title('铁水温度与预测出钢温度关系')
        plt.xlabel('铁水温度 (°C)')
        plt.ylabel('预测出钢温度 (°C)')
        plt.grid(alpha=0.3)
        plt.savefig('batch5_hotmetal_vs_prediction.png', dpi=300, bbox_inches='tight')
        logger.info("铁水温度与预测温度关系图已保存")

    # 比较不同模型的预测结果
    model_columns = [col for col in results.columns if col.endswith('_预测温度')]
    if len(model_columns) > 1:
        logger.info("比较不同模型的预测结果:")
        for col in model_columns:
            model_name = col.replace('_预测温度', '')
            logger.info(f"  {model_name}:")
            logger.info(f"    平均值: {results[col].mean():.2f}°C")
            logger.info(f"    标准差: {results[col].std():.2f}°C")
            logger.info(f"    与综合预测的平均差异: {(results[col] - results['综合预测温度']).abs().mean():.2f}°C")

def main():
    """主函数"""
    logger.info("开始处理第五批测试数据")

    # 第五批数据文件路径
    batch5_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"

    # 训练数据文件路径
    train_file = "1-4521剔除重复20250514.xlsx"

    # 检查文件是否存在
    if not os.path.exists(batch5_file):
        logger.error(f"文件不存在: {batch5_file}")
        print(f"错误: 找不到文件 {batch5_file}")
        return

    if not os.path.exists(train_file):
        logger.error(f"文件不存在: {train_file}")
        print(f"错误: 找不到文件 {train_file}")
        return

    # 创建结果目录
    create_directory("results")

    # 加载第五批数据
    batch5_data = load_batch5_data(batch5_file)

    # 训练简单模型
    models, train_columns = train_simple_models(train_file)

    # 预测第五批数据
    results = predict_batch5(batch5_data, models, train_columns)

    # 保存预测结果
    save_prediction_results(results)

    # 分析预测结果
    analyze_prediction_results(results)

    logger.info("第五批数据处理完成")
    print("第五批数据预测完成，结果已保存到 batch5_predictions_fixed.xlsx")

if __name__ == "__main__":
    main()
