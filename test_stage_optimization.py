"""
测试阶段性优化模型的简化版本
"""

import pandas as pd
import numpy as np
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_data_loading():
    """测试数据加载"""
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        logger.info(f"成功读取数据，共{len(df)}条记录，{df.shape[1]}个特征")
        logger.info(f"列名: {list(df.columns)}")
        
        # 检查数据类型
        logger.info("数据类型:")
        for col in df.columns:
            logger.info(f"{col}: {df[col].dtype}")
        
        return df
    except Exception as e:
        logger.error(f"读取数据失败：{e}")
        return None

def test_basic_features(df):
    """测试基础特征创建"""
    try:
        logger.info("开始测试基础特征创建")
        
        # 造渣料配比特征
        slag_materials = ['石灰', '白云石', '破碎料', '烧结返矿', '石灰石']
        available_materials = [col for col in slag_materials if col in df.columns]
        
        logger.info(f"可用造渣料列: {available_materials}")
        
        if available_materials:
            # 检查数据类型
            for material in available_materials:
                logger.info(f"{material} 数据类型: {df[material].dtype}")
                logger.info(f"{material} 前5个值: {df[material].head().tolist()}")
                
                # 转换为数值类型
                df[material] = pd.to_numeric(df[material], errors='coerce').fillna(0)
                logger.info(f"{material} 转换后数据类型: {df[material].dtype}")
            
            # 计算总造渣料
            df['总造渣料'] = df[available_materials].sum(axis=1)
            logger.info(f"总造渣料计算成功，范围: {df['总造渣料'].min()} - {df['总造渣料'].max()}")
        
        return df
        
    except Exception as e:
        logger.error(f"基础特征创建失败: {e}")
        return None

def main():
    """主函数"""
    logger.info("=== 开始测试阶段性优化模型 ===")
    
    # 测试数据加载
    df = test_data_loading()
    if df is None:
        return
    
    # 测试基础特征创建
    df_features = test_basic_features(df)
    if df_features is None:
        return
    
    logger.info("=== 测试完成 ===")

if __name__ == "__main__":
    main()
