"""
加载89.3%精度模型对第五批测试数据进行钢水温度预测
并生成详细分析报告

模型：improved_classification_system_20250529_101513.pkl
测试数据：4523-4905筛选后完整数据20250519（第五批测试数据）
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime
import joblib
import pickle

# 核心机器学习库
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler

# 导入原始类定义
try:
    from improved_classification_system import ImprovedClassificationSystem
except ImportError:
    # 如果导入失败，创建一个简化的类定义
    class ImprovedClassificationSystem:
        def __init__(self):
            self.baseline_accuracy = 82.6
            self.target_accuracy = 95.0
            self.target_range = (1590, 1670)
            self.target_tolerance = 20

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"model_prediction_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ModelPredictionAnalysis:
    """模型预测分析系统"""

    def __init__(self, model_path: str):
        self.model_path = model_path
        self.model_data = None
        self.optimizer = None
        self.base_models = None
        self.specialized_results = None
        self.selected_features = None
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

    def load_model(self) -> bool:
        """加载训练好的模型"""
        logger.info(f"加载模型: {self.model_path}")

        try:
            self.model_data = joblib.load(self.model_path)
            self.optimizer = self.model_data['optimizer']
            self.base_models = self.model_data['base_models']
            self.specialized_results = self.model_data['specialized_results']
            self.selected_features = self.model_data['selected_features']

            logger.info("模型加载成功")
            logger.info(f"选择特征数: {len(self.selected_features)}")
            logger.info(f"基础模型数: {len(self.base_models)}")
            logger.info(f"钢水温度专门化模型数: {len(self.specialized_results.get('steel_temp_models', {}))}")
            logger.info(f"成分专门化模型数: {len(self.specialized_results.get('composition_models', {}))}")

            return True

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def preprocess_test_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """预处理测试数据（与训练时保持一致）"""
        logger.info("开始预处理测试数据")

        df_processed = df.copy()

        # 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').replace('°', '').strip()
                return float(value)
            except:
                return default

        # 处理数值列 - 适应测试数据的列名
        numeric_columns = ['铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s']

        # 检查并添加存在的列
        possible_columns = {
            '铁水温度': ['铁水温度', '铁水温度度'],
            '铁水C': ['铁水C'],
            '铁水SI': ['铁水SI'],
            '铁水MN': ['铁水MN'],
            '铁水P': ['铁水P'],
            '铁水S': ['铁水S'],
            '最大角度': ['最大角度'],
            '气体流速': ['气体流速', '平均流速M3/h', '平均 流速M3/h']
        }

        for standard_name, possible_names in possible_columns.items():
            for name in possible_names:
                if name in df_processed.columns:
                    numeric_columns.append(name)
                    # 如果列名不标准，重命名为标准名
                    if name != standard_name:
                        df_processed[standard_name] = df_processed[name]
                    break

        for col in numeric_columns:
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].apply(safe_convert)

        # 移除无穷大值
        df_processed = df_processed.replace([np.inf, -np.inf], np.nan)

        # 约束范围
        constraints = {
            '铁水温度': (1300, 1460),
            '铁水C': (3.6, 4.9),
            '铁水SI': (0.2, 1.0),
            '铁水MN': (0.1, 0.7),
            '铁水P': (0.08, 0.22),
            '铁水': (70, 110),
            '废钢': (5, 40),
            '累氧实际': (4000, 6000),
            '吹氧时间s': (400, 1000),
            '最大角度': (0, 360),      # 炉子转动角度
            '气体流速': (0.5, 15.0),   # 烟气流速
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].clip(min_val, max_val)

        # 目标变量处理
        if '钢水温度' in df_processed.columns:
            df_processed['钢水温度'] = df_processed['钢水温度'].apply(safe_convert)
            # 不过滤测试数据，保留所有样本进行预测

        logger.info(f"测试数据预处理完成，保留{len(df_processed)}条记录")
        return df_processed

    def feature_engineering_test_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """对测试数据进行特征工程（与训练时保持一致）"""
        logger.info("开始测试数据特征工程")

        df_features = df.copy()

        # === 基础工程特征 ===
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # === 成分交互特征 ===
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['total_impurities'] = df_features['铁水SI'] + df_features['铁水MN'] + df_features['铁水P'] + df_features['铁水S']

        # === 温度相关特征 ===
        df_features['temp_carbon_ratio'] = df_features['铁水温度'] / (df_features['铁水C'] + 1e-6)
        df_features['temp_oxygen_ratio'] = df_features['铁水温度'] / (df_features['oxygen_intensity'] + 1e-6)

        # === 新增特征（正确理解）===
        if '最大角度' in df_features.columns:
            # 炉子转动角度影响混合效果
            df_features['mixing_efficiency'] = np.sin(np.radians(df_features['最大角度'])) * df_features['吹氧时间s']
            df_features['rotation_factor'] = df_features['最大角度'] / 360.0

        if '气体流速' in df_features.columns:
            # 烟气流速影响热损失
            df_features['heat_loss_index'] = df_features['气体流速'] * df_features['吹氧时间s'] / 60
            df_features['gas_efficiency'] = 1 / (df_features['气体流速'] + 1e-6)

        # === 高阶特征 ===
        df_features['carbon_burn_potential'] = df_features['铁水C'] * df_features['oxygen_intensity']
        df_features['heat_balance_factor'] = df_features['铁水温度'] * df_features['铁水'] / (df_features['废钢'] + 1e-6)

        logger.info("测试数据特征工程完成")
        return df_features

    def create_test_classification(self, df: pd.DataFrame) -> pd.DataFrame:
        """为测试数据创建分类标签"""
        logger.info("开始为测试数据创建分类标签")

        df_classified = df.copy()

        # 钢水温度分类阈值（与训练时保持一致）
        steel_temp_thresholds = [1600, 1620, 1640, 1660]

        # 检查是否有钢水温度列，如果没有则使用铁水温度估算
        if '钢水温度' not in df_classified.columns:
            logger.warning("测试数据中没有钢水温度列，将基于铁水温度估算钢水温度")
            if '铁水温度' in df_classified.columns:
                # 简单估算：钢水温度 ≈ 铁水温度 + 150°C（基于冶金经验）
                df_classified['钢水温度'] = df_classified['铁水温度'] + 150
                logger.info("已基于铁水温度估算钢水温度")
            else:
                # 如果连铁水温度都没有，使用默认值
                logger.warning("测试数据中也没有铁水温度列，使用默认钢水温度1620°C")
                df_classified['钢水温度'] = 1620

        # 基于钢水温度目标范围的精细分类
        def classify_steel_temperature(temp):
            if pd.isna(temp):
                return 'mid_target'  # 默认分类
            if temp < steel_temp_thresholds[0]:
                return 'below_target'     # 低于目标范围
            elif temp < steel_temp_thresholds[1]:
                return 'low_target'       # 目标范围低端
            elif temp < steel_temp_thresholds[2]:
                return 'mid_target'       # 目标范围中端
            elif temp < steel_temp_thresholds[3]:
                return 'high_target'      # 目标范围高端
            else:
                return 'above_target'     # 高于目标范围

        df_classified['steel_temp_class'] = df_classified['钢水温度'].apply(classify_steel_temperature)

        # 基于铁水成分的分类
        def classify_composition(row):
            c_level = 'high' if row.get('铁水C', 4.2) > 4.2 else 'low'
            si_level = 'high' if row.get('铁水SI', 0.6) > 0.6 else 'low'
            return f"{c_level}_c_{si_level}_si"

        df_classified['composition_class'] = df_classified.apply(classify_composition, axis=1)

        # 统计各类别数量
        steel_counts = df_classified['steel_temp_class'].value_counts()
        comp_counts = df_classified['composition_class'].value_counts()

        logger.info(f"测试数据钢水温度分类统计: {steel_counts.to_dict()}")
        logger.info(f"测试数据成分分类统计: {comp_counts.to_dict()}")

        return df_classified

    def intelligent_ensemble_prediction(self, X_test: pd.DataFrame, df_test_classified: pd.DataFrame) -> np.ndarray:
        """智能集成预测（与训练时保持一致）"""
        logger.info("开始智能集成预测")

        predictions = np.zeros(len(X_test))
        prediction_weights = np.zeros(len(X_test))

        # 1. 基础模型预测
        base_predictions = {}
        for model_name, model in self.base_models.items():
            try:
                pred = model.predict(X_test)
                base_predictions[model_name] = pred
                logger.info(f"基础模型 {model_name} 预测完成")
            except Exception as e:
                logger.warning(f"基础模型 {model_name} 预测失败: {e}")

        # 基础模型集成（权重基于历史性能）
        base_weights = {'xgboost': 0.4, 'lightgbm': 0.35, 'random_forest': 0.25}
        base_ensemble = np.zeros(len(X_test))

        for model_name, pred in base_predictions.items():
            weight = base_weights.get(model_name, 0.1)
            base_ensemble += weight * pred

        predictions += 0.5 * base_ensemble  # 基础模型权重50%
        prediction_weights += 0.5

        # 2. 钢水温度专门化模型预测
        steel_temp_models = self.specialized_results.get('steel_temp_models', {})
        for i, steel_class in enumerate(df_test_classified['steel_temp_class']):
            if steel_class in steel_temp_models:
                try:
                    class_models = steel_temp_models[steel_class]
                    class_predictions = []

                    for model_name, model in class_models.items():
                        pred = model.predict(X_test.iloc[i:i+1])[0]
                        class_predictions.append(pred)

                    if class_predictions:
                        class_pred = np.mean(class_predictions)
                        predictions[i] += 0.3 * class_pred  # 钢水温度专门化权重30%
                        prediction_weights[i] += 0.3

                except Exception as e:
                    logger.warning(f"钢水温度专门化模型 {steel_class} 预测失败: {e}")

        # 3. 成分专门化模型预测
        composition_models = self.specialized_results.get('composition_models', {})
        for i, comp_class in enumerate(df_test_classified['composition_class']):
            if comp_class in composition_models:
                try:
                    class_models = composition_models[comp_class]
                    class_predictions = []

                    for model_name, model in class_models.items():
                        pred = model.predict(X_test.iloc[i:i+1])[0]
                        class_predictions.append(pred)

                    if class_predictions:
                        class_pred = np.mean(class_predictions)
                        predictions[i] += 0.2 * class_pred  # 成分专门化权重20%
                        prediction_weights[i] += 0.2

                except Exception as e:
                    logger.warning(f"成分专门化模型 {comp_class} 预测失败: {e}")

        # 4. 权重归一化
        valid_mask = prediction_weights > 0
        predictions[valid_mask] = predictions[valid_mask] / prediction_weights[valid_mask]

        # 5. 对于没有有效预测的样本，使用基础集成
        invalid_mask = prediction_weights == 0
        if invalid_mask.sum() > 0:
            predictions[invalid_mask] = base_ensemble[invalid_mask]
            logger.warning(f"有{invalid_mask.sum()}个样本使用基础集成预测")

        # 6. 后处理：确保预测值在合理范围内
        predictions = np.clip(predictions, 1540, 1700)

        logger.info("智能集成预测完成")
        return predictions

    def comprehensive_analysis(self, y_true: np.ndarray, y_pred: np.ndarray,
                             df_test: pd.DataFrame) -> Dict[str, Any]:
        """全面的预测分析"""
        logger.info("开始全面预测分析")

        analysis_results = {}

        # 1. 基础性能指标
        mae = mean_absolute_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)

        # 计算不同容差的精度
        accuracy_20 = self.calculate_target_accuracy(y_true, y_pred, tolerance=20)
        accuracy_15 = self.calculate_target_accuracy(y_true, y_pred, tolerance=15)
        accuracy_10 = self.calculate_target_accuracy(y_true, y_pred, tolerance=10)
        accuracy_5 = self.calculate_target_accuracy(y_true, y_pred, tolerance=5)

        analysis_results['basic_metrics'] = {
            'mae': mae,
            'rmse': rmse,
            'r2': r2,
            'accuracy_20': accuracy_20,
            'accuracy_15': accuracy_15,
            'accuracy_10': accuracy_10,
            'accuracy_5': accuracy_5
        }

        # 2. 目标范围分析
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])
        target_samples = target_mask.sum()
        total_samples = len(y_true)
        target_ratio = target_samples / total_samples * 100

        if target_samples > 0:
            target_y_true = y_true[target_mask]
            target_y_pred = y_pred[target_mask]

            target_mae = mean_absolute_error(target_y_true, target_y_pred)
            target_rmse = np.sqrt(mean_squared_error(target_y_true, target_y_pred))
            target_r2 = r2_score(target_y_true, target_y_pred)
        else:
            target_mae = target_rmse = target_r2 = 0

        analysis_results['target_range_analysis'] = {
            'target_samples': target_samples,
            'total_samples': total_samples,
            'target_ratio': target_ratio,
            'target_mae': target_mae,
            'target_rmse': target_rmse,
            'target_r2': target_r2
        }

        # 3. 误差分布分析
        errors = y_pred - y_true
        abs_errors = np.abs(errors)

        error_stats = {
            'mean_error': np.mean(errors),
            'std_error': np.std(errors),
            'mean_abs_error': np.mean(abs_errors),
            'median_abs_error': np.median(abs_errors),
            'max_abs_error': np.max(abs_errors),
            'error_percentiles': {
                '25%': np.percentile(abs_errors, 25),
                '50%': np.percentile(abs_errors, 50),
                '75%': np.percentile(abs_errors, 75),
                '90%': np.percentile(abs_errors, 90),
                '95%': np.percentile(abs_errors, 95)
            }
        }

        analysis_results['error_distribution'] = error_stats

        # 4. 温度区间分析
        temp_ranges = [
            (1540, 1580, '低温区'),
            (1580, 1620, '中低温区'),
            (1620, 1660, '中高温区'),
            (1660, 1700, '高温区')
        ]

        range_analysis = {}
        for min_temp, max_temp, range_name in temp_ranges:
            range_mask = (y_true >= min_temp) & (y_true < max_temp)
            if range_mask.sum() > 0:
                range_y_true = y_true[range_mask]
                range_y_pred = y_pred[range_mask]

                range_mae = mean_absolute_error(range_y_true, range_y_pred)
                range_accuracy = np.mean(np.abs(range_y_true - range_y_pred) <= 20) * 100

                range_analysis[range_name] = {
                    'samples': range_mask.sum(),
                    'mae': range_mae,
                    'accuracy': range_accuracy
                }

        analysis_results['temperature_range_analysis'] = range_analysis

        # 5. 工艺参数影响分析
        process_analysis = {}

        # 铁水温度影响
        hotmetal_temp_ranges = [
            (1300, 1350, '低铁水温度'),
            (1350, 1400, '中铁水温度'),
            (1400, 1460, '高铁水温度')
        ]

        for min_temp, max_temp, range_name in hotmetal_temp_ranges:
            range_mask = (df_test['铁水温度'] >= min_temp) & (df_test['铁水温度'] < max_temp)
            if range_mask.sum() > 0:
                range_y_true = y_true[range_mask]
                range_y_pred = y_pred[range_mask]

                range_mae = mean_absolute_error(range_y_true, range_y_pred)
                range_accuracy = np.mean(np.abs(range_y_true - range_y_pred) <= 20) * 100

                process_analysis[range_name] = {
                    'samples': range_mask.sum(),
                    'mae': range_mae,
                    'accuracy': range_accuracy
                }

        analysis_results['process_parameter_analysis'] = process_analysis

        logger.info("全面预测分析完成")
        return analysis_results

    def generate_visualization(self, y_true: np.ndarray, y_pred: np.ndarray,
                             analysis_results: Dict, save_path: str):
        """生成可视化图表"""
        logger.info("开始生成可视化图表")

        # 设置图表样式
        plt.style.use('default')
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('89.3%精度模型第五批测试数据预测分析', fontsize=16, fontweight='bold')

        # 1. 预测值vs真实值散点图
        ax1 = axes[0, 0]
        ax1.scatter(y_true, y_pred, alpha=0.6, s=30)
        ax1.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        ax1.set_xlabel('真实钢水温度 (°C)')
        ax1.set_ylabel('预测钢水温度 (°C)')
        ax1.set_title('预测值 vs 真实值')
        ax1.grid(True, alpha=0.3)

        # 添加性能指标文本
        mae = analysis_results['basic_metrics']['mae']
        r2 = analysis_results['basic_metrics']['r2']
        accuracy = analysis_results['basic_metrics']['accuracy_20']
        ax1.text(0.05, 0.95, f'MAE: {mae:.2f}°C\nR²: {r2:.3f}\n精度: {accuracy:.1f}%',
                transform=ax1.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 2. 误差分布直方图
        ax2 = axes[0, 1]
        errors = y_pred - y_true
        ax2.hist(errors, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(0, color='red', linestyle='--', linewidth=2)
        ax2.set_xlabel('预测误差 (°C)')
        ax2.set_ylabel('频次')
        ax2.set_title('预测误差分布')
        ax2.grid(True, alpha=0.3)

        # 3. 绝对误差分布
        ax3 = axes[0, 2]
        abs_errors = np.abs(errors)
        ax3.hist(abs_errors, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        ax3.axvline(20, color='red', linestyle='--', linewidth=2, label='±20°C阈值')
        ax3.set_xlabel('绝对误差 (°C)')
        ax3.set_ylabel('频次')
        ax3.set_title('绝对误差分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 不同容差精度对比
        ax4 = axes[1, 0]
        tolerances = [5, 10, 15, 20]
        accuracies = [
            analysis_results['basic_metrics']['accuracy_5'],
            analysis_results['basic_metrics']['accuracy_10'],
            analysis_results['basic_metrics']['accuracy_15'],
            analysis_results['basic_metrics']['accuracy_20']
        ]
        bars = ax4.bar(tolerances, accuracies, color=['red', 'orange', 'yellow', 'green'], alpha=0.7)
        ax4.set_xlabel('容差 (°C)')
        ax4.set_ylabel('精度 (%)')
        ax4.set_title('不同容差下的预测精度')
        ax4.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, acc in zip(bars, accuracies):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{acc:.1f}%', ha='center', va='bottom')

        # 5. 温度区间性能分析
        ax5 = axes[1, 1]
        range_analysis = analysis_results['temperature_range_analysis']
        ranges = list(range_analysis.keys())
        range_accuracies = [range_analysis[r]['accuracy'] for r in ranges]
        range_samples = [range_analysis[r]['samples'] for r in ranges]

        bars = ax5.bar(ranges, range_accuracies, color='lightcoral', alpha=0.7)
        ax5.set_ylabel('精度 (%)')
        ax5.set_title('不同温度区间预测精度')
        ax5.tick_params(axis='x', rotation=45)
        ax5.grid(True, alpha=0.3)

        # 添加样本数标签
        for bar, samples in zip(bars, range_samples):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'n={samples}', ha='center', va='bottom', fontsize=8)

        # 6. 残差图
        ax6 = axes[1, 2]
        ax6.scatter(y_pred, errors, alpha=0.6, s=30)
        ax6.axhline(0, color='red', linestyle='--', linewidth=2)
        ax6.axhline(20, color='orange', linestyle='--', linewidth=1, alpha=0.7)
        ax6.axhline(-20, color='orange', linestyle='--', linewidth=1, alpha=0.7)
        ax6.set_xlabel('预测钢水温度 (°C)')
        ax6.set_ylabel('预测误差 (°C)')
        ax6.set_title('残差分析')
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"可视化图表已保存: {save_path}")

    def generate_detailed_report(self, analysis_results: Dict, df_test: pd.DataFrame,
                               y_true: np.ndarray, y_pred: np.ndarray) -> str:
        """生成详细分析报告"""

        report = []
        report.append("89.3%精度模型第五批测试数据预测分析报告")
        report.append("=" * 80)
        report.append("")

        # 1. 模型信息
        report.append("🤖 模型信息:")
        report.append(f"模型文件: {self.model_path}")
        report.append(f"训练精度: 89.3% (±20°C容差)")
        report.append(f"选择特征数: {len(self.selected_features)}")
        report.append(f"基础模型: XGBoost + LightGBM + RandomForest")
        report.append(f"专门化模型: 钢水温度分类 + 成分分类")
        report.append("")

        # 2. 测试数据概况
        report.append("📊 测试数据概况:")
        report.append(f"测试样本数: {len(df_test)}")
        report.append(f"钢水温度范围: {y_true.min():.1f}°C - {y_true.max():.1f}°C")
        report.append(f"钢水温度均值: {y_true.mean():.1f}°C ± {y_true.std():.1f}°C")

        target_analysis = analysis_results['target_range_analysis']
        report.append(f"目标范围(1590-1670°C)样本: {target_analysis['target_samples']}/{target_analysis['total_samples']} ({target_analysis['target_ratio']:.1f}%)")
        report.append("")

        # 3. 预测性能
        basic_metrics = analysis_results['basic_metrics']
        report.append("🎯 预测性能:")
        report.append(f"  MAE: {basic_metrics['mae']:.2f}°C")
        report.append(f"  RMSE: {basic_metrics['rmse']:.2f}°C")
        report.append(f"  R²: {basic_metrics['r2']:.4f}")
        report.append("")

        report.append("📏 不同容差精度:")
        report.append(f"  ±5°C精度:  {basic_metrics['accuracy_5']:.1f}%")
        report.append(f"  ±10°C精度: {basic_metrics['accuracy_10']:.1f}%")
        report.append(f"  ±15°C精度: {basic_metrics['accuracy_15']:.1f}%")
        report.append(f"  ±20°C精度: {basic_metrics['accuracy_20']:.1f}%")
        report.append("")

        # 4. 目标范围性能
        if target_analysis['target_samples'] > 0:
            report.append("🎯 目标范围(1590-1670°C)性能:")
            report.append(f"  目标范围MAE: {target_analysis['target_mae']:.2f}°C")
            report.append(f"  目标范围RMSE: {target_analysis['target_rmse']:.2f}°C")
            report.append(f"  目标范围R²: {target_analysis['target_r2']:.4f}")
            report.append("")

        # 5. 误差分析
        error_dist = analysis_results['error_distribution']
        report.append("📈 误差分析:")
        report.append(f"  平均误差: {error_dist['mean_error']:.2f}°C")
        report.append(f"  误差标准差: {error_dist['std_error']:.2f}°C")
        report.append(f"  平均绝对误差: {error_dist['mean_abs_error']:.2f}°C")
        report.append(f"  中位绝对误差: {error_dist['median_abs_error']:.2f}°C")
        report.append(f"  最大绝对误差: {error_dist['max_abs_error']:.2f}°C")
        report.append("")

        report.append("📊 误差百分位数:")
        percentiles = error_dist['error_percentiles']
        report.append(f"  25%样本误差 ≤ {percentiles['25%']:.2f}°C")
        report.append(f"  50%样本误差 ≤ {percentiles['50%']:.2f}°C")
        report.append(f"  75%样本误差 ≤ {percentiles['75%']:.2f}°C")
        report.append(f"  90%样本误差 ≤ {percentiles['90%']:.2f}°C")
        report.append(f"  95%样本误差 ≤ {percentiles['95%']:.2f}°C")
        report.append("")

        # 6. 温度区间分析
        range_analysis = analysis_results['temperature_range_analysis']
        report.append("🌡️ 温度区间性能分析:")
        for range_name, metrics in range_analysis.items():
            report.append(f"  {range_name}: MAE={metrics['mae']:.2f}°C, 精度={metrics['accuracy']:.1f}%, 样本数={metrics['samples']}")
        report.append("")

        # 7. 工艺参数影响
        process_analysis = analysis_results['process_parameter_analysis']
        report.append("⚙️ 工艺参数影响分析:")
        for param_name, metrics in process_analysis.items():
            report.append(f"  {param_name}: MAE={metrics['mae']:.2f}°C, 精度={metrics['accuracy']:.1f}%, 样本数={metrics['samples']}")
        report.append("")

        # 8. 模型评估结论
        report.append("📋 模型评估结论:")

        if basic_metrics['accuracy_20'] >= 85:
            report.append("✅ 模型性能优秀: ±20°C精度≥85%")
        elif basic_metrics['accuracy_20'] >= 80:
            report.append("✅ 模型性能良好: ±20°C精度≥80%")
        else:
            report.append("⚠️ 模型性能需要改进: ±20°C精度<80%")

        if basic_metrics['mae'] <= 15:
            report.append("✅ MAE表现优秀: ≤15°C")
        elif basic_metrics['mae'] <= 20:
            report.append("✅ MAE表现良好: ≤20°C")
        else:
            report.append("⚠️ MAE需要改进: >20°C")

        if basic_metrics['r2'] >= 0.5:
            report.append("✅ 模型解释能力良好: R²≥0.5")
        else:
            report.append("⚠️ 模型解释能力有限: R²<0.5")

        report.append("")

        # 9. 建议和改进方向
        report.append("💡 建议和改进方向:")

        if basic_metrics['accuracy_20'] < 90:
            report.append("1. 考虑增加更多训练数据以提高模型泛化能力")

        if error_dist['std_error'] > 15:
            report.append("2. 误差波动较大，建议优化特征工程或模型集成策略")

        if target_analysis['target_ratio'] < 80:
            report.append("3. 目标范围样本比例较低，建议重点关注目标范围的预测精度")

        report.append("4. 持续收集生产数据，定期重训练模型以保持性能")
        report.append("5. 考虑结合实时工艺参数进行在线模型更新")

        report.append("")
        report.append(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return "\n".join(report)

def main():
    """主函数 - 模型预测分析"""
    logger.info("=== 89.3%精度模型第五批测试数据预测分析 ===")

    try:
        # 1. 创建分析器并加载模型
        model_path = "improved_classification_system_20250529_101513.pkl"
        analyzer = ModelPredictionAnalysis(model_path)

        if not analyzer.load_model():
            logger.error("模型加载失败，程序退出")
            return

        # 2. 加载第五批测试数据
        logger.info("=== 加载第五批测试数据 ===")
        test_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"

        if not os.path.exists(test_file):
            logger.error(f"测试数据文件不存在: {test_file}")
            return

        test_df = pd.read_excel(test_file)
        logger.info(f"第五批测试数据: {test_df.shape}")
        logger.info(f"测试数据列名: {list(test_df.columns)}")

        # 3. 数据预处理
        logger.info("=== 数据预处理 ===")
        test_processed = analyzer.preprocess_test_data(test_df)
        logger.info(f"预处理后数据: {test_processed.shape}")

        # 4. 特征工程
        logger.info("=== 特征工程 ===")
        test_features = analyzer.feature_engineering_test_data(test_processed)
        logger.info(f"特征工程后: {test_features.shape}")

        # 5. 创建分类标签
        logger.info("=== 创建分类标签 ===")
        test_classified = analyzer.create_test_classification(test_features)
        logger.info("分类标签创建完成")

        # 6. 准备预测数据
        logger.info("=== 准备预测数据 ===")

        # 检查目标列 - 对于预测任务，可能没有真实的钢水温度
        target_col = '钢水温度'
        has_true_target = target_col in test_df.columns  # 检查原始数据是否有真实目标值

        if not has_true_target:
            logger.info("这是一个纯预测任务，测试数据中没有真实的钢水温度值")
            # 使用估算的钢水温度作为参考（仅用于分类，不用于评估）
            y_test = test_classified[target_col]  # 这是估算值
            is_prediction_only = True
        else:
            logger.info("这是一个验证任务，测试数据中包含真实的钢水温度值")
            y_test = test_classified[target_col]
            is_prediction_only = False

        # 选择模型训练时使用的特征
        selected_features = analyzer.selected_features

        # 检查特征是否存在
        missing_features = [f for f in selected_features if f not in test_classified.columns]
        if missing_features:
            logger.warning(f"测试数据中缺少以下特征: {missing_features}")
            # 用0填充缺失特征
            for feature in missing_features:
                test_classified[feature] = 0

        X_test = test_classified[selected_features]
        y_test = test_classified[target_col]

        # 处理缺失值
        X_test = X_test.fillna(X_test.median())
        y_test = y_test.fillna(y_test.median())

        logger.info(f"预测特征数: {len(selected_features)}")
        logger.info(f"测试样本数: {len(X_test)}")

        # 7. 模型预测
        logger.info("=== 模型预测 ===")
        predictions = analyzer.intelligent_ensemble_prediction(X_test, test_classified)

        # 8. 分析和结果展示
        logger.info("=== 预测结果分析 ===")

        if is_prediction_only:
            # 纯预测任务 - 只能做描述性分析
            logger.info("这是纯预测任务，进行描述性分析")

            logger.info(f"第五批测试数据预测结果:")
            logger.info(f"  预测样本数: {len(predictions)}")
            logger.info(f"  预测钢水温度范围: {predictions.min():.1f}°C - {predictions.max():.1f}°C")
            logger.info(f"  预测钢水温度均值: {predictions.mean():.1f}°C ± {predictions.std():.1f}°C")

            # 目标范围分析
            target_mask = (predictions >= analyzer.target_range[0]) & (predictions <= analyzer.target_range[1])
            target_samples = target_mask.sum()
            target_ratio = target_samples / len(predictions) * 100

            logger.info(f"\n目标范围(1590-1670°C)预测分析:")
            logger.info(f"  预测在目标范围内的样本: {target_samples}/{len(predictions)} ({target_ratio:.1f}%)")

            # 预测分布分析
            temp_ranges = [
                (1540, 1580, '低温区'),
                (1580, 1620, '中低温区'),
                (1620, 1660, '中高温区'),
                (1660, 1700, '高温区')
            ]

            logger.info(f"\n预测温度分布:")
            for min_temp, max_temp, range_name in temp_ranges:
                range_mask = (predictions >= min_temp) & (predictions < max_temp)
                range_count = range_mask.sum()
                range_ratio = range_count / len(predictions) * 100
                logger.info(f"  {range_name}: {range_count}样本 ({range_ratio:.1f}%)")

            # 创建简化的分析结果
            analysis_results = {
                'prediction_summary': {
                    'total_samples': len(predictions),
                    'min_pred': predictions.min(),
                    'max_pred': predictions.max(),
                    'mean_pred': predictions.mean(),
                    'std_pred': predictions.std(),
                    'target_samples': target_samples,
                    'target_ratio': target_ratio
                },
                'is_prediction_only': True
            }

        else:
            # 验证任务 - 可以做完整的性能分析
            logger.info("这是验证任务，进行完整性能分析")
            analysis_results = analyzer.comprehensive_analysis(y_test.values, predictions, test_classified)
            analysis_results['is_prediction_only'] = False

            basic_metrics = analysis_results['basic_metrics']

            logger.info(f"第五批测试数据预测性能:")
            logger.info(f"  MAE: {basic_metrics['mae']:.2f}°C")
            logger.info(f"  RMSE: {basic_metrics['rmse']:.2f}°C")
            logger.info(f"  R²: {basic_metrics['r2']:.4f}")
            logger.info(f"  ±20°C精度: {basic_metrics['accuracy_20']:.1f}%")
            logger.info(f"  ±15°C精度: {basic_metrics['accuracy_15']:.1f}%")
            logger.info(f"  ±10°C精度: {basic_metrics['accuracy_10']:.1f}%")
            logger.info(f"  ±5°C精度: {basic_metrics['accuracy_5']:.1f}%")

            # 目标范围分析
            target_analysis = analysis_results['target_range_analysis']
            logger.info(f"\n目标范围(1590-1670°C)分析:")
            logger.info(f"  目标范围样本: {target_analysis['target_samples']}/{target_analysis['total_samples']} ({target_analysis['target_ratio']:.1f}%)")
            if target_analysis['target_samples'] > 0:
                logger.info(f"  目标范围MAE: {target_analysis['target_mae']:.2f}°C")
                logger.info(f"  目标范围R²: {target_analysis['target_r2']:.4f}")

        # 9. 生成可视化图表和报告
        logger.info("=== 生成分析结果 ===")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if not is_prediction_only:
            # 只有在有真实值的情况下才生成完整的可视化和报告
            chart_filename = f"model_prediction_analysis_charts_{timestamp}.png"
            analyzer.generate_visualization(y_test.values, predictions, analysis_results, chart_filename)

            detailed_report = analyzer.generate_detailed_report(analysis_results, test_classified, y_test.values, predictions)
            report_filename = f"model_prediction_analysis_report_{timestamp}.txt"
            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write(detailed_report)
            logger.info(f"详细报告已保存: {report_filename}")
        else:
            # 纯预测任务生成简化报告
            chart_filename = None
            report_filename = f"model_prediction_report_{timestamp}.txt"

            # 生成简化报告
            simple_report = []
            simple_report.append("89.3%精度模型第五批测试数据预测报告")
            simple_report.append("=" * 60)
            simple_report.append("")
            simple_report.append("🤖 模型信息:")
            simple_report.append(f"模型文件: {analyzer.model_path}")
            simple_report.append(f"训练精度: 89.3% (±20°C容差)")
            simple_report.append(f"选择特征数: {len(analyzer.selected_features)}")
            simple_report.append("")
            simple_report.append("📊 预测结果:")
            pred_summary = analysis_results['prediction_summary']
            simple_report.append(f"  预测样本数: {pred_summary['total_samples']}")
            simple_report.append(f"  预测钢水温度范围: {pred_summary['min_pred']:.1f}°C - {pred_summary['max_pred']:.1f}°C")
            simple_report.append(f"  预测钢水温度均值: {pred_summary['mean_pred']:.1f}°C ± {pred_summary['std_pred']:.1f}°C")
            simple_report.append(f"  预测在目标范围内的样本: {pred_summary['target_samples']}/{pred_summary['total_samples']} ({pred_summary['target_ratio']:.1f}%)")
            simple_report.append("")
            simple_report.append("📋 说明:")
            simple_report.append("本次为纯预测任务，测试数据中没有真实的钢水温度值。")
            simple_report.append("预测结果基于铁水温度估算的钢水温度进行分类建模。")
            simple_report.append("建议结合实际生产数据验证预测准确性。")
            simple_report.append("")
            simple_report.append(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            with open(report_filename, 'w', encoding='utf-8') as f:
                f.write("\n".join(simple_report))
            logger.info(f"预测报告已保存: {report_filename}")

        # 10. 保存预测结果
        logger.info("=== 保存预测结果 ===")

        # 创建结果DataFrame
        results_df = test_classified.copy()
        results_df['预测钢水温度'] = predictions

        if not is_prediction_only:
            # 有真实值时计算误差
            results_df['预测误差'] = predictions - y_test.values
            results_df['绝对误差'] = np.abs(predictions - y_test.values)
            results_df['命中±20°C'] = np.abs(predictions - y_test.values) <= 20
        else:
            # 纯预测任务时添加说明列
            results_df['说明'] = '纯预测任务，无真实钢水温度对比'
            results_df['目标范围内'] = (predictions >= analyzer.target_range[0]) & (predictions <= analyzer.target_range[1])

        results_filename = f"model_prediction_results_{timestamp}.xlsx"
        results_df.to_excel(results_filename, index=False)
        logger.info(f"预测结果已保存: {results_filename}")

        # 11. 性能评估总结
        logger.info("=== 性能评估总结 ===")

        if not is_prediction_only:
            # 有真实值时进行性能评估
            basic_metrics = analysis_results['basic_metrics']

            if basic_metrics['accuracy_20'] >= 85:
                logger.info("🎉 模型在第五批测试数据上表现优秀！")
            elif basic_metrics['accuracy_20'] >= 80:
                logger.info("✅ 模型在第五批测试数据上表现良好")
            else:
                logger.info("⚠️ 模型在第五批测试数据上需要进一步优化")

            # 与训练精度比较
            training_accuracy = 89.3
            test_accuracy = basic_metrics['accuracy_20']
            accuracy_drop = training_accuracy - test_accuracy

            logger.info(f"\n泛化能力分析:")
            logger.info(f"  训练精度: {training_accuracy:.1f}%")
            logger.info(f"  测试精度: {test_accuracy:.1f}%")
            logger.info(f"  精度下降: {accuracy_drop:.1f}%")

            if accuracy_drop <= 5:
                logger.info("✅ 模型泛化能力优秀")
            elif accuracy_drop <= 10:
                logger.info("✅ 模型泛化能力良好")
            else:
                logger.info("⚠️ 模型可能存在过拟合，需要进一步优化")
        else:
            # 纯预测任务的总结
            pred_summary = analysis_results['prediction_summary']
            logger.info("📊 预测任务完成总结:")
            logger.info(f"  成功预测{pred_summary['total_samples']}个样本的钢水温度")
            logger.info(f"  预测结果合理性: 温度范围{pred_summary['min_pred']:.1f}-{pred_summary['max_pred']:.1f}°C")
            logger.info(f"  目标范围命中率: {pred_summary['target_ratio']:.1f}%")
            logger.info("  建议: 结合实际生产数据验证预测准确性")

        logger.info("=== 89.3%精度模型第五批测试数据预测分析完成 ===")

        return {
            'chart_filename': chart_filename,
            'report_filename': report_filename,
            'results_filename': results_filename,
            'predictions': predictions,
            'analysis_results': analysis_results,
            'is_prediction_only': is_prediction_only
        }

    except Exception as e:
        logger.error(f"模型预测分析执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
