"""
增强深度学习钢水温度预测系统
修复并实施4大关键改进：
1. 修复TabNet和TensorFlow深度学习模型部署问题
2. 扩大超参数搜索空间
3. 实施多目标优化
4. 增加炉渣成分特征
目标：冲击90%命中率
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime
import joblib

# 核心机器学习库
from sklearn.model_selection import train_test_split, StratifiedKFold, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb

# CatBoost
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
except ImportError:
    CATBOOST_AVAILABLE = False
    print("CatBoost not available, install with: pip install catboost")

# 超参数优化
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    print("Optuna not available, install with: pip install optuna")

# TensorFlow/Keras - 修复版本
TF_AVAILABLE = False
try:
    import tensorflow as tf
    # 设置TensorFlow日志级别
    tf.get_logger().setLevel('ERROR')
    # 禁用GPU（如果有问题）
    tf.config.set_visible_devices([], 'GPU')

    from tensorflow import keras
    from tensorflow.keras import layers, Model, Sequential
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from tensorflow.keras.regularizers import l1_l2

    # 测试TensorFlow是否正常工作
    test_model = Sequential([layers.Dense(1, input_shape=(1,))])
    test_model.compile(optimizer='adam', loss='mse')
    TF_AVAILABLE = True
    print("TensorFlow successfully loaded and tested")

except Exception as e:
    TF_AVAILABLE = False
    print(f"TensorFlow not available: {e}")

# TabNet - 修复版本
TABNET_AVAILABLE = False
try:
    # 首先尝试导入PyTorch
    import torch
    # 设置PyTorch使用CPU
    torch.set_default_tensor_type('torch.FloatTensor')

    # 然后导入TabNet
    from pytorch_tabnet.tab_model import TabNetRegressor
    from pytorch_tabnet.metrics import Metric

    # 测试TabNet是否正常工作
    test_tabnet = TabNetRegressor(n_d=8, n_a=8, n_steps=3)
    TABNET_AVAILABLE = True
    print("TabNet successfully loaded and tested")

except Exception as e:
    TABNET_AVAILABLE = False
    print(f"TabNet not available: {e}")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"enhanced_deep_learning_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedDeepLearningPredictor:
    """增强深度学习预测器"""

    def __init__(self):
        self.models = {}
        self.best_params = {}
        self.scalers = {}
        self.label_encoders = {}
        self.feature_names = []
        self.study_results = {}

        # 目标范围参数
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

        # 多目标优化权重
        self.multi_objective_weights = {
            'accuracy': 0.5,      # 目标精度权重
            'stability': 0.2,     # 预测稳定性权重
            'generalization': 0.2, # 泛化能力权重
            'physics': 0.1        # 物理一致性权重
        }

    def multi_objective_loss(self, y_true: np.ndarray, y_pred: np.ndarray,
                           y_val_true: np.ndarray = None, y_val_pred: np.ndarray = None) -> float:
        """多目标优化损失函数"""

        # 1. 精度损失（目标范围内的命中率）
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])
        if target_mask.sum() > 0:
            target_y_true = y_true[target_mask]
            target_y_pred = y_pred[target_mask]
            hit_rate = np.mean(np.abs(target_y_true - target_y_pred) <= self.target_tolerance)
            accuracy_loss = (1 - hit_rate) * 1000
        else:
            accuracy_loss = 1000

        # 2. 稳定性损失（预测方差）
        stability_loss = np.var(y_pred) / 100  # 归一化

        # 3. 泛化能力损失（训练集vs验证集性能差异）
        if y_val_true is not None and y_val_pred is not None:
            train_mae = np.mean(np.abs(y_true - y_pred))
            val_mae = np.mean(np.abs(y_val_true - y_val_pred))
            generalization_loss = abs(val_mae - train_mae) * 10
        else:
            generalization_loss = 0

        # 4. 物理一致性损失（预测值在合理范围内）
        physics_loss = np.mean((y_pred < 1500) | (y_pred > 1750)) * 500

        # 组合损失
        total_loss = (self.multi_objective_weights['accuracy'] * accuracy_loss +
                     self.multi_objective_weights['stability'] * stability_loss +
                     self.multi_objective_weights['generalization'] * generalization_loss +
                     self.multi_objective_weights['physics'] * physics_loss)

        return total_loss

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def robust_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """稳健的数据清理"""
        logger.info("开始稳健数据清理")

        df_clean = df.copy()

        # 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
                return float(value)
            except:
                return default

        # 处理数值列
        numeric_columns = ['铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                          '铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s', '最大角度']

        for col in numeric_columns:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].apply(safe_convert)

        # 移除无穷大值和异常值
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)

        # 基于物理约束的异常值检测
        constraints = {
            '铁水温度': (1250, 1500),
            '铁水C': (3.0, 5.5),
            '铁水SI': (0.1, 1.5),
            '铁水MN': (0.05, 1.0),
            '铁水P': (0.05, 0.3),
            '铁水': (60, 120),
            '废钢': (0, 50),
            '累氧实际': (3000, 7000),
            '吹氧时间s': (300, 1200)
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].clip(min_val, max_val)

        # 目标变量清理
        if '钢水温度' in df_clean.columns:
            df_clean['钢水温度'] = df_clean['钢水温度'].apply(safe_convert)
            df_clean = df_clean[(df_clean['钢水温度'] >= 1500) & (df_clean['钢水温度'] <= 1750)]

        logger.info(f"数据清理完成，保留{len(df_clean)}条记录")
        return df_clean

    def create_slag_composition_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建炉渣成分特征（改进版）"""
        logger.info("创建炉渣成分特征")

        df_slag = df.copy()

        # 基础炉渣成分估算（基于加料量和铁水成分）
        for idx, row in df_slag.iterrows():
            try:
                # 基础参数
                lime = self.safe_convert(row.get('石灰', 0))
                dolomite = self.safe_convert(row.get('白云石', 0))
                hot_metal_mass = self.safe_convert(row.get('铁水', 90))
                si_content = self.safe_convert(row.get('铁水SI', 0.4))
                mn_content = self.safe_convert(row.get('铁水MN', 0.17))
                p_content = self.safe_convert(row.get('铁水P', 0.1))

                # 1. 炉渣碱度估算
                # CaO来源：石灰(90%CaO) + 白云石(55%CaO)
                cao_from_lime = lime * 0.9
                cao_from_dolomite = dolomite * 0.55
                total_cao = cao_from_lime + cao_from_dolomite

                # SiO2来源：主要来自铁水中的Si氧化
                sio2_from_si = si_content * hot_metal_mass * 0.01 * 2.14  # Si -> SiO2

                # 炉渣碱度 = CaO/SiO2
                if sio2_from_si > 0:
                    basicity = total_cao / sio2_from_si
                else:
                    basicity = 3.0  # 默认值

                df_slag.loc[idx, 'slag_basicity'] = np.clip(basicity, 1.5, 5.0)

                # 2. 炉渣FeO含量估算
                # FeO主要来自铁水氧化和废钢氧化
                oxygen_consumed = self.safe_convert(row.get('累氧实际', 4800))
                scrap_mass = self.safe_convert(row.get('废钢', 20))

                # 简化的FeO估算
                feo_from_oxidation = (oxygen_consumed / 1000) * 0.3  # 30%氧气用于铁氧化
                feo_content = np.clip(feo_from_oxidation, 8, 25)  # 典型范围8-25%

                df_slag.loc[idx, 'slag_feo'] = feo_content

                # 3. 炉渣MgO含量估算
                # MgO来源：白云石(40%MgO) + 镁砖溶损
                mgo_from_dolomite = dolomite * 0.4
                mgo_from_refractory = 2.0  # 假设镁砖溶损贡献2%
                total_mgo = mgo_from_dolomite + mgo_from_refractory

                df_slag.loc[idx, 'slag_mgo'] = np.clip(total_mgo, 3, 15)

                # 4. 炉渣P2O5含量估算
                # P2O5主要来自铁水中的P
                p2o5_content = p_content * 2.29  # P -> P2O5
                df_slag.loc[idx, 'slag_p2o5'] = np.clip(p2o5_content, 0.5, 3.0)

                # 5. 炉渣液相线温度估算（简化模型）
                # 基于炉渣成分的液相线温度估算
                liquidus_temp = (1600 - basicity * 20 + feo_content * 2 -
                                total_mgo * 3 + p2o5_content * 10)
                df_slag.loc[idx, 'slag_liquidus_temp'] = np.clip(liquidus_temp, 1450, 1650)

                # 6. 炉渣粘度指数
                # 基于成分的粘度估算
                viscosity_index = (basicity * 0.3 + total_mgo * 0.2 - feo_content * 0.1)
                df_slag.loc[idx, 'slag_viscosity_index'] = np.clip(viscosity_index, 0.5, 3.0)

                # 7. 炉渣脱磷能力指数
                # 基于碱度和温度的脱磷能力
                dephosphorization_index = basicity * 0.4 + (1650 - liquidus_temp) * 0.01
                df_slag.loc[idx, 'slag_dephosphorization_index'] = np.clip(dephosphorization_index, 1.0, 4.0)

                # 8. 炉渣泡沫指数
                # 基于FeO含量和碱度的泡沫倾向
                foam_index = feo_content * 0.1 + basicity * 0.2
                df_slag.loc[idx, 'slag_foam_index'] = np.clip(foam_index, 1.0, 5.0)

            except Exception as e:
                logger.warning(f"计算第{idx}行炉渣特征时出错: {e}")
                # 设置默认值
                df_slag.loc[idx, 'slag_basicity'] = 2.8
                df_slag.loc[idx, 'slag_feo'] = 15.0
                df_slag.loc[idx, 'slag_mgo'] = 8.0
                df_slag.loc[idx, 'slag_p2o5'] = 1.5
                df_slag.loc[idx, 'slag_liquidus_temp'] = 1550
                df_slag.loc[idx, 'slag_viscosity_index'] = 1.5
                df_slag.loc[idx, 'slag_dephosphorization_index'] = 2.5
                df_slag.loc[idx, 'slag_foam_index'] = 2.5

        logger.info("炉渣成分特征创建完成")
        return df_slag

    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            if isinstance(value, str):
                value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
            return float(value)
        except:
            return default

    def create_enhanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建增强特征"""
        logger.info("创建增强特征")

        df_features = df.copy()

        # 1. 基础工程特征
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # 2. 成分交互特征
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['C_MN_interaction'] = df_features['铁水C'] * df_features['铁水MN']
        df_features['SI_MN_interaction'] = df_features['铁水SI'] * df_features['铁水MN']
        df_features['C_P_interaction'] = df_features['铁水C'] * df_features['铁水P']

        # 3. 温度相关特征
        df_features['temp_carbon_ratio'] = df_features['铁水温度'] / (df_features['铁水C'] + 1e-6)
        df_features['temp_oxygen_ratio'] = df_features['铁水温度'] / (df_features['oxygen_intensity'] + 1e-6)
        df_features['temp_scrap_interaction'] = df_features['铁水温度'] * df_features['scrap_ratio']

        # 4. 炉渣-工艺交互特征
        if 'slag_basicity' in df_features.columns:
            df_features['basicity_oxygen_interaction'] = df_features['slag_basicity'] * df_features['oxygen_intensity']
            df_features['feo_oxygen_interaction'] = df_features['slag_feo'] * df_features['oxygen_intensity']
            df_features['basicity_temp_interaction'] = df_features['slag_basicity'] * df_features['铁水温度']
            df_features['liquidus_temp_diff'] = df_features['铁水温度'] - df_features['slag_liquidus_temp']

        # 5. 物理约束特征
        for idx, row in df_features.iterrows():
            try:
                hot_metal_temp = row['铁水温度']
                c_content = row['铁水C'] / 100
                si_content = row['铁水SI'] / 100
                scrap_ratio = row['scrap_ratio']

                # 理论温升
                oxidation_heat = c_content * 15 + si_content * 25
                scrap_cooling = scrap_ratio * 50
                theoretical_temp_rise = oxidation_heat - scrap_cooling
                theoretical_temp_rise = np.clip(theoretical_temp_rise, 50, 400)

                df_features.loc[idx, 'theoretical_temp_rise'] = theoretical_temp_rise
                df_features.loc[idx, 'theoretical_end_temp'] = hot_metal_temp + theoretical_temp_rise

            except Exception as e:
                logger.warning(f"计算第{idx}行物理特征时出错: {e}")
                df_features.loc[idx, 'theoretical_temp_rise'] = 100
                df_features.loc[idx, 'theoretical_end_temp'] = row['铁水温度'] + 100

        # 6. 钢种分类特征
        if '钢种' in df_features.columns:
            def classify_steel_grade(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70', 'C72DA', 'SWRCH']):
                    return '高碳钢'
                elif any(x in steel_type for x in ['ER50', 'ML40CR', '40CR', '20CRMO']):
                    return '合金钢'
                elif any(x in steel_type for x in ['304', '316', '321', '430']):
                    return '不锈钢'
                else:
                    return '普通钢'

            df_features['steel_category'] = df_features['钢种'].apply(classify_steel_grade)

            def estimate_carbon_content(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70']):
                    return 'high_carbon'
                elif any(x in steel_type for x in ['20', '16MN']):
                    return 'low_carbon'
                else:
                    return 'medium_carbon'

            df_features['carbon_grade'] = df_features['钢种'].apply(estimate_carbon_content)

        # 7. 时序特征（简化版）
        if '炉号' in df_features.columns:
            df_features = df_features.sort_values('炉号')

            for feature in ['铁水温度', '铁水C', 'oxygen_intensity']:
                if feature in df_features.columns:
                    df_features[f'{feature}_lag_1'] = df_features[feature].shift(1)
                    df_features[f'{feature}_lag_2'] = df_features[feature].shift(2)

            for feature in ['铁水温度', '铁水C']:
                if feature in df_features.columns:
                    df_features[f'{feature}_ma_3'] = df_features[feature].rolling(3).mean()
                    df_features[f'{feature}_ma_5'] = df_features[feature].rolling(5).mean()

        # 填充缺失值
        numeric_cols = df_features.select_dtypes(include=[np.number]).columns
        df_features[numeric_cols] = df_features[numeric_cols].fillna(df_features[numeric_cols].median())

        logger.info("增强特征创建完成")
        return df_features

    def prepare_data_for_models(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List[str], List[str]]:
        """为模型准备数据"""
        logger.info("准备模型数据")

        exclude_cols = ['炉号', '出钢重量估算', 'Unnamed: 4', '钢水温度']

        feature_cols = [col for col in df.columns if col not in exclude_cols]
        X = df[feature_cols].copy()

        if '钢水温度' in df.columns:
            y = df['钢水温度'].copy()
        else:
            y = pd.Series([0] * len(df))

        # 识别分类特征
        categorical_features = []
        for col in X.columns:
            if X[col].dtype == 'object' or col in ['steel_category', 'carbon_grade']:
                categorical_features.append(col)

        # 处理分类特征
        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                X[col] = self.label_encoders[col].fit_transform(X[col].astype(str))
            else:
                try:
                    X[col] = self.label_encoders[col].transform(X[col].astype(str))
                except ValueError:
                    mode_value = X[col].mode()[0] if not X[col].mode().empty else 0
                    X[col] = X[col].apply(lambda x: self.label_encoders[col].transform([str(x)])[0]
                                         if str(x) in self.label_encoders[col].classes_ else mode_value)

        # 最终数据清理
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        categorical_cols = X.select_dtypes(include=['object']).columns

        if len(numeric_cols) > 0:
            X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())

        if len(categorical_cols) > 0:
            for col in categorical_cols:
                X[col] = X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'unknown')

        logger.info(f"数据准备完成：{X.shape[1]}个特征，{len(categorical_features)}个分类特征")
        return X, y, feature_cols, categorical_features

    def create_enhanced_tensorflow_model(self, input_dim: int, categorical_features: List[str] = None) -> Any:
        """创建增强的TensorFlow模型（修复版）"""
        if not TF_AVAILABLE:
            logger.warning("TensorFlow不可用，跳过神经网络模型")
            return None

        try:
            logger.info("创建增强TensorFlow模型")

            # 输入层
            inputs = keras.Input(shape=(input_dim,), name='input_features')

            # 特征提取层（更深的网络）
            x = layers.Dense(256, activation='relu', name='dense_1')(inputs)
            x = layers.BatchNormalization()(x)
            x = layers.Dropout(0.3)(x)

            x = layers.Dense(128, activation='relu', name='dense_2')(x)
            x = layers.BatchNormalization()(x)
            x = layers.Dropout(0.2)(x)

            x = layers.Dense(64, activation='relu', name='dense_3')(x)
            x = layers.BatchNormalization()(x)
            x = layers.Dropout(0.1)(x)

            # 分支预测
            main_branch = layers.Dense(32, activation='relu', name='main_branch')(x)
            main_output = layers.Dense(1, name='main_output')(main_branch)

            # 辅助分支（多任务学习）
            aux_branch = layers.Dense(16, activation='relu', name='aux_branch')(x)
            aux_output = layers.Dense(1, name='aux_output')(aux_branch)

            # 组合输出
            combined = layers.Add(name='combined_output')([main_output, aux_output])

            # 物理约束层
            def physics_constraint_layer(x):
                return tf.clip_by_value(x, 1500.0, 1750.0)

            final_output = layers.Lambda(physics_constraint_layer, name='physics_constrained')(combined)

            model = Model(inputs=inputs, outputs=final_output)

            # 自定义损失函数
            def tensorflow_multi_objective_loss(y_true, y_pred):
                # 基础MSE损失
                mse_loss = tf.keras.losses.mean_squared_error(y_true, y_pred)

                # 目标范围内的精度损失
                target_mask = tf.logical_and(y_true >= self.target_range[0], y_true <= self.target_range[1])

                if tf.reduce_sum(tf.cast(target_mask, tf.float32)) > 0:
                    target_y_true = tf.boolean_mask(y_true, target_mask)
                    target_y_pred = tf.boolean_mask(y_pred, target_mask)

                    hit_rate = tf.reduce_mean(tf.cast(tf.abs(target_y_true - target_y_pred) <= self.target_tolerance, tf.float32))
                    accuracy_loss = (1 - hit_rate) * 1000
                else:
                    accuracy_loss = 1000.0

                # 稳定性损失
                stability_loss = tf.math.reduce_variance(y_pred) / 100

                # 物理一致性损失
                physics_loss = tf.reduce_mean(tf.cast(tf.logical_or(y_pred < 1500, y_pred > 1750), tf.float32)) * 500

                # 组合损失
                total_loss = (self.multi_objective_weights['accuracy'] * accuracy_loss +
                             self.multi_objective_weights['stability'] * stability_loss +
                             self.multi_objective_weights['physics'] * physics_loss +
                             0.3 * mse_loss)

                return total_loss

            model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss=tensorflow_multi_objective_loss,
                metrics=['mae']
            )

            return model

        except Exception as e:
            logger.error(f"创建TensorFlow模型失败: {e}")
            return None

    def create_enhanced_tabnet_model(self, X_train: pd.DataFrame, y_train: pd.Series,
                                   X_val: pd.DataFrame, y_val: pd.Series,
                                   categorical_features: List[str]) -> Optional[Any]:
        """创建增强的TabNet模型（修复版）"""
        if not TABNET_AVAILABLE:
            logger.warning("TabNet不可用，跳过TabNet模型")
            return None

        try:
            logger.info("创建增强TabNet模型")

            # 准备分类特征索引
            cat_idxs = [X_train.columns.get_loc(col) for col in categorical_features if col in X_train.columns]
            cat_dims = [X_train[col].nunique() for col in categorical_features if col in X_train.columns]

            # 增强的TabNet参数
            tabnet_params = {
                'cat_idxs': cat_idxs,
                'cat_dims': cat_dims,
                'cat_emb_dim': 2,  # 增加嵌入维度
                'n_d': 64,         # 增加决策层维度
                'n_a': 64,         # 增加注意力层维度
                'n_steps': 7,      # 增加步数
                'gamma': 1.3,      # 调整稀疏性
                'lambda_sparse': 1e-3,
                'optimizer_fn': torch.optim.Adam,
                'optimizer_params': dict(lr=2e-2, weight_decay=1e-5),
                'mask_type': 'entmax',
                'scheduler_params': {"step_size": 30, "gamma": 0.8},
                'scheduler_fn': torch.optim.lr_scheduler.StepLR,
                'verbose': 0,
                'seed': 42
            }

            model = TabNetRegressor(**tabnet_params)

            # 自定义评估指标
            class TargetAccuracyMetric:
                def __init__(self):
                    self._name = "target_accuracy"
                    self._maximize = True

                def __call__(self, y_true, y_score):
                    y_true = y_true.reshape(-1)
                    y_score = y_score.reshape(-1)

                    target_mask = (y_true >= 1590) & (y_true <= 1670)
                    if target_mask.sum() == 0:
                        return 0.0

                    target_y_true = y_true[target_mask]
                    target_y_score = y_score[target_mask]

                    accuracy = np.mean(np.abs(target_y_true - target_y_score) <= 20)
                    return accuracy

            # 训练
            model.fit(
                X_train.values, y_train.values.reshape(-1, 1),
                eval_set=[(X_val.values, y_val.values.reshape(-1, 1))],
                eval_name=['val'],
                eval_metric=[TargetAccuracyMetric()],
                max_epochs=300,
                patience=30,
                batch_size=512,
                virtual_batch_size=256,
                num_workers=0,
                drop_last=False
            )

            return model

        except Exception as e:
            logger.error(f"TabNet训练失败: {e}")
            return None

    def expanded_hyperparameter_optimization(self, X: pd.DataFrame, y: pd.Series,
                                           categorical_features: List[str]) -> Dict[str, Any]:
        """扩大的超参数搜索空间优化"""
        logger.info("开始扩大超参数搜索空间优化")

        if not OPTUNA_AVAILABLE:
            logger.warning("Optuna不可用，使用默认参数")
            return self.train_default_models(X, y, categorical_features)

        optimization_results = {}

        # 1. XGBoost扩大搜索空间
        def optimize_xgboost_expanded(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 2000),  # 扩大范围
                'max_depth': trial.suggest_int('max_depth', 2, 20),           # 扩大范围
                'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.5),  # 扩大范围
                'subsample': trial.suggest_float('subsample', 0.5, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.5, 1.0),
                'colsample_bylevel': trial.suggest_float('colsample_bylevel', 0.5, 1.0),  # 新增
                'colsample_bynode': trial.suggest_float('colsample_bynode', 0.5, 1.0),    # 新增
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 20),         # 扩大范围
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 20),       # 扩大范围
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),  # 新增
                'gamma': trial.suggest_float('gamma', 0, 10),                 # 新增
                'random_state': 42
            }

            model = xgb.XGBRegressor(**params)

            tscv = TimeSeriesSplit(n_splits=3)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("优化XGBoost超参数（扩大搜索空间）...")
        study_xgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=20),  # 增加启动试验
            pruner=MedianPruner(n_startup_trials=10, n_warmup_steps=15)
        )
        study_xgb.optimize(optimize_xgboost_expanded, n_trials=100, timeout=3600)  # 增加试验次数

        optimization_results['XGBoost_Enhanced'] = {
            'best_params': study_xgb.best_params,
            'best_score': study_xgb.best_value,
            'study': study_xgb
        }

        # 2. LightGBM扩大搜索空间
        def optimize_lightgbm_expanded(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 50, 2000),
                'max_depth': trial.suggest_int('max_depth', 2, 20),
                'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.5),
                'subsample': trial.suggest_float('subsample', 0.5, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.5, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 20),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 20),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 100),  # 新增
                'min_child_weight': trial.suggest_float('min_child_weight', 1e-3, 10),  # 新增
                'num_leaves': trial.suggest_int('num_leaves', 10, 300),       # 新增
                'feature_fraction': trial.suggest_float('feature_fraction', 0.5, 1.0),  # 新增
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.5, 1.0),  # 新增
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 7),      # 新增
                'random_state': 42,
                'verbose': -1
            }

            model = lgb.LGBMRegressor(**params)

            tscv = TimeSeriesSplit(n_splits=3)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("优化LightGBM超参数（扩大搜索空间）...")
        study_lgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=20),
            pruner=MedianPruner(n_startup_trials=10, n_warmup_steps=15)
        )
        study_lgb.optimize(optimize_lightgbm_expanded, n_trials=100, timeout=3600)

        optimization_results['LightGBM_Enhanced'] = {
            'best_params': study_lgb.best_params,
            'best_score': study_lgb.best_value,
            'study': study_lgb
        }

        # 3. CatBoost扩大搜索空间
        if CATBOOST_AVAILABLE:
            def optimize_catboost_expanded(trial):
                params = {
                    'iterations': trial.suggest_int('iterations', 50, 2000),
                    'depth': trial.suggest_int('depth', 2, 12),
                    'learning_rate': trial.suggest_float('learning_rate', 0.005, 0.5),
                    'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 0.1, 20),
                    'border_count': trial.suggest_int('border_count', 32, 255),
                    'bagging_temperature': trial.suggest_float('bagging_temperature', 0, 10),  # 新增
                    'random_strength': trial.suggest_float('random_strength', 0, 10),          # 新增
                    'one_hot_max_size': trial.suggest_int('one_hot_max_size', 2, 25),          # 新增
                    'leaf_estimation_iterations': trial.suggest_int('leaf_estimation_iterations', 1, 10),  # 新增
                    'random_state': 42,
                    'verbose': False
                }

                cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
                model = cb.CatBoostRegressor(cat_features=cat_features_idx, **params)

                tscv = TimeSeriesSplit(n_splits=3)
                scores = []

                for train_idx, val_idx in tscv.split(X):
                    X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                    y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                    model.fit(X_train_cv, y_train_cv)
                    y_pred_cv = model.predict(X_val_cv)

                    score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                    scores.append(score)

                return np.mean(scores)

            logger.info("优化CatBoost超参数（扩大搜索空间）...")
            study_cat = optuna.create_study(
                direction='minimize',
                sampler=TPESampler(seed=42, n_startup_trials=15),
                pruner=MedianPruner(n_startup_trials=8, n_warmup_steps=12)
            )
            study_cat.optimize(optimize_catboost_expanded, n_trials=80, timeout=3000)

            optimization_results['CatBoost_Enhanced'] = {
                'best_params': study_cat.best_params,
                'best_score': study_cat.best_value,
                'study': study_cat
            }

        self.study_results = optimization_results
        logger.info("扩大超参数搜索空间优化完成")

        return optimization_results
