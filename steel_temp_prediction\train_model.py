"""
训练钢水温度预测模型并保存。
该脚本使用第四批数据集进行模型训练，然后保存模型以便后续预测使用。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
import pickle
import time
from typing import Dict, List, Tuple, Any, Optional

# 确保模块可以被正确导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename=os.path.join(parent_dir, 'model_training.log')
)
logger = logging.getLogger(__name__)
# 同时输出到控制台
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logger.addHandler(console)

# 导入所需模块
from feature_engineering import engineer_all_features
from model_development import train_sequential_thinking_model
from utils import calculate_metrics, format_time

def describe_feature_importance(model, feature_names, top_n=20):
    """
    描述特征重要性。
    
    Args:
        model: 模型
        feature_names: 特征名列表
        top_n: 显示前几个特征
        
    Returns:
        包含特征重要性的DataFrame
    """
    if not hasattr(model, 'feature_importances_'):
        return pd.DataFrame()
    
    importances = model.feature_importances_
    indices = np.argsort(importances)[::-1]
    
    top_indices = indices[:top_n]
    top_importances = importances[top_indices]
    top_features = [feature_names[i] for i in top_indices]
    
    importance_df = pd.DataFrame({
        '特征': top_features,
        '重要性': top_importances
    })
    
    # 按重要性降序排列
    importance_df = importance_df.sort_values('重要性', ascending=False)
    
    # 计算累积重要性百分比
    importance_df['累积百分比'] = (
        importance_df['重要性'].cumsum() / importance_df['重要性'].sum() * 100
    )
    
    return importance_df

def load_training_data(file_path: str) -> pd.DataFrame:
    """
    加载训练数据。
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        加载的DataFrame
    """
    logger.info(f"加载训练数据: {file_path}")
    try:
        df = pd.read_excel(file_path)
        logger.info(f"成功加载数据，共 {len(df)} 行, {len(df.columns)} 列")
        
        # 检查是否包含目标变量
        target_column = "钢水温度"  # 修改目标变量名
        if target_column not in df.columns:
            logger.error(f"训练数据中没有目标变量'{target_column}'，无法训练模型")
            raise ValueError(f"缺少目标变量'{target_column}'")
        
        # 为了保持一致性，将"钢水温度"列重命名为"出钢温度"
        df = df.rename(columns={target_column: "出钢温度"})
        logger.info(f"已将'{target_column}'列重命名为'出钢温度'")
            
        return df
    except Exception as e:
        logger.error(f"加载数据时出错: {str(e)}")
        raise

def prepare_training_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    准备训练数据，包括清洗和预处理。
    
    Args:
        df: 原始DataFrame
        
    Returns:
        清洗后的DataFrame
    """
    logger.info("准备训练数据...")
    
    # 复制数据，避免修改原始数据
    df_clean = df.copy()
    
    # 1. 检查并删除重复项
    duplicates = df_clean.duplicated()
    if duplicates.any():
        logger.info(f"删除 {duplicates.sum()} 行重复数据")
        df_clean = df_clean.drop_duplicates()
    
    # 2. 检查并处理缺失值
    missing_values = df_clean.isnull().sum()
    if missing_values.any():
        logger.info("处理缺失值:")
        for col in missing_values[missing_values > 0].index:
            missing_count = missing_values[col]
            missing_percent = missing_count / len(df_clean) * 100
            logger.info(f"  列 '{col}' 有 {missing_count} 缺失值 ({missing_percent:.2f}%)")
            
            # 对数值列使用中位数填充
            if df_clean[col].dtype in ['int64', 'float64']:
                median_value = df_clean[col].median()
                df_clean[col].fillna(median_value, inplace=True)
                logger.info(f"    已用中位数 {median_value} 填充")
            # 对分类列使用众数填充
            else:
                mode_value = df_clean[col].mode()[0]
                df_clean[col].fillna(mode_value, inplace=True)
                logger.info(f"    已用众数 '{mode_value}' 填充")
    
    # 3. 检查异常值
    for col in df_clean.select_dtypes(include=['int64', 'float64']).columns:
        mean = df_clean[col].mean()
        std = df_clean[col].std()
        lower_bound = mean - 3 * std
        upper_bound = mean + 3 * std
        
        outliers = df_clean[(df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)]
        if len(outliers) > 0:
            logger.info(f"  列 '{col}' 有 {len(outliers)} 个异常值")
            # 记录但不处理异常值，交给特征工程步骤处理
    
    logger.info(f"数据准备完成，剩余 {len(df_clean)} 行")
    return df_clean

def add_features_and_clean(df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series]:
    """
    进行特征工程并清洗数据。
    
    Args:
        df: 输入DataFrame
        
    Returns:
        X_train: 训练特征
        y_train: 训练目标
    """
    logger.info("开始特征工程...")
    
    # 进行特征工程
    df_featured = engineer_all_features(
        df,
        enable_slag=True,
        enable_process_params=True,
        enable_time_series=False,  # 假设没有时间序列
        enable_interactions=True,
        enable_physicochem_props=True,
        enable_advanced_slag=True,
        enable_lance_dynamics=True
    )
    
    logger.info(f"特征工程完成，特征数: {len(df_featured.columns)}")
    
    # 分离特征和目标
    X = df_featured.select_dtypes(include=['float64', 'int64']).drop(columns=['出钢温度'], errors='ignore')
    y = df_featured['出钢温度']
    
    # 数据清洗 - 处理inf和极大值
    logger.info("清洗特征数据，处理inf和异常值...")
    
    # 处理磷容量系数
    if 'feature_slag_phosphate_capacity' in X.columns:
        logger.info(f"对磷容量系数取对数，原始范围: "
                  f"{X['feature_slag_phosphate_capacity'].min():.2e}-{X['feature_slag_phosphate_capacity'].max():.2e}")
        # 先确保所有值都是正数，然后取对数
        X['feature_slag_phosphate_capacity'] = np.where(
            X['feature_slag_phosphate_capacity'] > 0,
            np.log10(X['feature_slag_phosphate_capacity']),
            0
        )
    
    # 处理其他可能的极大值特征
    for col in X.columns:
        try:
            if X[col].max() > 1e20:
                logger.info(f"对特征 {col} 取对数")
                X[col] = np.where(X[col] > 0, np.log10(X[col]), 0)
        except (TypeError, ValueError):
            logger.warning(f"无法处理特征 {col} 的极值，将删除该特征")
            X = X.drop(columns=[col])
    
    # 替换inf和-inf
    X = X.replace([np.inf, -np.inf], np.nan)
    
    # 处理缺失值
    X = X.fillna(X.median())
    
    # 处理极值
    for col in X.columns:
        median = X[col].median()
        std = X[col].std()
        upper_limit = median + 5 * std
        lower_limit = median - 5 * std
        X[col] = X[col].clip(lower_limit, upper_limit)
    
    # 检查是否有问题的特征
    col_stats = X.describe().transpose()[['min', 'max', 'mean', 'std']]
    problematic_cols = col_stats[(col_stats['max'] > 1e10) | (col_stats['min'] < -1e10) | 
                              col_stats['std'].isna() | np.isinf(col_stats['std'])]
    
    if not problematic_cols.empty:
        for idx, row in problematic_cols.iterrows():
            logger.warning(f"删除有问题的特征: {idx}")
            X = X.drop(columns=[idx])
    
    logger.info(f"数据清洗完成，最终特征数: {X.shape[1]}")
    return X, y

def train_model(X_train: pd.DataFrame, y_train: pd.Series, model_path: str = 'steel_temp_model.pkl') -> Any:
    """
    训练模型并保存。
    
    Args:
        X_train: 训练特征
        y_train: 训练目标
        model_path: 模型保存路径
        
    Returns:
        训练好的模型
    """
    logger.info("开始训练模型...")
    
    # 配置超参数调优设置
    tune_base_models = {
        'xgboost': 5,     # 对XGBoost进行5次调优迭代
        'lightgbm': 5     # 对LightGBM进行5次调优迭代
    }
    
    # 训练模型
    start_time = time.time()
    model = train_sequential_thinking_model(
        X_train, y_train,
        tune_base_model_n_iters=tune_base_models,
        tune_meta_model_n_iter=10
    )
    training_time = time.time() - start_time
    logger.info(f"模型训练完成，耗时 {format_time(training_time)}")
    
    # 保存模型
    logger.info(f"保存模型到: {model_path}")
    with open(model_path, 'wb') as f:
        pickle.dump(model, f)
    
    # 计算训练集表现
    y_pred_train = model.predict(X_train)
    metrics = calculate_metrics(y_train, y_pred_train)
    
    logger.info("训练集评估结果:")
    logger.info(f"  平均绝对误差 (MAE): {metrics['mae']:.2f}°C")
    logger.info(f"  均方根误差 (RMSE): {metrics['rmse']:.2f}°C")
    logger.info(f"  R²: {metrics['r2']:.4f}")
    logger.info(f"  命中率 (±20°C): {metrics['hit_rate_20']:.2f}%")
    
    # 可视化训练结果
    plt.figure(figsize=(10, 6))
    plt.scatter(y_train, y_pred_train, alpha=0.5)
    plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--')
    plt.xlabel('实际温度 (°C)')
    plt.ylabel('预测温度 (°C)')
    plt.title('训练集温度预测结果')
    plt.savefig(os.path.join(parent_dir, 'training_results.png'), dpi=300, bbox_inches='tight')
    logger.info("训练集结果图已保存到 training_results.png")
    
    return model

def main():
    """
    主函数，训练钢水温度预测模型。
    """
    logger.info("开始训练钢水温度预测模型")
    
    # 设置文件路径
    training_data_file = os.path.join(parent_dir, "第四批测试数据20250513.xlsx")
    model_file = os.path.join(current_dir, "steel_temp_model.pkl")
    
    # 检查训练数据是否存在
    if not os.path.exists(training_data_file):
        logger.error(f"训练数据文件不存在: {training_data_file}")
        # 列出当前目录和上级目录的文件，帮助定位
        logger.info(f"当前目录: {os.getcwd()}")
        logger.info("当前目录文件列表:")
        for file in os.listdir(current_dir):
            logger.info(f"  - {file}")
        logger.info("上级目录文件列表:")
        for file in os.listdir(parent_dir):
            logger.info(f"  - {file}")
        return
    
    # 1. 加载训练数据
    df = load_training_data(training_data_file)
    
    # 2. 数据准备
    df_clean = prepare_training_data(df)
    
    # 3. 特征工程和数据清洗
    X_train, y_train = add_features_and_clean(df_clean)
    
    # 4. 训练模型
    model = train_model(X_train, y_train, model_file)
    
    # 5. 打印模型信息和特征重要性
    if hasattr(model, 'feature_importances_'):
        importance_df = describe_feature_importance(model, list(X_train.columns), top_n=20)
        logger.info("特征重要性前20名:")
        for i, (index, row) in enumerate(importance_df.iterrows()):
            logger.info(f"  {i+1}. {row['特征']}: {row['重要性']:.4f} (累积 {row['累积百分比']:.2f}%)")
    
    logger.info("钢水温度预测模型训练完成")

if __name__ == "__main__":
    main() 