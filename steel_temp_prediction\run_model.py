"""
运行钢水温度预测模型的示例脚本。
这个脚本展示了使用增强的特征工程模块进行特征提取和模型训练的过程。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
from typing import Dict, List, Tuple, Any

# 确保模块可以被导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入所需模块
try:
    from steel_temp_prediction.feature_engineering import (
        engineer_all_features, 
        add_all_slag_features,
        add_lance_dynamics_features,
        add_slag_process_interactions,
        calculate_viscosity_from_factsage,
        calculate_slag_phase_properties,
        calculate_phosphate_capacity
    )
    from steel_temp_prediction.model_development import train_sequential_thinking_model
except ImportError:
    # 如果上面的导入失败，尝试直接导入（当脚本在包内运行时）
    from feature_engineering import (
        engineer_all_features, 
        add_all_slag_features,
        add_lance_dynamics_features,
        add_slag_process_interactions,
        calculate_viscosity_from_factsage,
        calculate_slag_phase_properties,
        calculate_phosphate_capacity
    )
    from model_development import train_sequential_thinking_model

def create_sample_data(n_samples: int = 20) -> pd.DataFrame:
    """
    创建样本数据用于测试。
    
    Args:
        n_samples: 样本数量
    
    Returns:
        样本数据DataFrame
    """
    # 随机种子，保证结果可复现
    np.random.seed(42)
    
    # 创建样本数据
    data = {
        '铁水': np.random.uniform(70000, 100000, n_samples),  # 铁水质量(kg)
        '铁水温度': np.random.uniform(1280, 1350, n_samples),  # 铁水温度(℃)
        '铁水SI': np.random.uniform(0.4, 0.8, n_samples),     # 铁水中Si含量(%)
        '铁水MN': np.random.uniform(0.2, 0.5, n_samples),     # 铁水中Mn含量(%)
        '铁水P': np.random.uniform(0.08, 0.15, n_samples),    # 铁水中P含量(%)
        '铁水C': np.random.uniform(4.0, 4.5, n_samples),      # 铁水中C含量(%)
        '废钢': np.random.uniform(5000, 15000, n_samples),    # 废钢加入量(kg)
        '钢水SI': np.random.uniform(0.02, 0.04, n_samples),   # 钢水最终Si含量(%)
        '钢水MN': np.random.uniform(0.05, 0.15, n_samples),   # 钢水最终Mn含量(%)
        '钢水P': np.random.uniform(0.01, 0.02, n_samples),    # 钢水最终P含量(%)
        '石灰': np.random.uniform(3000, 5000, n_samples),     # 石灰加入量(kg)
        '白云石': np.random.uniform(1000, 2000, n_samples),   # 白云石加入量(kg)
        '累氧实际': np.random.uniform(5000, 8000, n_samples), # 累计供氧量(Nm³)
        '吹氧时间s': np.random.uniform(800, 1200, n_samples), # 吹氧时间(s)
        '枪位': np.random.uniform(800, 1200, n_samples),      # 喷枪高度(mm)
        '最大角度': np.random.uniform(10, 20, n_samples),     # 最大枪角度(°)
        '间隔时间min': np.random.uniform(20, 40, n_samples),  # 间隔时间(min)
        '出钢温度': np.random.uniform(1600, 1680, n_samples)  # 出钢温度(℃)，目标变量
    }
    
    return pd.DataFrame(data)

def main():
    """主函数，演示增强的特征工程和模型训练流程。"""
    logger.info("启动钢水温度预测示例脚本")
    
    # 创建样本数据
    logger.info("创建样本数据...")
    sample_data = create_sample_data(n_samples=30)
    logger.info(f"创建了 {len(sample_data)} 条样本数据")
    
    # 打印样本数据的前几行
    logger.info("\n样本数据示例:")
    print(sample_data.head())
    
    # 展示数据统计
    logger.info("\n数据统计信息:")
    print(sample_data.describe().round(2))
    
    # 使用增强的特征工程模块
    logger.info("\n开始特征工程...")
    
    # 1. 先进行炉渣特征工程
    logger.info("添加炉渣特征...")
    df_with_slag = add_all_slag_features(sample_data)
    
    # 查看添加的炉渣特征列
    slag_features = [col for col in df_with_slag.columns if col.startswith('feature_slag_')]
    logger.info(f"添加了 {len(slag_features)} 个炉渣特征")
    
    # 2. 添加炉渣物理特性
    logger.info("添加炉渣相平衡特性...")
    df_with_phase = calculate_slag_phase_properties(df_with_slag)
    
    # 3. 添加炉渣粘度和网络结构特征
    logger.info("添加炉渣粘度和网络结构特征...")
    df_with_viscosity = calculate_viscosity_from_factsage(df_with_phase)
    
    # 4. 添加炉渣磷容量特征
    logger.info("添加炉渣磷容量特征...")
    df_with_pcap = calculate_phosphate_capacity(df_with_viscosity)
    
    # 5. 添加喷枪动态特征
    logger.info("添加喷枪动态特征...")
    df_with_lance = add_lance_dynamics_features(df_with_pcap)
    
    # 6. 添加炉渣-工艺交互特征
    logger.info("添加炉渣-工艺交互特征...")
    df_with_interactions = add_slag_process_interactions(df_with_lance)
    
    # 或者，使用综合特征工程函数
    logger.info("\n使用综合特征工程函数...")
    df_featured = engineer_all_features(
        sample_data,
        enable_slag=True,
        enable_process_params=True,
        enable_time_series=False,  # 示例数据中没有时间序列
        enable_interactions=True,
        enable_physicochem_props=True,
        enable_advanced_slag=True,
        enable_lance_dynamics=True
    )
    
    # 展示特征工程后的结果
    logger.info(f"\n特征工程后的列数: {len(df_featured.columns)}")
    
    # 打印添加的特征分类
    feature_types = {
        "炉渣特征": [col for col in df_featured.columns if col.startswith('feature_slag_')],
        "喷枪特征": [col for col in df_featured.columns if col.startswith('feature_lance_')],
        "交互特征": [col for col in df_featured.columns if col.startswith('feature_interact_')]
    }
    
    for feat_type, cols in feature_types.items():
        logger.info(f"{feat_type}: {len(cols)} 个")
        if cols:
            logger.info(f"  示例: {', '.join(cols[:3])}")
    
    # 为模型训练准备数据
    logger.info("\n准备模型训练数据...")
    
    # 分割特征和目标变量
    X = df_featured.select_dtypes(include=['float64', 'int64']).drop(columns=['出钢温度'], errors='ignore')
    y = df_featured['出钢温度']
    
    # 数据清洗 - 处理inf和极大值
    logger.info("清洗特征数据，处理inf和异常值...")
    
    # 1. 特殊处理极大值特征
    # 对磷容量系数取对数，它通常是一个非常大的值
    if 'feature_slag_phosphate_capacity' in X.columns:
        logger.info("对磷容量系数(feature_slag_phosphate_capacity)取对数，原始范围: "
                   f"{X['feature_slag_phosphate_capacity'].min():.2e}-{X['feature_slag_phosphate_capacity'].max():.2e}")
        # 先确保所有值都是正数，然后取对数
        X['feature_slag_phosphate_capacity'] = np.where(
            X['feature_slag_phosphate_capacity'] > 0,
            np.log10(X['feature_slag_phosphate_capacity']),
            0
        )
        logger.info(f"取对数后范围: {X['feature_slag_phosphate_capacity'].min():.2f}-{X['feature_slag_phosphate_capacity'].max():.2f}")
    
    # 检查并处理其他可能的极大值特征
    for col in X.columns:
        if X[col].max() > 1e20:  # 设置一个阈值来检测极大值
            logger.info(f"对特征 {col} 取对数，原始范围: {X[col].min():.2e}-{X[col].max():.2e}")
            # 对极大值特征取对数
            X[col] = np.where(X[col] > 0, np.log10(X[col]), 0)
            logger.info(f"取对数后范围: {X[col].min():.2f}-{X[col].max():.2f}")
    
    # 2. 替换inf和-inf
    X = X.replace([np.inf, -np.inf], np.nan)
    
    # 3. 处理缺失值
    # 对于缺失值，使用中位数填充
    X = X.fillna(X.median())
    
    # 4. 处理极大值
    # 对于每列，限制值在该列中位数+/-5倍标准差范围内
    for col in X.columns:
        median = X[col].median()
        std = X[col].std()
        upper_limit = median + 5 * std
        lower_limit = median - 5 * std
        X[col] = X[col].clip(lower_limit, upper_limit)
    
    # 5. 检查数据是否仍有问题
    # 显示每列的基本统计信息
    col_stats = X.describe().transpose()[['min', 'max', 'mean', 'std']]
    problematic_cols = col_stats[(col_stats['max'] > 1e10) | (col_stats['min'] < -1e10) | 
                                col_stats['std'].isna() | np.isinf(col_stats['std'])]
    
    if not problematic_cols.empty:
        logger.warning(f"检测到 {len(problematic_cols)} 个可能有问题的特征:")
        for idx, row in problematic_cols.iterrows():
            logger.warning(f"特征 {idx}: min={row['min']}, max={row['max']}, mean={row['mean']}, std={row['std']}")
            # 对有问题的特征进行更激进的处理 - 这里选择直接删除
            logger.warning(f"删除特征: {idx}")
            X = X.drop(columns=[idx])
    
    logger.info(f"数据清洗完成，特征数: {X.shape[1]}")
    
    # 分割训练集和测试集
    train_size = int(len(X) * 0.7)
    X_train, X_test = X.iloc[:train_size], X.iloc[train_size:]
    y_train, y_test = y.iloc[:train_size], y.iloc[train_size:]
    
    logger.info(f"训练集: {X_train.shape[0]} 样本, {X_train.shape[1]} 特征")
    logger.info(f"测试集: {X_test.shape[0]} 样本, {X_test.shape[1]} 特征")
    
    # 训练顺序思维模型
    logger.info("\n训练顺序思维模型...")
    
    # 配置超参数调优设置
    tune_base_models = {
        'xgboost': 5,     # 对XGBoost进行5次调优迭代
        'lightgbm': 5     # 对LightGBM进行5次调优迭代
    }
    
    # 训练模型
    model = train_sequential_thinking_model(
        X_train, y_train,
        X_val=X_test, y_val=y_test,  # 使用测试集作为验证集
        tune_base_model_n_iters=tune_base_models,
        tune_meta_model_n_iter=10
    )
    
    # 进行预测
    logger.info("\n进行预测和评估...")
    y_pred = model.predict(X_test)
    
    # 计算评估指标
    mae = np.mean(np.abs(y_test - y_pred))
    rmse = np.sqrt(np.mean((y_test - y_pred) ** 2))
    hit_rate_20 = np.mean(np.abs(y_test - y_pred) <= 20) * 100
    
    logger.info(f"平均绝对误差 (MAE): {mae:.2f}°C")
    logger.info(f"均方根误差 (RMSE): {rmse:.2f}°C")
    logger.info(f"命中率 (±20°C): {hit_rate_20:.2f}%")
    
    # 可视化结果
    plt.figure(figsize=(10, 6))
    plt.scatter(y_test, y_pred, alpha=0.7)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
    plt.xlabel('实际温度 (°C)')
    plt.ylabel('预测温度 (°C)')
    plt.title('温度预测结果对比')
    plt.grid(alpha=0.3)
    
    # 保存图像
    plt.savefig('temperature_prediction_results.png', dpi=300, bbox_inches='tight')
    logger.info("结果可视化已保存到 'temperature_prediction_results.png'")
    
    logger.info("\n钢水温度预测演示完成")

if __name__ == "__main__":
    main() 