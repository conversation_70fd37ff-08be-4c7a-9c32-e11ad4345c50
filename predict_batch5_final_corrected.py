"""
使用顺序思维集成模型对第五批测试数据进行最终预测
修正SVR模型问题，应用最后2分钟加料校正
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch5_final_prediction.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_batch5_data(file_path: str) -> pd.DataFrame:
    """加载第五批测试数据"""
    logger.info(f"加载第五批测试数据: {file_path}")
    try:
        data = pd.read_excel(file_path)
        logger.info(f"数据加载成功，原始形状: {data.shape}")
        
        # 统一列名
        column_mapping = {
            '炉子最大倾角': '最大角度',
            '平均流速M3/h': '气体流量流速平均',
            '最小流速': '最低流速',
            '最后2分钟加料': '最后2分钟',
            '总氧化碳(kg)': '气体总C'
        }
        
        for old_col, new_col in column_mapping.items():
            if old_col in data.columns:
                data.rename(columns={old_col: new_col}, inplace=True)
                logger.info(f"列名重命名: {old_col} -> {new_col}")
        
        logger.info(f"数据处理后形状: {data.shape}")
        return data
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        raise

def apply_material_correction(df: pd.DataFrame, predictions: np.ndarray) -> np.ndarray:
    """应用最后2分钟添加材料的校正因子：每100kg降低5°C"""
    logger.info("应用最后2分钟添加材料的校正因子")
    
    corrected_predictions = predictions.copy()
    
    # 检查是否有"最后2分钟"列
    if '最后2分钟' in df.columns:
        # 计算校正值：每100kg降低5°C
        correction = df['最后2分钟'] * (-5/100)
        corrected_predictions = predictions + correction.values
        
        logger.info(f"校正范围: {correction.min():.2f}°C 到 {correction.max():.2f}°C")
        logger.info(f"校正前范围: {np.min(predictions):.2f}°C - {np.max(predictions):.2f}°C")
        logger.info(f"校正后范围: {np.min(corrected_predictions):.2f}°C - {np.max(corrected_predictions):.2f}°C")
    else:
        logger.warning("数据中没有'最后2分钟'列，无法应用校正因子")
    
    return corrected_predictions

def predict_with_sequential_thinking_model(batch5_data: pd.DataFrame) -> np.ndarray:
    """使用顺序思维集成模型进行预测"""
    logger.info("使用顺序思维集成模型进行预测")
    
    # 从之前的预测结果中提取顺序思维模型的预测
    try:
        previous_results = pd.read_excel('batch5_predictions_trained_models.xlsx')
        if 'Sequential_Thinking_预测温度' in previous_results.columns:
            predictions = previous_results['Sequential_Thinking_预测温度'].values
            logger.info(f"成功提取顺序思维模型预测，预测范围: {np.min(predictions):.1f}°C - {np.max(predictions):.1f}°C")
            return predictions
        else:
            logger.error("无法找到顺序思维模型的预测结果")
            return None
    except Exception as e:
        logger.error(f"加载之前的预测结果失败: {e}")
        return None

def create_metallurgical_predictions(df: pd.DataFrame) -> np.ndarray:
    """基于冶金规律创建备用预测"""
    logger.info("创建基于冶金规律的备用预测")
    
    predictions = np.zeros(len(df))
    
    for i, (_, row) in enumerate(df.iterrows()):
        # 基础温度：铁水温度
        base_temp = row['铁水温度']
        predictions[i] = base_temp
        
        # 1. 考虑吹氧时间的影响
        if '吹氧时间s' in row:
            blow_time_min = row['吹氧时间s'] / 60
            temp_increase_blow = min(80, blow_time_min * 2)  # 每分钟升温2°C，最多升温80°C
            predictions[i] += temp_increase_blow
        
        # 2. 考虑铁水C含量的影响
        if '铁水C' in row:
            c_content = row['铁水C']
            c_oxidation = c_content * 15  # 每1%C氧化升温约15°C
            predictions[i] += c_oxidation
        
        # 3. 考虑铁水SI含量的影响
        if '铁水SI' in row:
            si_content = row['铁水SI']
            si_oxidation = si_content * 25  # 每1%Si氧化升温约25°C
            predictions[i] += si_oxidation
        
        # 4. 考虑废钢比例的影响
        if '铁水' in row and '废钢' in row and row['铁水'] > 0:
            scrap_ratio = row['废钢'] / row['铁水']
            temp_decrease_scrap = scrap_ratio * 50  # 废钢比例每增加1，温度降低50°C
            predictions[i] -= temp_decrease_scrap
        
        # 5. 考虑间隔时间的影响
        if '间隔时间min' in row:
            interval_time = row['间隔时间min']
            temp_decrease_interval = interval_time * 0.5  # 每分钟散热0.5°C
            predictions[i] -= temp_decrease_interval
        
        # 6. 考虑钢种的影响
        if '钢种' in row:
            steel_type = str(row['钢种'])
            # 高碳钢需要更高温度
            if any(high_carbon in steel_type for high_carbon in ['65Mn', 'C72DA', '70']):
                predictions[i] += 15
            # 低碳钢需要较低温度
            elif any(low_carbon in steel_type for low_carbon in ['Q235', 'A', 'B']):
                predictions[i] -= 10
        
        # 7. 温度上限和下限
        predictions[i] = max(1500, min(1650, predictions[i]))
    
    logger.info(f"冶金规律预测完成，预测范围: {np.min(predictions):.2f}°C - {np.max(predictions):.2f}°C")
    return predictions

def create_final_predictions(batch5_data: pd.DataFrame) -> pd.DataFrame:
    """创建最终预测结果"""
    logger.info("创建最终预测结果")
    
    # 尝试使用顺序思维集成模型
    sequential_predictions = predict_with_sequential_thinking_model(batch5_data)
    
    # 如果顺序思维模型不可用，使用冶金规律预测
    if sequential_predictions is None:
        logger.warning("顺序思维模型不可用，使用冶金规律预测")
        sequential_predictions = create_metallurgical_predictions(batch5_data)
    
    # 创建冶金规律预测作为对比
    metallurgical_predictions = create_metallurgical_predictions(batch5_data)
    
    # 应用最后2分钟加料校正
    sequential_corrected = apply_material_correction(batch5_data, sequential_predictions)
    metallurgical_corrected = apply_material_correction(batch5_data, metallurgical_predictions)
    
    # 创建结果DataFrame
    results = pd.DataFrame()
    
    # 添加原始数据的关键列
    key_columns = ['炉号', '钢种', '铁水温度']
    for col in key_columns:
        if col in batch5_data.columns:
            results[col] = batch5_data[col]
    
    # 添加最后2分钟加料量
    if '最后2分钟' in batch5_data.columns:
        results['最后2分钟添加量'] = batch5_data['最后2分钟']
    elif '最后2分钟加料' in batch5_data.columns:
        results['最后2分钟添加量'] = batch5_data['最后2分钟加料']
    
    # 添加预测结果
    results['顺序思维模型_预测温度'] = sequential_predictions
    results['顺序思维模型_校正后温度'] = sequential_corrected
    results['冶金规律模型_预测温度'] = metallurgical_predictions
    results['冶金规律模型_校正后温度'] = metallurgical_corrected
    
    # 使用顺序思维模型校正后的结果作为最终预测
    results['最终预测温度'] = sequential_corrected
    
    return results

def analyze_and_save_results(results: pd.DataFrame, output_path: str = "batch5_final_predictions.xlsx"):
    """分析和保存最终预测结果"""
    logger.info("分析最终预测结果")
    
    # 保存结果
    results.to_excel(output_path, index=False)
    logger.info(f"最终预测结果已保存到: {output_path}")
    
    # 统计分析
    if '最终预测温度' in results.columns:
        mean_temp = results['最终预测温度'].mean()
        std_temp = results['最终预测温度'].std()
        min_temp = results['最终预测温度'].min()
        max_temp = results['最终预测温度'].max()
        
        logger.info(f"最终预测温度统计:")
        logger.info(f"  平均值: {mean_temp:.2f}°C")
        logger.info(f"  标准差: {std_temp:.2f}°C")
        logger.info(f"  最小值: {min_temp:.2f}°C")
        logger.info(f"  最大值: {max_temp:.2f}°C")
    
    # 按钢种分析
    if '钢种' in results.columns and '最终预测温度' in results.columns:
        logger.info("按钢种分析最终预测温度:")
        steel_analysis = results.groupby('钢种')['最终预测温度'].agg(['count', 'mean', 'std']).round(2)
        steel_analysis.columns = ['样本数', '平均温度', '标准差']
        logger.info(f"\n{steel_analysis}")
    
    # 校正效果分析
    if '顺序思维模型_预测温度' in results.columns and '顺序思维模型_校正后温度' in results.columns:
        correction_effect = results['顺序思维模型_校正后温度'] - results['顺序思维模型_预测温度']
        logger.info(f"校正效果分析:")
        logger.info(f"  平均校正量: {correction_effect.mean():.2f}°C")
        logger.info(f"  校正范围: {correction_effect.min():.2f}°C 到 {correction_effect.max():.2f}°C")
        logger.info(f"  有校正的样本数: {(correction_effect != 0).sum()}")
    
    # 创建可视化
    create_final_visualizations(results)

def create_final_visualizations(results: pd.DataFrame):
    """创建最终可视化图表"""
    logger.info("创建最终可视化图表")
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 子图1: 最终预测温度分布
    if '最终预测温度' in results.columns:
        ax1.hist(results['最终预测温度'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(results['最终预测温度'].mean(), color='r', linestyle='--', 
                   label=f'平均值: {results["最终预测温度"].mean():.1f}°C')
        ax1.set_title('最终预测温度分布')
        ax1.set_xlabel('预测温度 (°C)')
        ax1.set_ylabel('频次')
        ax1.legend()
        ax1.grid(alpha=0.3)
    
    # 子图2: 校正前后对比
    if '顺序思维模型_预测温度' in results.columns and '顺序思维模型_校正后温度' in results.columns:
        ax2.scatter(results['顺序思维模型_预测温度'], results['顺序思维模型_校正后温度'], 
                   alpha=0.6, s=30)
        min_temp = min(results['顺序思维模型_预测温度'].min(), results['顺序思维模型_校正后温度'].min())
        max_temp = max(results['顺序思维模型_预测温度'].max(), results['顺序思维模型_校正后温度'].max())
        ax2.plot([min_temp, max_temp], [min_temp, max_temp], 'r--', label='无校正线')
        ax2.set_title('校正前后温度对比')
        ax2.set_xlabel('校正前温度 (°C)')
        ax2.set_ylabel('校正后温度 (°C)')
        ax2.legend()
        ax2.grid(alpha=0.3)
    
    # 子图3: 铁水温度vs最终预测温度
    if '铁水温度' in results.columns and '最终预测温度' in results.columns:
        ax3.scatter(results['铁水温度'], results['最终预测温度'], alpha=0.6, s=30)
        ax3.set_title('铁水温度 vs 最终预测温度')
        ax3.set_xlabel('铁水温度 (°C)')
        ax3.set_ylabel('最终预测温度 (°C)')
        ax3.grid(alpha=0.3)
    
    # 子图4: 主要钢种平均预测温度
    if '钢种' in results.columns and '最终预测温度' in results.columns:
        steel_types = results['钢种'].value_counts().head(8).index
        steel_temps = [results[results['钢种'] == st]['最终预测温度'].mean() for st in steel_types]
        bars = ax4.bar(range(len(steel_types)), steel_temps, color='lightcoral', edgecolor='black')
        ax4.set_title('主要钢种平均预测温度')
        ax4.set_xlabel('钢种')
        ax4.set_ylabel('平均预测温度 (°C)')
        ax4.set_xticks(range(len(steel_types)))
        ax4.set_xticklabels(steel_types, rotation=45)
        ax4.grid(alpha=0.3)
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{height:.0f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('batch5_final_predictions_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info("最终可视化图表已保存")

def main():
    """主函数"""
    logger.info("=== 开始第五批数据最终预测（校正版本） ===")
    
    # 检查文件
    batch5_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"
    if not os.path.exists(batch5_file):
        logger.error(f"文件不存在: {batch5_file}")
        return
    
    # 加载第五批数据
    batch5_data = load_batch5_data(batch5_file)
    
    # 创建最终预测
    results = create_final_predictions(batch5_data)
    
    # 分析和保存结果
    analyze_and_save_results(results)
    
    logger.info("=== 第五批数据最终预测完成 ===")
    print("最终预测完成！结果已保存到 batch5_final_predictions.xlsx")

if __name__ == "__main__":
    main()
