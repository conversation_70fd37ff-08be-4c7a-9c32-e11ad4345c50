import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
import numpy as np

# 定义日志打印函数
def log_print(message):
    print(f"[INFO] {message}")

# 1. 加载数据
excel_file_path = 'e:\\专业提升命中率20250516\\4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx'
log_print(f"Loading data from {excel_file_path}")
try:
    df = pd.read_excel(excel_file_path, sheet_name=0)
    log_print("Data loaded successfully.")
except FileNotFoundError:
    log_print(f"Error: The file {excel_file_path} was not found.")
    exit()
except Exception as e:
    log_print(f"An error occurred while reading the Excel file: {e}")
    exit()

# 打印原始数据信息
log_print("Original DataFrame head:")
print(df.head())
log_print("Original DataFrame info:")
df.info()

# 2. 数据清洗与预处理
log_print("Starting data cleaning and preprocessing...")

# 目标变量
target_column = 'Unnamed: 32'
if target_column not in df.columns:
    log_print(f"Error: Target column '{target_column}' not found in the data.")
    # 尝试寻找可能的列名，例如包含“温度”的最后一列
    potential_targets = [col for col in df.columns if '温度' in col]
    if potential_targets:
        log_print(f"Potential target columns found: {potential_targets}. Please verify.")
    exit()

# 处理时间列: '冶炼开始时刻', '冶炼结束时刻'
for col in ['冶炼开始时刻', '冶炼结束时刻']:
    if col in df.columns:
        df[col] = pd.to_datetime(df[col], errors='coerce') # errors='coerce' 会将无法解析的转为NaT

# 特征工程: 计算冶炼时长 (分钟)
if '冶炼开始时刻' in df.columns and '冶炼结束时刻' in df.columns:
    df['冶炼时长_分钟'] = (df['冶炼结束时刻'] - df['冶炼开始时刻']).dt.total_seconds() / 60
    log_print("Created feature: '冶炼时长_分钟'")

# 选择数值类型的特征列进行填充
numeric_cols = df.select_dtypes(include=np.number).columns.tolist()

# 移除目标变量和ID列（如果存在）以避免在特征中错误使用
features_to_fill = [col for col in numeric_cols if col != target_column and '炉号' not in col.lower() and 'id' not in col.lower()]

log_print(f"Numeric columns to fill NaN: {features_to_fill}")

for col in features_to_fill:
    if df[col].isnull().any():
        median_val = df[col].median()
        df[col].fillna(median_val, inplace=True)
        log_print(f"Filled NaN in '{col}' with median value: {median_val:.2f}")

# 删除目标变量中包含NaN的行，因为无法用于训练或预测
initial_rows = len(df)
df.dropna(subset=[target_column], inplace=True)
if len(df) < initial_rows:
    log_print(f"Dropped {initial_rows - len(df)} rows due to NaN in target column '{target_column}'.")

# 选择特征 (X) 和目标 (y)
# 排除非数值列和原始时间列，以及ID类列
X_columns = df.select_dtypes(include=np.number).columns.tolist()
X_columns = [col for col in X_columns if col not in [target_column, '炉号', '冶炼开始时刻', '冶炼结束时刻'] and 'id' not in col.lower()]

# 确保 '冶炼时长_分钟' 如果被创建了，则包含在内
if '冶炼时长_分钟' in df.columns and '冶炼时长_分钟' not in X_columns:
    X_columns.append('冶炼时长_分钟')

# 再次检查X_columns中是否有非数值列（不太可能，但作为保险）
X_columns = [col for col in X_columns if df[col].dtype in [np.number, 'float64', 'int64']]

if not X_columns:
    log_print("Error: No valid feature columns found after preprocessing.")
    exit()

log_print(f"Selected features for model: {X_columns}")
X = df[X_columns]
y = df[target_column]

# 删除所有值都为NaN的列
df.dropna(axis=1, how='all', inplace=True)
log_print(f"Columns after dropping all-NaN columns: {df.columns.tolist()}")

# 重新选择数值类型的特征列进行填充，因为列可能已改变
numeric_cols = df.select_dtypes(include=np.number).columns.tolist()
features_to_fill = [col for col in numeric_cols if col != target_column and '炉号' not in col.lower() and 'id' not in col.lower()]

log_print(f"Numeric columns to fill NaN (after dropping all-NaN cols): {features_to_fill}")

for col in features_to_fill:
    if df[col].isnull().any():
        median_val = df[col].median()
        df[col].fillna(median_val, inplace=True)
        log_print(f"Filled NaN in '{col}' with median value: {median_val:.2f}")

# 重新选择特征 (X) 和目标 (y) 因为df的列可能已经改变
X_columns = df.select_dtypes(include=np.number).columns.tolist()
X_columns = [col for col in X_columns if col not in [target_column, '炉号', '冶炼开始时刻', '冶炼结束时刻'] and 'id' not in col.lower()]
if '冶炼时长_分钟' in df.columns and '冶炼时长_分钟' not in X_columns:
    X_columns.append('冶炼时长_分钟')
X_columns = [col for col in X_columns if df[col].dtype in [np.number, 'float64', 'int64']]

if not X_columns:
    log_print("Error: No valid feature columns found after preprocessing and dropping all-NaN columns.")
    exit()

log_print(f"Selected features for model (after dropping all-NaN cols): {X_columns}")
X = df[X_columns]
y = df[target_column]

# 再次检查X中是否有NaN值，这可能发生在特征工程之后或某些列未被正确处理
if X.isnull().values.any():
    log_print("Warning: NaN values found in features (X) after initial filling. Attempting to fill again.")
    for col in X.columns:
        if X[col].isnull().any():
            median_val = X[col].median()
            X[col].fillna(median_val, inplace=True)
            log_print(f"Filled NaN in feature '{col}' with median: {median_val:.2f}")
    if X.isnull().values.any():
        log_print("Error: NaN values still present in features (X) after second fill. Please check data.")
        # 打印出包含NaN的列
        nan_cols = X.columns[X.isnull().any()].tolist()
        log_print(f"Columns with NaN: {nan_cols}")
        for col in nan_cols:
            print(f"NaN count in {col}: {X[col].isnull().sum()}")
        exit()

log_print("Data cleaning and preprocessing finished.")

# 3. 模型选择与训练
# 由于这是“测试数据”，我们假设目标是进行预测。如果需要评估，我们会划分训练/测试集。
# 这里我们用全部数据训练一个模型，然后对这些数据进行“预测”以展示流程。
# 在实际场景中，应该用一个已经训练好的模型来预测新数据。
log_print("Initializing Random Forest Regressor model...")
model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)

log_print("Training the model...")
try:
    model.fit(X, y)
    log_print("Model training completed.")
except Exception as e:
    log_print(f"Error during model training: {e}")
    exit()

# 4. 预测
log_print("Making predictions...")
predictions = model.predict(X)

# 5. 输出结果
df['预测出钢温度'] = predictions

log_print("\nPredictions vs Actuals (first 10 rows):")
print(df[[target_column, '预测出钢温度']].head(10))

# 评估（在同一数据集上，仅作参考）
mse = mean_squared_error(y, predictions)
r2 = r2_score(y, predictions)
log_print(f"\nModel Performance (on the same data used for training/prediction):")
log_print(f"Mean Squared Error: {mse:.2f}")
log_print(f"R-squared: {r2:.2f}")

# 保存包含预测结果的Excel文件
output_excel_path = 'e:\\专业提升命中率20250516\\predictions_batch5.xlsx'
try:
    df.to_excel(output_excel_path, index=False)
    log_print(f"Predictions saved to {output_excel_path}")
except Exception as e:
    log_print(f"Error saving predictions to Excel: {e}")

log_print("Script finished.")