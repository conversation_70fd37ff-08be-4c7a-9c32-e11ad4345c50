"""
基于数据分析报告的阶段性优化钢水温度预测模型
Stage 1: 数据清理和基础特征工程（预期提升5-15%）
Stage 2: 高级特征工程（预期提升10-20%）
Stage 3: 高级建模（预期提升5-10%）

目标：将±20°C命中率从当前62%提升到90-95%
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso
from sklearn.svm import SVR
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("stage_optimization.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Stage1DataCleaner:
    """Stage 1: 数据清理和基础特征工程"""

    def __init__(self):
        self.steel_type_groups = {
            '低碳钢': ['Q235', 'Q345', 'Q355', 'Q245', 'Q420', 'Q460', 'A', 'B', 'C', 'D'],
            '中碳钢': ['20Mn2A', '35', '45', '50', '55', '60'],
            '高碳钢': ['65Mn', '70', '75', '80', '85', 'C72DA'],
            '合金钢': ['16Mn', '20CrMo', '40Cr', 'ER50', 'ER70', 'H08A', 'H08MnA']
        }

    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据清理"""
        logger.info("开始Stage 1数据清理")

        # 1. 处理铁水C的异常值
        df_cleaned = df.copy()

        # 铁水C异常值处理（>5%的记录）
        c_mask = df_cleaned['铁水C'] > 5.0
        logger.info(f"发现{c_mask.sum()}个铁水C异常值(>5%)")
        df_cleaned.loc[c_mask, '铁水C'] = np.clip(df_cleaned.loc[c_mask, '铁水C'], 3.5, 5.0)

        # 2. 处理缺失值
        numeric_columns = df_cleaned.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if df_cleaned[col].isnull().sum() > 0:
                median_val = df_cleaned[col].median()
                df_cleaned[col].fillna(median_val, inplace=True)
                logger.info(f"填充{col}的{df_cleaned[col].isnull().sum()}个缺失值")

        # 3. 处理异常温度值
        temp_mask = df_cleaned['钢水温度'] > 1700
        logger.info(f"发现{temp_mask.sum()}个高温异常值(>1700°C)")

        # 4. 标准化气体流速数据
        if '气体流量流速平均' in df_cleaned.columns:
            # 假设单位不一致，进行标准化
            flow_rate = df_cleaned['气体流量流速平均']
            # 将异常大的值缩放
            df_cleaned.loc[flow_rate > 10000, '气体流量流速平均'] = flow_rate / 1000

        logger.info(f"数据清理完成，处理后数据形状: {df_cleaned.shape}")
        return df_cleaned

    def group_steel_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """钢种分组"""
        logger.info("开始钢种分组")

        df_grouped = df.copy()
        df_grouped['钢种分组'] = '其他'

        for group_name, steel_types in self.steel_type_groups.items():
            for steel_type in steel_types:
                mask = df_grouped['钢种'].str.contains(steel_type, na=False)
                df_grouped.loc[mask, '钢种分组'] = group_name

        # 统计分组结果
        group_counts = df_grouped['钢种分组'].value_counts()
        logger.info(f"钢种分组结果:\n{group_counts}")

        return df_grouped

    def create_basic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建基础特征"""
        logger.info("开始创建基础特征")

        df_features = df.copy()

        # 1. 氧气强度特征
        if '累氧实际' in df_features.columns and '吹氧时间s' in df_features.columns:
            df_features['氧气强度'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60)  # Nm³/min

        # 2. 造渣料配比特征
        slag_materials = ['石灰', '白云石', '破碎料', '烧结返矿', '石灰石']
        available_materials = [col for col in slag_materials if col in df_features.columns]

        if available_materials:
            # 确保造渣料列都是数值类型
            for material in available_materials:
                df_features[material] = pd.to_numeric(df_features[material], errors='coerce').fillna(0)

            df_features['总造渣料'] = df_features[available_materials].sum(axis=1)
            df_features['造渣料比'] = df_features['总造渣料'] / df_features['铁水']

            # 各种造渣料比例
            for material in available_materials:
                df_features[f'{material}_比例'] = df_features[material] / (df_features['总造渣料'] + 1e-6)

        # 3. 废钢比例
        if '废钢' in df_features.columns and '铁水' in df_features.columns:
            df_features['废钢比'] = df_features['废钢'] / df_features['铁水']

        # 4. 单位铁水供氧量
        if '累氧实际' in df_features.columns:
            df_features['单位铁水供氧量'] = df_features['累氧实际'] / df_features['铁水']

        # 5. 吹氧强度
        if '吹氧时间s' in df_features.columns:
            df_features['吹氧强度'] = df_features['累氧实际'] / df_features['吹氧时间s'] * 60  # Nm³/min

        logger.info(f"基础特征创建完成，新增特征数: {df_features.shape[1] - df.shape[1]}")
        return df_features

class Stage2SlagFeatureEngineer:
    """Stage 2: 高级特征工程（包含炉渣模型特征）"""

    def __init__(self):
        # 分子量
        self.molecular_weights = {
            'Ca': 40.08, 'CaO': 56.08, 'Si': 28.09, 'SiO2': 60.08,
            'Fe': 55.85, 'FeO': 71.85, 'Mg': 24.31, 'MgO': 40.30,
            'Mn': 54.94, 'MnO': 70.94, 'P': 30.97, 'P2O5': 141.94
        }

        # 氧化反应系数
        self.oxidation_coeffs = {
            'Si_to_SiO2': 2.14, 'Mn_to_MnO': 1.29,
            'P_to_P2O5': 2.29, 'Fe_to_FeO': 1.29
        }

        # 造渣材料成分
        self.flux_compositions = {
            'lime': {'CaO': 0.88, 'MgO': 0.02, 'SiO2': 0.03},
            'dolomite': {'CaO': 0.32, 'MgO': 0.20, 'SiO2': 0.02},
            'limestone': {'CaO': 0.52, 'MgO': 0.02, 'SiO2': 0.04}
        }

        # 反应热数据（kJ/kg）
        self.reaction_heats = {
            'C_to_CO': 10100, 'C_to_CO2': 32800,
            'Si_oxidation': 30800, 'Mn_oxidation': 7200,
            'P_oxidation': 24000, 'Fe_oxidation': 4800
        }

    def create_slag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建炉渣模型特征"""
        logger.info("开始创建炉渣模型特征")

        df_slag = df.copy()

        # 初始化炉渣特征列
        slag_features = [
            'slag_CaO_percent', 'slag_SiO2_percent', 'slag_FeO_percent',
            'slag_MgO_percent', 'slag_MnO_percent', 'slag_P2O5_percent',
            'slag_basicity', 'slag_rate', 'slag_iron_oxides_percent'
        ]

        for feature in slag_features:
            df_slag[feature] = 0.0

        # 计算各氧化物含量
        for idx, row in df_slag.iterrows():
            try:
                # 基础数据
                hot_metal_mass = row['铁水'] * 1000  # kg

                # 氧化产物计算
                si_oxidized = hot_metal_mass * row['铁水SI'] * 0.95 / 100
                sio2_from_si = si_oxidized * self.oxidation_coeffs['Si_to_SiO2']

                mn_oxidized = hot_metal_mass * row['铁水MN'] * 0.80 / 100
                mno_from_mn = mn_oxidized * self.oxidation_coeffs['Mn_to_MnO']

                p_oxidized = hot_metal_mass * row['铁水P'] * 0.85 / 100
                p2o5_from_p = p_oxidized * self.oxidation_coeffs['P_to_P2O5']

                fe_loss = hot_metal_mass * 0.015  # 1.5%铁损
                feo_from_fe = fe_loss * self.oxidation_coeffs['Fe_to_FeO']

                # 造渣材料贡献
                lime_mass = row.get('石灰', 0)
                dolomite_mass = row.get('白云石', 0)
                limestone_mass = row.get('石灰石', 0)

                # CaO计算
                cao_from_lime = lime_mass * self.flux_compositions['lime']['CaO']
                cao_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['CaO']
                cao_from_limestone = limestone_mass * self.flux_compositions['limestone']['CaO']
                total_cao = cao_from_lime + cao_from_dolomite + cao_from_limestone

                # MgO计算
                mgo_from_lime = lime_mass * self.flux_compositions['lime']['MgO']
                mgo_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['MgO']
                mgo_from_limestone = limestone_mass * self.flux_compositions['limestone']['MgO']
                mgo_from_refractory = hot_metal_mass * 0.15 / 1000  # 耐火材料贡献
                total_mgo = mgo_from_lime + mgo_from_dolomite + mgo_from_limestone + mgo_from_refractory

                # SiO2计算
                sio2_from_flux = (lime_mass * self.flux_compositions['lime']['SiO2'] +
                                 dolomite_mass * self.flux_compositions['dolomite']['SiO2'] +
                                 limestone_mass * self.flux_compositions['limestone']['SiO2'])
                sio2_from_refractory = hot_metal_mass * 0.08 / 1000
                total_sio2 = sio2_from_si + sio2_from_flux + sio2_from_refractory

                # 总炉渣量
                total_slag = total_cao + total_sio2 + feo_from_fe + total_mgo + mno_from_mn + p2o5_from_p

                if total_slag > 0:
                    # 炉渣成分百分比
                    df_slag.loc[idx, 'slag_CaO_percent'] = total_cao / total_slag * 100
                    df_slag.loc[idx, 'slag_SiO2_percent'] = total_sio2 / total_slag * 100
                    df_slag.loc[idx, 'slag_FeO_percent'] = feo_from_fe / total_slag * 100
                    df_slag.loc[idx, 'slag_MgO_percent'] = total_mgo / total_slag * 100
                    df_slag.loc[idx, 'slag_MnO_percent'] = mno_from_mn / total_slag * 100
                    df_slag.loc[idx, 'slag_P2O5_percent'] = p2o5_from_p / total_slag * 100

                    # 碱度
                    df_slag.loc[idx, 'slag_basicity'] = total_cao / total_sio2 if total_sio2 > 0 else 2.8

                    # 炉渣率
                    df_slag.loc[idx, 'slag_rate'] = total_slag / hot_metal_mass * 100

                    # 铁氧化物含量
                    df_slag.loc[idx, 'slag_iron_oxides_percent'] = feo_from_fe / total_slag * 100
                else:
                    # 默认值
                    df_slag.loc[idx, 'slag_CaO_percent'] = 45.0
                    df_slag.loc[idx, 'slag_SiO2_percent'] = 16.0
                    df_slag.loc[idx, 'slag_FeO_percent'] = 20.0
                    df_slag.loc[idx, 'slag_MgO_percent'] = 10.0
                    df_slag.loc[idx, 'slag_MnO_percent'] = 7.0
                    df_slag.loc[idx, 'slag_P2O5_percent'] = 2.0
                    df_slag.loc[idx, 'slag_basicity'] = 2.8
                    df_slag.loc[idx, 'slag_rate'] = 8.0
                    df_slag.loc[idx, 'slag_iron_oxides_percent'] = 20.0

            except Exception as e:
                logger.warning(f"计算第{idx}行炉渣特征时出错: {e}")
                continue

        logger.info("炉渣模型特征创建完成")
        return df_slag

    def create_thermal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建动态热平衡特征"""
        logger.info("开始创建动态热平衡特征")

        df_thermal = df.copy()

        # 初始化热平衡特征列
        thermal_features = [
            'hot_metal_sensible_heat', 'scrap_heating_heat', 'total_oxidation_heat',
            'decarb_heat', 'si_oxidation_heat', 'mn_oxidation_heat', 'p_oxidation_heat',
            'net_heat_balance', 'theoretical_temp_rise', 'theoretical_end_temp'
        ]

        for feature in thermal_features:
            df_thermal[feature] = 0.0

        for idx, row in df_thermal.iterrows():
            try:
                # 基础数据
                hot_metal_mass = row['铁水']
                scrap_mass = row.get('废钢', 0)
                hot_metal_temp = row['铁水温度']

                # 铁水显热（以1500°C为基准）
                cp_iron = 0.75  # kJ/kg·K
                df_thermal.loc[idx, 'hot_metal_sensible_heat'] = (
                    cp_iron * hot_metal_mass * 1000 * (hot_metal_temp - 1500)
                )

                # 废钢加热热量（目标温度1600°C，环境温度25°C）
                cp_steel = 0.65  # kJ/kg·K
                melting_heat = 1200  # kJ/kg
                df_thermal.loc[idx, 'scrap_heating_heat'] = (
                    scrap_mass * 1000 * (cp_steel * (1600 - 25) + melting_heat)
                )

                # 氧化反应热
                c_oxidized = hot_metal_mass * row['铁水C'] * 0.88 / 100  # 88%脱碳率
                si_oxidized = hot_metal_mass * row['铁水SI'] * 0.95 / 100
                mn_oxidized = hot_metal_mass * row['铁水MN'] * 0.80 / 100
                p_oxidized = hot_metal_mass * row['铁水P'] * 0.85 / 100

                # 考虑CO/CO2比例（70%CO, 30%CO2）
                co_ratio = 0.7
                co2_ratio = 0.3

                decarb_heat = (c_oxidized * co_ratio * self.reaction_heats['C_to_CO'] +
                              c_oxidized * co2_ratio * self.reaction_heats['C_to_CO2'])

                si_heat = si_oxidized * self.reaction_heats['Si_oxidation']
                mn_heat = mn_oxidized * self.reaction_heats['Mn_oxidation']
                p_heat = p_oxidized * self.reaction_heats['P_oxidation']

                df_thermal.loc[idx, 'total_oxidation_heat'] = decarb_heat + si_heat + mn_heat + p_heat
                df_thermal.loc[idx, 'decarb_heat'] = decarb_heat
                df_thermal.loc[idx, 'si_oxidation_heat'] = si_heat
                df_thermal.loc[idx, 'mn_oxidation_heat'] = mn_heat
                df_thermal.loc[idx, 'p_oxidation_heat'] = p_heat

                # 净热量平衡
                df_thermal.loc[idx, 'net_heat_balance'] = (
                    df_thermal.loc[idx, 'total_oxidation_heat'] -
                    df_thermal.loc[idx, 'scrap_heating_heat']
                )

                # 理论温升
                total_steel = (hot_metal_mass + scrap_mass) * 1000
                if total_steel > 0:
                    df_thermal.loc[idx, 'theoretical_temp_rise'] = (
                        df_thermal.loc[idx, 'net_heat_balance'] / (total_steel * cp_iron)
                    )
                else:
                    df_thermal.loc[idx, 'theoretical_temp_rise'] = 0

                # 理论终点温度
                df_thermal.loc[idx, 'theoretical_end_temp'] = (
                    hot_metal_temp + df_thermal.loc[idx, 'theoretical_temp_rise']
                )

            except Exception as e:
                logger.warning(f"计算第{idx}行热平衡特征时出错: {e}")
                continue

        logger.info("动态热平衡特征创建完成")
        return df_thermal

    def create_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建交互特征"""
        logger.info("开始创建交互特征")

        df_interaction = df.copy()

        # 重要的交互特征
        interaction_pairs = [
            ('铁水C', '单位铁水供氧量'),
            ('铁水SI', 'slag_basicity'),
            ('废钢比', '铁水温度'),
            ('吹氧强度', '总造渣料'),
            ('slag_FeO_percent', 'theoretical_temp_rise'),
            ('slag_CaO_percent', 'slag_SiO2_percent')
        ]

        for feature1, feature2 in interaction_pairs:
            if feature1 in df_interaction.columns and feature2 in df_interaction.columns:
                # 乘积交互
                df_interaction[f'{feature1}_x_{feature2}'] = (
                    df_interaction[feature1] * df_interaction[feature2]
                )

                # 比值交互（避免除零）
                df_interaction[f'{feature1}_div_{feature2}'] = (
                    df_interaction[feature1] / (df_interaction[feature2] + 1e-6)
                )

        logger.info(f"交互特征创建完成，新增特征数: {len(interaction_pairs) * 2}")
        return df_interaction

class Stage3AdvancedModeling:
    """Stage 3: 高级建模"""

    def __init__(self):
        self.models = {}
        self.feature_importance = {}
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()

    def prepare_features(self, df: pd.DataFrame, target_col: str = '钢水温度') -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """准备建模特征"""
        logger.info("开始准备建模特征")

        # 分离特征和目标
        feature_cols = [col for col in df.columns if col not in [
            target_col, '炉号', '钢种', '出钢重量估算'
        ]]

        X = df[feature_cols].copy()
        y = df[target_col].copy()

        # 处理分类特征
        categorical_cols = X.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            if col in X.columns:
                X[col] = self.label_encoder.fit_transform(X[col].astype(str))

        # 处理无穷大和NaN值
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(X.median())

        # 特征缩放
        X_scaled = self.scaler.fit_transform(X)

        logger.info(f"特征准备完成，特征数: {X_scaled.shape[1]}, 样本数: {X_scaled.shape[0]}")
        return X_scaled, y.values, feature_cols

    def train_models(self, X: np.ndarray, y: np.ndarray, feature_names: List[str]) -> Dict[str, Any]:
        """训练多个模型"""
        logger.info("开始训练高级模型")

        # 分割训练和验证集
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)

        # 定义模型
        models_config = {
            'XGBoost': xgb.XGBRegressor(
                n_estimators=500, max_depth=8, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8, random_state=42
            ),
            'LightGBM': lgb.LGBMRegressor(
                n_estimators=500, max_depth=8, learning_rate=0.1,
                subsample=0.8, colsample_bytree=0.8, random_state=42
            ),
            'RandomForest': RandomForestRegressor(
                n_estimators=300, max_depth=12, random_state=42
            ),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=300, max_depth=8, learning_rate=0.1, random_state=42
            ),
            'SVR': SVR(kernel='rbf', C=100, gamma='scale'),
            'NeuralNetwork': MLPRegressor(
                hidden_layer_sizes=(200, 100, 50), max_iter=500, random_state=42
            )
        }

        results = {}

        for name, model in models_config.items():
            try:
                logger.info(f"训练{name}模型...")

                # 训练模型
                model.fit(X_train, y_train)

                # 预测
                y_pred_train = model.predict(X_train)
                y_pred_val = model.predict(X_val)

                # 评估
                train_mae = mean_absolute_error(y_train, y_pred_train)
                val_mae = mean_absolute_error(y_val, y_pred_val)
                train_r2 = r2_score(y_train, y_pred_train)
                val_r2 = r2_score(y_val, y_pred_val)

                # 计算±20°C精度
                val_accuracy_20 = np.mean(np.abs(y_val - y_pred_val) <= 20) * 100

                results[name] = {
                    'model': model,
                    'train_mae': train_mae,
                    'val_mae': val_mae,
                    'train_r2': train_r2,
                    'val_r2': val_r2,
                    'accuracy_20': val_accuracy_20,
                    'predictions_val': y_pred_val
                }

                logger.info(f"{name} - 验证MAE: {val_mae:.1f}°C, R²: {val_r2:.3f}, ±20°C精度: {val_accuracy_20:.1f}%")

            except Exception as e:
                logger.error(f"训练{name}模型失败: {e}")
                continue

        self.models = results
        return results

    def create_ensemble_model(self, X: np.ndarray, y: np.ndarray) -> Any:
        """创建集成模型"""
        logger.info("创建集成模型")

        if len(self.models) < 2:
            logger.warning("模型数量不足，无法创建集成模型")
            return None

        # 获取各模型的预测结果
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)

        # 构建元学习器的特征
        meta_features_train = []
        meta_features_val = []

        for name, model_info in self.models.items():
            model = model_info['model']
            pred_train = model.predict(X_train)
            pred_val = model.predict(X_val)

            meta_features_train.append(pred_train)
            meta_features_val.append(pred_val)

        meta_X_train = np.column_stack(meta_features_train)
        meta_X_val = np.column_stack(meta_features_val)

        # 训练元学习器
        meta_learner = Ridge(alpha=1.0)
        meta_learner.fit(meta_X_train, y_train)

        # 评估集成模型
        ensemble_pred = meta_learner.predict(meta_X_val)
        ensemble_mae = mean_absolute_error(y_val, ensemble_pred)
        ensemble_r2 = r2_score(y_val, ensemble_pred)
        ensemble_accuracy_20 = np.mean(np.abs(y_val - ensemble_pred) <= 20) * 100

        logger.info(f"集成模型 - 验证MAE: {ensemble_mae:.1f}°C, R²: {ensemble_r2:.3f}, ±20°C精度: {ensemble_accuracy_20:.1f}%")

        return {
            'meta_learner': meta_learner,
            'base_models': self.models,
            'mae': ensemble_mae,
            'r2': ensemble_r2,
            'accuracy_20': ensemble_accuracy_20
        }

def apply_material_correction(df: pd.DataFrame, predictions: np.ndarray) -> np.ndarray:
    """
    应用最后2分钟添加材料的校正因子：每100kg降低5°C
    """
    logger.info("应用最后2分钟添加材料的校正因子")

    corrected_predictions = predictions.copy()

    # 检查是否有"最后2分钟"列
    if '最后2分钟' in df.columns:
        # 计算校正值：每100kg降低5°C
        correction = df['最后2分钟'] * (-5/100)
        corrected_predictions = predictions + correction.values

        logger.info(f"校正范围: {correction.min():.2f}°C 到 {correction.max():.2f}°C")
        logger.info(f"校正前范围: {np.min(predictions):.2f}°C - {np.max(predictions):.2f}°C")
        logger.info(f"校正后范围: {np.min(corrected_predictions):.2f}°C - {np.max(corrected_predictions):.2f}°C")
    else:
        logger.warning("数据中没有'最后2分钟'列，无法应用校正因子")

    return corrected_predictions

def create_visualization(results: Dict[str, Any], df: pd.DataFrame, output_dir: str = "stage_optimization_results"):
    """创建可视化图表"""
    logger.info("创建可视化图表")

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 1. 模型性能对比
    plt.figure(figsize=(12, 8))

    model_names = list(results.keys())
    mae_scores = [results[name]['val_mae'] for name in model_names]
    accuracy_scores = [results[name]['accuracy_20'] for name in model_names]

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # MAE对比
    ax1.bar(model_names, mae_scores)
    ax1.set_title('模型MAE对比')
    ax1.set_ylabel('平均绝对误差 (°C)')
    ax1.tick_params(axis='x', rotation=45)

    # ±20°C精度对比
    ax2.bar(model_names, accuracy_scores)
    ax2.set_title('模型±20°C精度对比')
    ax2.set_ylabel('精度 (%)')
    ax2.tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/model_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 特征重要性（以最佳模型为例）
    best_model_name = min(results.keys(), key=lambda x: results[x]['val_mae'])
    best_model = results[best_model_name]['model']

    if hasattr(best_model, 'feature_importances_'):
        plt.figure(figsize=(10, 8))
        feature_importance = best_model.feature_importances_
        # 这里需要特征名称，暂时使用索引
        plt.barh(range(len(feature_importance)), feature_importance)
        plt.title(f'{best_model_name} 特征重要性')
        plt.xlabel('重要性')
        plt.ylabel('特征索引')
        plt.savefig(f'{output_dir}/feature_importance.png', dpi=300, bbox_inches='tight')
        plt.close()

    logger.info(f"可视化图表已保存到 {output_dir} 目录")

def main():
    """主函数 - 执行完整的阶段性优化流程"""
    logger.info("=== 开始阶段性优化钢水温度预测模型 ===")

    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        logger.info(f"成功读取数据，共{len(df)}条记录，{df.shape[1]}个特征")
    except Exception as e:
        logger.error(f"读取数据失败：{e}")
        return

    # Stage 1: 数据清理和基础特征工程
    logger.info("\n=== Stage 1: 数据清理和基础特征工程 ===")
    stage1 = Stage1DataCleaner()

    # 数据清理
    df_cleaned = stage1.clean_data(df)

    # 钢种分组
    df_grouped = stage1.group_steel_types(df_cleaned)

    # 创建基础特征
    df_basic_features = stage1.create_basic_features(df_grouped)

    logger.info(f"Stage 1完成，特征数从{df.shape[1]}增加到{df_basic_features.shape[1]}")

    # Stage 2: 高级特征工程
    logger.info("\n=== Stage 2: 高级特征工程（炉渣模型特征） ===")
    stage2 = Stage2SlagFeatureEngineer()

    # 创建炉渣特征
    df_slag_features = stage2.create_slag_features(df_basic_features)

    # 创建热平衡特征
    df_thermal_features = stage2.create_thermal_features(df_slag_features)

    # 创建交互特征
    df_interaction_features = stage2.create_interaction_features(df_thermal_features)

    logger.info(f"Stage 2完成，特征数从{df_basic_features.shape[1]}增加到{df_interaction_features.shape[1]}")

    # Stage 3: 高级建模
    logger.info("\n=== Stage 3: 高级建模 ===")
    stage3 = Stage3AdvancedModeling()

    # 准备特征
    X, y, feature_names = stage3.prepare_features(df_interaction_features)

    # 训练模型
    model_results = stage3.train_models(X, y, feature_names)

    # 创建集成模型
    ensemble_model = stage3.create_ensemble_model(X, y)

    if ensemble_model:
        model_results['Ensemble'] = ensemble_model

    # 创建可视化
    create_visualization(model_results, df_interaction_features)

    # 保存结果
    logger.info("\n=== 保存优化结果 ===")

    # 保存最佳模型
    best_model_name = min(model_results.keys(), key=lambda x: model_results[x]['mae'] if 'mae' in model_results[x] else model_results[x]['val_mae'])
    best_model = model_results[best_model_name]

    # 创建结果目录
    output_dir = "stage_optimization_results"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 保存模型
    with open(f'{output_dir}/best_model_{best_model_name.lower()}.pkl', 'wb') as f:
        if 'model' in best_model:
            pickle.dump(best_model['model'], f)
        else:
            pickle.dump(best_model, f)

    # 保存特征工程后的数据
    df_interaction_features.to_excel(f'{output_dir}/processed_training_data.xlsx', index=False)

    # 保存特征名称
    with open(f'{output_dir}/feature_names.pkl', 'wb') as f:
        pickle.dump(feature_names, f)

    # 保存预处理器
    with open(f'{output_dir}/scaler.pkl', 'wb') as f:
        pickle.dump(stage3.scaler, f)

    with open(f'{output_dir}/label_encoder.pkl', 'wb') as f:
        pickle.dump(stage3.label_encoder, f)

    # 生成最终报告
    logger.info("\n=== 最终优化结果 ===")
    for name, result in model_results.items():
        if 'mae' in result:
            mae = result['mae']
            accuracy = result['accuracy_20']
        else:
            mae = result['val_mae']
            accuracy = result['accuracy_20']

        logger.info(f"{name}: MAE={mae:.1f}°C, ±20°C精度={accuracy:.1f}%")

    logger.info(f"\n最佳模型: {best_model_name}")
    logger.info(f"所有结果已保存到 {output_dir} 目录")
    logger.info("=== 阶段性优化完成 ===")

if __name__ == "__main__":
    main()
