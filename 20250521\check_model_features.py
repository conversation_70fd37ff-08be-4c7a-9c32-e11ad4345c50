"""
检查模型特征
"""

import os
import pickle
import pandas as pd
import numpy as np

def load_model(model_path):
    """加载模型"""
    print(f"加载模型: {model_path}")
    with open(model_path, 'rb') as f:
        model = pickle.load(f)
    return model

def check_model_features(model, model_name):
    """检查模型特征"""
    print(f"\n{model_name} 模型信息:")
    
    if hasattr(model, 'feature_names_'):
        print(f"特征名称: {model.feature_names_}")
    else:
        print("无特征名称属性")
    
    if hasattr(model, 'n_features_in_'):
        print(f"特征数量: {model.n_features_in_}")
    else:
        print("无特征数量属性")
    
    if hasattr(model, 'base_models'):
        print(f"基础模型数量: {len(model.base_models)}")
        for i, base_model in enumerate(model.base_models):
            print(f"\n基础模型 {i} ({type(base_model).__name__}):")
            if hasattr(base_model, 'feature_names_'):
                print(f"特征名称: {base_model.feature_names_}")
            else:
                print("无特征名称属性")
            
            if hasattr(base_model, 'n_features_in_'):
                print(f"特征数量: {base_model.n_features_in_}")
            else:
                print("无特征数量属性")

def main():
    """主函数"""
    models_dir = "results"
    
    # 加载顺序思维模型
    sequential_model_path = os.path.join(models_dir, "sequential_thinking_model.pkl")
    if os.path.exists(sequential_model_path):
        sequential_model = load_model(sequential_model_path)
        check_model_features(sequential_model, "顺序思维")
    
    # 加载基础模型
    base_model_names = ["xgboost", "lightgbm", "random_forest", "svr", "ridge", "lasso"]
    for name in base_model_names:
        model_path = os.path.join(models_dir, f"{name}_model.pkl")
        if os.path.exists(model_path):
            model = load_model(model_path)
            check_model_features(model, name)

if __name__ == "__main__":
    main()
