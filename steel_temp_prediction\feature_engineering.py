
"""
Feature engineering module for the steel temperature prediction model.
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import PolynomialFeatures, StandardScaler, OneHotEncoder
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.impute import SimpleImputer
import logging
from typing import Dict, List, Tuple, Union, Any, Optional

# 修复导入路径
try:
    from steel_temp_prediction.config import HEAT_CONSTANTS, PROCESS_STAGES, FEATURE_GROUPS, TARGET, COL_SLAG_FEO_MEASURED_PERCENT, DEFAULT_SLAG_FEO_PERCENT, COL_SLAG_TOTAL_MASS_KG, ELEMENT_TO_OXIDE_MASS_RATIO, FLUX_COMPONENT_RATIO, MOLAR_MASS, COL_HM_MASS_KG, COL_STEEL_MASS_KG, COL_SCRAP_MASS_KG, DEFAULT_IRON_MASS_COL # 确保所有常量都被导入
except ImportError:
    # 当直接在包内运行时使用这个导入
    from config import HEAT_CONSTANTS, PROCESS_STAGES, FEATURE_GROUPS, TARGET, COL_SLAG_FEO_MEASURED_PERCENT, DEFAULT_SLAG_FEO_PERCENT, COL_SLAG_TOTAL_MASS_KG, ELEMENT_TO_OXIDE_MASS_RATIO, FLUX_COMPONENT_RATIO, MOLAR_MASS, COL_HM_MASS_KG, COL_STEEL_MASS_KG, COL_SCRAP_MASS_KG, DEFAULT_IRON_MASS_COL

# Set up logging
logger = logging.getLogger(__name__)

# 中文注释：在这里定义原始数据中可能用到的列名常量。
# 用户需要根据自己的实际数据集中的列名来修改这些常量。

# --- 冶金常量 ---
# 元素和氧化物的摩尔质量 (g/mol)
MOLAR_MASS = {
    'Si': 28.085,    # 硅
    'Mn': 54.938,    # 锰
    'P': 30.974,     # 磷
    'C': 12.011,     # 碳
    'Fe': 55.845,    # 铁
    'O': 15.999,     # 氧
    'Ca': 40.078,    # 钙
    'Mg': 24.305,    # 镁
    'Al': 26.982,    # 铝
    'SiO2': 60.084,  # 二氧化硅
    'MnO': 70.937,   # 氧化锰
    'P2O5': 141.945, # 五氧化二磷
    'CaO': 56.077,   # 氧化钙
    'MgO': 40.304,   # 氧化镁
    'Al2O3': 101.96, # 氧化铝
    'FeO': 71.844,   # 氧化亚铁
    'Fe2O3': 159.69  # 氧化铁
}

# 元素氧化成氧化物的质量转换系数
ELEMENT_TO_OXIDE_MASS_RATIO = {
    'Si_to_SiO2': MOLAR_MASS['SiO2'] / MOLAR_MASS['Si'],      # 约 2.14
    'Mn_to_MnO': MOLAR_MASS['MnO'] / MOLAR_MASS['Mn'],        # 约 1.29
    'P_to_P2O5': MOLAR_MASS['P2O5'] / (2 * MOLAR_MASS['P'])   # 约 2.29
}

# 辅料中活性成分的估计含量 (根据典型值设置，实际应根据具体工厂物料情况调整)
FLUX_COMPONENT_RATIO = {
    'CaO_in_Lime': 0.90,           # 石灰中CaO的含量，约90%
    'CaO_in_Dolomite': 0.58,       # 白云石中CaO的含量，约58%
    'MgO_in_Dolomite': 0.40,       # 白云石中MgO的含量，约40% (如有使用)
    'Al2O3_in_Bauxite': 0.70       # 铝矾土中Al2O3的含量，约70% (如有使用)
}

# 炉渣损耗系数 (根据图片中3.2和3.7节信息)
SLAG_LOSS_RATE_KG_PER_SEC = 0.0025  # 碱性辅料排渣损耗率 (kg/s)，表1中给出为0.0025kg/s

# --- 新增：FeO 相关默认值 ---
DEFAULT_SLAG_FEO_PERCENT = 18.0  # 当无法从数据中获取或估算时，默认的炉渣FeO百分比
DEFAULT_IRON_MASS_COL = '铁水' # 默认铁水质量列，如果原始数据没有明确指定
COL_SLAG_TOTAL_MASS_KG = 'feature_slag_total_mass_kg' # 总炉渣质量的特征名

# --- 原始数据列名定义 (请根据实际数据集调整) ---

# 铁水相关列
COL_HM_MASS_KG = '铁水'                 # 铁水质量(kg)
COL_HM_TEMP_C = '铁水温度'              # 铁水温度(℃)
COL_HM_SI_PERCENT = '铁水SI'            # 铁水中Si含量(%)
COL_HM_MN_PERCENT = '铁水MN'            # 铁水中Mn含量(%)
COL_HM_P_PERCENT = '铁水P'              # 铁水中P含量(%)
COL_HM_C_PERCENT = '铁水C'              # 铁水中C含量(%) (可选)

# 钢水相关列 (如果有)
COL_STEEL_MASS_KG = '钢水重量'          # 钢水质量(kg)，如果没有可用铁水+废钢近似
COL_SCRAP_MASS_KG = '废钢'              # 废钢加入量(kg)
COL_STEEL_SI_FINAL_PERCENT = '钢水SI'   # 钢水最终Si含量(%)
COL_STEEL_MN_FINAL_PERCENT = '钢水MN'   # 钢水最终Mn含量(%)
COL_STEEL_P_FINAL_PERCENT = '钢水P'     # 钢水最终P含量(%)

# 辅料加入相关列
COL_LIME_ADDITION_KG = '石灰'            # 石灰加入量(kg)
COL_DOLOMITE_ADDITION_KG = '白云石'       # 白云石加入量(kg)
COL_BAUXITE_ADDITION_KG = '铝矾土'        # 铝矾土加入量(kg) (如有)
COL_OTHER_FLUX_ADDITION_KG = '其他辅料'    # 其他辅料加入量(kg) (如有)

# 吹炼和工艺参数相关列
COL_OXYGEN_TOTAL_NM3 = '累氧实际'        # 累计供氧量(Nm³)
COL_BLOWING_TIME_SEC = '吹氧时间s'        # 吹氧时间(s)
COL_LANCE_HEIGHT_MM = '枪位'             # 喷枪高度(mm)
COL_LANCE_ANGLE_MAX = '最大角度'          # 最大枪角度(°)
COL_BOTTOM_BLOWING_INTENSITY = '底吹强度'  # 底吹强度 (如有)

# 炉渣相关列 (如果直接有测量数据，则可以使用)
COL_SLAG_WEIGHT_MEASURED_KG = '炉渣重量'  # 实测炉渣重量(kg) (如有)
COL_SLAG_FEO_MEASURED_PERCENT = '炉渣FeO'  # 实测炉渣中FeO含量(%) (如有)

# --- 炉渣成分相关列名 (示例) ---
# 注意：以下部分列可能在实际数据中并不存在，而是通过计算得到
COL_SLAG_CAO_KG = 'raw_slag_cao_kg'                # 例如：炉渣中CaO的量 (kg) 或来源
COL_SLAG_SIO2_KG = 'raw_slag_sio2_kg'              # 例如：炉渣中SiO2的量 (kg) 或来源
COL_SLAG_MGO_KG = 'raw_slag_mgo_kg'                # 例如：炉渣中MgO的量 (kg) 或来源
COL_SLAG_AL2O3_KG = 'raw_slag_al2o3_kg'            # 例如：炉渣中Al2O3的量 (kg) 或来源
COL_SLAG_FEO_PERCENT = 'raw_slag_feo_percent'      # 例如：炉渣中FeO的百分比 (直接测量或估算)
COL_SLAG_WEIGHT_ESTIMATED_KG = 'raw_slag_weight_kg' # 例如：估算的炉渣重量 (kg)

# --- 工艺参数相关列名 (示例) ---
COL_PROCESS_OXYGEN_RATE = 'raw_oxygen_flow_rate_m3_min' # 吹氧速率 (m³/min)
COL_PROCESS_OXYGEN_VOLUME = 'raw_total_oxygen_volume_m3'  # 总吹氧量 (m³)
COL_PROCESS_LANCE_HEIGHT = 'raw_lance_height_mm'          # 喷枪高度 (mm)
COL_PROCESS_STIRRING_POWER = 'raw_stirring_power_kw'    # 底吹搅拌强度/功率 (kW)
# 示例：辅料加入时间和量 (用户可能有多条此类数据，需要相应处理)
# COL_PROCESS_FLUX_A_TIME_SEC = 'raw_flux_a_add_time_sec' # 辅料A加入时间 (秒)
# COL_PROCESS_FLUX_A_AMOUNT_KG = 'raw_flux_a_add_amount_kg' # 辅料A加入量 (kg)

# --- 时间序列数据相关列名 (示例) ---
# 这些列通常与一个分组ID (如炉号) 和一个时间戳关联
COL_TS_HEAT_ID = 'heat_id'                         # 炉号/批次号，用于分组
COL_TS_TIMESTAMP = 'timestamp_sec'                 # 时间戳 (秒或其他单位)
COL_TS_SENSOR_A_READING = 'raw_sensor_a_value'     # 传感器A的读数
COL_TS_SENSOR_B_READING = 'raw_sensor_b_value'     # 传感器B的读数

# --- 其他可能用于交互或计算的特征列名 (示例) ---
COL_INTERACT_STEEL_TEMP_INITIAL = 'raw_steel_temp_initial_c' # 初始钢水温度 (℃)
COL_INTERACT_ALLOY_X_CONTENT = 'raw_alloy_x_content_percent' # 某种合金成分X的百分比

# --- NEW REDUCED FEATURE LIST ---
ACTUAL_MODEL_INPUT_FEATURES = [
    # Features with importance > 0 from meta-learner, mapped to original names
    # feature_0 (铁水)
    '铁水',
    # feature_1 (铁水温度)
    '铁水温度',
    # feature_2 (铁水SI)
    '铁水SI',
    # feature_3 (铁水MN)
    '铁水MN',
    # feature_4 (铁水P)
    '铁水P',
    # feature_5 (铁水C)
    '铁水C',
    # feature_6 (石灰)
    '石灰',
    # feature_7 (累氧实际)
    '累氧实际',
    # feature_8 (吹氧时间s)
    '吹氧时间s',
    # feature_9 (最大角度)
    '最大角度',
    # feature_10 (feature_slag_SiO2_from_Si_kg)
    'feature_slag_SiO2_from_Si_kg',
    # feature_11 (feature_slag_MnO_from_Mn_kg)
    'feature_slag_MnO_from_Mn_kg',
    # feature_12 (feature_slag_P2O5_from_P_kg)
    'feature_slag_P2O5_from_P_kg',
    # feature_13 (feature_slag_CaO_from_lime_kg)
    'feature_slag_CaO_from_lime_kg',
    # feature_14 (feature_slag_CaO_from_dolomite_kg)
    'feature_slag_CaO_from_dolomite_kg',
    # feature_15 (feature_slag_CaO_total_kg)
    'feature_slag_CaO_total_kg',
    # feature_16 (feature_slag_MgO_total_kg)
    'feature_slag_MgO_total_kg',
    # feature_18 (feature_slag_FeO_kg)
    'feature_slag_FeO_kg',
    # feature_19 (feature_slag_total_mass_kg)
    'feature_slag_total_mass_kg',
    # feature_20 (feature_slag_known_oxides_kg)
    'feature_slag_known_oxides_kg',
    
    # Original raw features that are fundamental
    '废钢',
    '白云石',
    
    # --- Added advanced slag features ---
    'feature_slag_CaO_percent',
    'feature_slag_SiO2_percent',
    'feature_slag_MgO_percent',
    'feature_slag_Al2O3_percent',
    'feature_slag_basicity_R2',
    'feature_slag_viscosity',
    'feature_slag_liquid_percent',
    'feature_slag_optical_basicity',
    'feature_slag_log_phosphate_capacity',
    
    # Consider if '铝矾土' and '底吹强度' should be here if they are consistently available
    # and if their absence in top importance doesn't mean they are useless
    # (e.g. they might be crucial for some base models or specific scenarios)

    # The following features were in the previous very long list but are not included now
    # as they either had 0 importance, were redundant, or are more complex derived features.
    # This aims to focus on a core, important set.
]
logger.info(f"Using REFINED ACTUAL_MODEL_INPUT_FEATURES list with {len(ACTUAL_MODEL_INPUT_FEATURES)} features based on meta-learner importance and new slag features.")

def create_basic_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create basic features based on raw data.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added basic features
    """
    logger.info("Creating basic features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # Calculate iron to scrap ratio
    if '铁水' in df_features.columns and '废钢' in df_features.columns:
        df_features['铁水废钢比'] = df_features['铁水'] / df_features['废钢']
        logger.info("Created feature: 铁水废钢比")

    # Calculate basicity (CaO+MgO)/(SiO2+P2O5)
    # Since we don't have direct slag composition, we'll estimate from additions
    if '石灰' in df_features.columns and '白云石' in df_features.columns:
        # Estimate CaO+MgO from lime and dolomite additions
        df_features['碱度估算'] = (df_features['石灰'] * 0.9 + df_features['白云石'] * 0.58) / \
                            (df_features['铁水SI'] * 2.14 + df_features['铁水P'] * 2.29)
        logger.info("Created feature: 碱度估算")

    # Calculate total oxygen supply per ton of hot metal
    if '累氧实际' in df_features.columns and '铁水' in df_features.columns:
        df_features['单位铁水供氧量'] = df_features['累氧实际'] / df_features['铁水']
        logger.info("Created feature: 单位铁水供氧量")

    # Calculate blowing intensity (oxygen per minute)
    if '累氧实际' in df_features.columns and '吹氧时间s' in df_features.columns:
        df_features['吹氧强度'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60)
        logger.info("Created feature: 吹氧强度")

    # Calculate time-related features
    if '间隔时间min' in df_features.columns and '吹氧时间s' in df_features.columns:
        # Total process time
        df_features['总处理时间min'] = df_features['间隔时间min'] + (df_features['吹氧时间s'] / 60)
        logger.info("Created feature: 总处理时间min")

    return df_features

def create_metallurgical_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create metallurgical features based on thermodynamic principles.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added metallurgical features
    """
    logger.info("Creating metallurgical features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # Calculate heat generation from oxidation reactions

    # Silicon oxidation heat
    if '铁水SI' in df_features.columns and '铁水' in df_features.columns:
        df_features['氧化硅热'] = df_features['铁水SI'] * df_features['铁水'] * HEAT_CONSTANTS['Si_oxidation'] * 10
        logger.info("Created feature: 氧化硅热")

    # Carbon oxidation heat
    if 'CO中C(kg)' in df_features.columns and ' CO2中C(kg)' in df_features.columns:
        df_features['氧化碳热'] = (df_features['CO中C(kg)'] + df_features[' CO2中C(kg)']) * HEAT_CONSTANTS['C_oxidation']
        logger.info("Created feature: 氧化碳热")
    elif '气体总C' in df_features.columns:
        df_features['氧化碳热'] = df_features['气体总C'] * HEAT_CONSTANTS['C_oxidation']
        logger.info("Created feature: 氧化碳热 (using 气体总C)")

    # Manganese oxidation heat
    if '铁水MN' in df_features.columns and '铁水' in df_features.columns:
        # Assuming 80% of Mn is oxidized
        df_features['氧化锰热'] = df_features['铁水MN'] * df_features['铁水'] * 0.8 * HEAT_CONSTANTS['Mn_oxidation'] * 10
        logger.info("Created feature: 氧化锰热")

    # Phosphorus oxidation heat
    if '铁水P' in df_features.columns and '铁水' in df_features.columns:
        # Assuming 90% of P is oxidized
        df_features['氧化磷热'] = df_features['铁水P'] * df_features['铁水'] * 0.9 * HEAT_CONSTANTS['P_oxidation'] * 10
        logger.info("Created feature: 氧化磷热")

    # Calculate total heat generation
    heat_columns = [col for col in ['氧化硅热', '氧化碳热', '氧化锰热', '氧化磷热'] if col in df_features.columns]
    if heat_columns:
        df_features['总放热量'] = df_features[heat_columns].sum(axis=1)
        logger.info("Created feature: 总放热量")

    # Calculate heat loss
    if '间隔时间min' in df_features.columns and '吹氧时间s' in df_features.columns:
        total_time = df_features['间隔时间min'] + (df_features['吹氧时间s'] / 60)
        # Estimate heat loss based on time and furnace condition
        df_features['散热量'] = total_time * HEAT_CONSTANTS['heat_loss_rate'] * (df_features['铁水'] + df_features['废钢'])
        logger.info("Created feature: 散热量")

    # Calculate net heat balance
    if '总放热量' in df_features.columns and '散热量' in df_features.columns:
        df_features['净热量'] = df_features['总放热量'] - df_features['散热量']
        logger.info("Created feature: 净热量")

    # Calculate theoretical temperature increase
    if '净热量' in df_features.columns and '铁水' in df_features.columns and '废钢' in df_features.columns:
        total_weight = df_features['铁水'] + df_features['废钢']
        df_features['理论升温'] = df_features['净热量'] / (total_weight * HEAT_CONSTANTS['heat_capacity_steel'] * 1000)
        logger.info("Created feature: 理论升温")

    # Calculate theoretical final temperature
    if '理论升温' in df_features.columns and '铁水温度' in df_features.columns:
        df_features['理论终点温度'] = df_features['铁水温度'] + df_features['理论升温']
        logger.info("Created feature: 理论终点温度")

    return df_features

def create_time_series_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create time series features based on process stages.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added time series features
    """
    logger.info("Creating time series features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # For this simplified version, we'll create proxy features since we don't have the actual time series data

    # Estimate decarburization rate in different stages based on total carbon removal
    if '气体总C' in df_features.columns and '吹氧时间s' in df_features.columns:
        # Total decarburization rate
        df_features['总脱碳率'] = df_features['气体总C'] / (df_features['吹氧时间s'] / 60)
        logger.info("Created feature: 总脱碳率")

        # Estimate early, middle, and late stage decarburization rates
        # In reality, these would be calculated from time series data
        # Here we're using approximations based on typical patterns

        # Early stage: typically 20% of total carbon removal in 30% of time
        early_time = df_features['吹氧时间s'] * PROCESS_STAGES['early_stage'] / 60
        df_features['初期脱碳率'] = df_features['气体总C'] * 0.2 / early_time
        logger.info("Created feature: 初期脱碳率")

        # Middle stage: typically 60% of total carbon removal in 40% of time
        mid_time = df_features['吹氧时间s'] * (PROCESS_STAGES['mid_stage'] - PROCESS_STAGES['early_stage']) / 60
        df_features['中期脱碳率'] = df_features['气体总C'] * 0.6 / mid_time
        logger.info("Created feature: 中期脱碳率")

        # Late stage: typically 20% of total carbon removal in 30% of time
        late_time = df_features['吹氧时间s'] * (PROCESS_STAGES['late_stage'] - PROCESS_STAGES['mid_stage']) / 60
        df_features['后期脱碳率'] = df_features['气体总C'] * 0.2 / late_time
        logger.info("Created feature: 后期脱碳率")

    # Estimate CO peak time (typically occurs at around 40-60% of blowing time)
    if '吹氧时间s' in df_features.columns:
        # Random variation around 50% of blowing time
        np.random.seed(42)  # For reproducibility
        variation = np.random.normal(0, 0.1, size=len(df_features))
        df_features['CO峰值时刻'] = (0.5 + variation) * df_features['吹氧时间s']
        # Clip to reasonable range
        df_features['CO峰值时刻'] = df_features['CO峰值时刻'].clip(0.3 * df_features['吹氧时间s'], 0.7 * df_features['吹氧时间s'])
        logger.info("Created feature: CO峰值时刻")

    # Estimate maximum decarburization rate time (typically occurs slightly before CO peak)
    if 'CO峰值时刻' in df_features.columns:
        df_features['脱碳速率最大时刻'] = df_features['CO峰值时刻'] * 0.9
        logger.info("Created feature: 脱碳速率最大时刻")

    return df_features

def create_interaction_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create interaction features based on metallurgical principles.

    Args:
        df: Input DataFrame

    Returns:
        DataFrame with added interaction features
    """
    logger.info("Creating interaction features...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    # Create interaction between silicon content and oxygen supply
    if '铁水SI' in df_features.columns and '单位铁水供氧量' in df_features.columns:
        df_features['铁水SI_氧供应强度'] = df_features['铁水SI'] * df_features['单位铁水供氧量']
        logger.info("Created feature: 铁水SI_氧供应强度")

    # Create interaction between carbon content and oxygen supply
    if '铁水C' in df_features.columns and '单位铁水供氧量' in df_features.columns:
        df_features['铁水C_氧供应强度'] = df_features['铁水C'] * df_features['单位铁水供氧量']
        logger.info("Created feature: 铁水C_氧供应强度")

    # Create interaction between hot metal temperature and silicon content
    if '铁水温度' in df_features.columns and '铁水SI' in df_features.columns:
        df_features['铁水温度_铁水SI'] = df_features['铁水温度'] * df_features['铁水SI']
        logger.info("Created feature: 铁水温度_铁水SI")

    # Create interaction between basicity and phosphorus
    if '碱度估算' in df_features.columns and '铁水P' in df_features.columns:
        df_features['碱度_铁水P'] = df_features['碱度估算'] * df_features['铁水P']
        logger.info("Created feature: 碱度_铁水P")

    # Create interaction between iron-to-scrap ratio and hot metal temperature
    if '铁水废钢比' in df_features.columns and '铁水温度' in df_features.columns:
        df_features['铁水废钢比_铁水温度'] = df_features['铁水废钢比'] * df_features['铁水温度']
        logger.info("Created feature: 铁水废钢比_铁水温度")

    # Create interaction between blowing intensity and maximum lance angle
    if '吹氧强度' in df_features.columns and '最大角度' in df_features.columns:
        df_features['吹氧强度_最大角度'] = df_features['吹氧强度'] * df_features['最大角度']
        logger.info("Created feature: 吹氧强度_最大角度")

    return df_features

def create_polynomial_features(df: pd.DataFrame, degree: int = 2,
                              feature_names: List[str] = None) -> pd.DataFrame:
    """
    Create polynomial features for selected columns.

    Args:
        df: Input DataFrame
        degree: Polynomial degree
        feature_names: List of feature names to use for polynomial features

    Returns:
        DataFrame with added polynomial features
    """
    logger.info(f"Creating polynomial features (degree={degree})...")

    # Make a copy to avoid modifying the original
    df_features = df.copy()

    if feature_names is None:
        # Use basic metallurgical features
        feature_names = [
            '铁水温度', '铁水SI', '铁水C', '铁水废钢比', '单位铁水供氧量',
            '吹氧强度', '总处理时间min'
        ]

    # Filter to only include features that exist in the DataFrame
    feature_names = [f for f in feature_names if f in df_features.columns]

    if not feature_names:
        logger.warning("No valid features for polynomial feature creation")
        return df_features

    # Create polynomial features
    poly = PolynomialFeatures(degree=degree, include_bias=False, interaction_only=False)
    poly_features = poly.fit_transform(df_features[feature_names])

    # Get feature names
    poly_feature_names = poly.get_feature_names_out(feature_names)

    # Remove the original features from the polynomial feature names
    poly_feature_names = [name for name in poly_feature_names if name not in feature_names]

    # Add polynomial features to DataFrame
    for i, name in enumerate(poly_feature_names):
        # Skip the first 'n' features which are the original ones
        idx = i + len(feature_names)
        if idx < poly_features.shape[1]:
            df_features[f'poly_{name}'] = poly_features[:, idx]

    logger.info(f"Created {len(poly_feature_names)} polynomial features")

    return df_features

def select_best_features(X_train: pd.DataFrame, y_train: pd.Series,
                        X_test: pd.DataFrame, k: int = 50) -> Tuple[pd.DataFrame, pd.DataFrame, List[str]]:
    """
    Select the best k features based on F-regression.

    Args:
        X_train: Training features
        y_train: Training target
        X_test: Test features
        k: Number of features to select

    Returns:
        Tuple of (X_train_selected, X_test_selected, selected_feature_names)
    """
    logger.info(f"Selecting best {k} features...")

    # Initialize feature selector
    selector = SelectKBest(score_func=f_regression, k=k)

    # Fit and transform training data
    X_train_selected = selector.fit_transform(X_train, y_train)

    # Transform test data
    X_test_selected = selector.transform(X_test)

    # Get selected feature names
    selected_indices = selector.get_support(indices=True)
    selected_features = X_train.columns[selected_indices]

    logger.info(f"Selected features: {', '.join(selected_features)}")

    # Convert back to DataFrame with feature names
    X_train_selected_df = pd.DataFrame(X_train_selected, columns=selected_features, index=X_train.index)
    X_test_selected_df = pd.DataFrame(X_test_selected, columns=selected_features, index=X_test.index)

    return X_train_selected_df, X_test_selected_df, selected_features

def calculate_slag_basicity(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算炉渣碱度。
    
    根据公式(3-14)，碱度R = CaO% / SiO2%
    也可以计算扩展的碱度定义，如三元碱度等。

    Args:
        df: 输入DataFrame，包含炉渣中CaO和SiO2的百分比含量。

    Returns:
        添加了炉渣碱度特征的DataFrame。
    """
    df_out = df.copy()
    logger.info("Calculating slag basicity...")
    
    # 确保有CaO和SiO2的百分比含量
    if 'feature_slag_CaO_percent' in df_out.columns and 'feature_slag_SiO2_percent' in df_out.columns:
        # 处理分母可能为零的情况，加上一个极小值epsilon
        epsilon = 1e-6
        # 计算二元碱度 R2 = CaO% / SiO2%
        df_out['feature_slag_basicity_R2'] = df_out['feature_slag_CaO_percent'] / \
                                               (df_out['feature_slag_SiO2_percent'] + epsilon)
        logger.info(f"Calculated 'feature_slag_basicity_R2'. Mean: {df_out['feature_slag_basicity_R2'].mean():.2f}")
        
        # 计算三元碱度 R3 = (CaO% + MgO%) / SiO2% (如果有MgO数据)
        if 'feature_slag_MgO_percent' in df_out.columns:
            df_out['feature_slag_basicity_R3'] = (df_out['feature_slag_CaO_percent'] + 
                                                 df_out['feature_slag_MgO_percent']) / \
                                                (df_out['feature_slag_SiO2_percent'] + epsilon)
            logger.info(f"Calculated 'feature_slag_basicity_R3'. Mean: {df_out['feature_slag_basicity_R3'].mean():.2f}")
        
        # 计算四元碱度 R4 = (CaO% + MgO%) / (SiO2% + Al2O3%) (如果有Al2O3数据)
        if 'feature_slag_MgO_percent' in df_out.columns and 'feature_slag_Al2O3_percent' in df_out.columns:
            df_out['feature_slag_basicity_R4'] = (df_out['feature_slag_CaO_percent'] + 
                                                 df_out['feature_slag_MgO_percent']) / \
                                                (df_out['feature_slag_SiO2_percent'] + 
                                                 df_out['feature_slag_Al2O3_percent'] + epsilon)
            logger.info(f"Calculated 'feature_slag_basicity_R4'. Mean: {df_out['feature_slag_basicity_R4'].mean():.2f}")                                     
    else:
        logger.warning("无法计算炉渣碱度：缺少必要的列 'feature_slag_CaO_percent' 或 'feature_slag_SiO2_percent'")
        # Ensure the column exists even if not calculated, for consistency with ACTUAL_MODEL_INPUT_FEATURES
        if 'feature_slag_basicity_R2' not in df_out.columns:
             df_out['feature_slag_basicity_R2'] = np.nan # Or some default like 0 or 2.5
             logger.warning("'feature_slag_basicity_R2' was not calculated and has been added with NaN values.")
    
    return df_out

def add_oxide_content_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    添加关键氧化物含量作为特征。
    这些特征可以直接使用原始数据中的含量（如果是百分比），
    或者根据加入量和估算的炉渣总量来计算百分比。
    """
    df_out = df.copy()
    oxide_cols_map = {
        'feature_slag_mgo_content': COL_SLAG_MGO_KG,
        'feature_slag_al2o3_content': COL_SLAG_AL2O3_KG,
        'feature_slag_feo_content_percent': COL_SLAG_FEO_PERCENT,
    }
    for feature_name, raw_col_name in oxide_cols_map.items():
        if raw_col_name in df_out.columns:
            # 假设原始列已经是所需的形式（例如，FeO是百分比，其他是总量）
            # 如果原始列是加入量，而需要百分比，则需除以 COL_SLAG_WEIGHT_ESTIMATED_KG
            df_out[feature_name] = df_out[raw_col_name]
            # print(f"信息: 已添加氧化物特征 {feature_name}。")
        # else:
            # print(f"警告: 氧化物特征源列 {raw_col_name} 未找到。")
    return df_out

def estimate_slag_physical_properties(df: pd.DataFrame) -> pd.DataFrame:
    """
    估算炉渣的物理化学性质（如粘度、熔点等）。
    
    这部分通常需要基于炉渣成分和物理化学模型进行，本函数提供一些简化的经验公式估算。
    用户可以根据具体需求和专业知识进一步细化和改进。
    
    Args:
        df: 输入DataFrame，包含炉渣的成分百分比、碱度等。
        
    Returns:
        添加了炉渣物理化学性质估算特征的DataFrame。
    """
    df_out = df.copy()
    
    # 前提条件检查
    required_cols = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                     'feature_slag_FeO_percent', 'feature_slag_basicity_R2']
    if not all(col in df_out.columns for col in required_cols):
        logger.warning(f"估算炉渣物理化学性质需要列: {required_cols}，但部分列缺失")
        return df_out
    
    # 1. 估算炉渣粘度 (牛顿/平方米)
    # 使用简化的经验公式，仅考虑碱度和FeO的影响
    # 实际中，粘度与温度、成分有复杂的关系，此处为示例
    try:
        # 示例公式：粘度 = 10^(A/T + B)，其中A与碱度和成分有关，B为常数，T为温度
        # 此处假设温度为1600℃，简化为粘度指数 = f(碱度, FeO)
        viscosity_index = (1.0 / df_out['feature_slag_basicity_R2']) + (df_out['feature_slag_FeO_percent'] * 0.05)
        df_out['feature_slag_viscosity_index'] = viscosity_index
    except Exception as e:
        logger.warning(f"计算炉渣粘度指数时出错: {e}")
    
    # 2. 估算炉渣熔点 (℃)
    # 简化公式，实际熔点受多种成分影响，是复杂的多元函数
    try:
        # 示例公式：熔点近似值 = f(CaO, SiO2, FeO)
        cao_effect = df_out['feature_slag_CaO_percent'] * 2.5
        sio2_effect = df_out['feature_slag_SiO2_percent'] * 2.0
        feo_effect = df_out['feature_slag_FeO_percent'] * (-1.5)  # FeO会降低熔点
        
        base_melt_point = 1300  # 基础熔点 (℃)
        estimated_melt_point = base_melt_point + cao_effect + sio2_effect + feo_effect
        
        # 限制在合理范围内
        estimated_melt_point = estimated_melt_point.clip(1200, 1700)
        df_out['feature_slag_melt_point_C'] = estimated_melt_point
    except Exception as e:
        logger.warning(f"计算炉渣熔点估计值时出错: {e}")
    
    # 3. 其他可能的物理化学特性，如导热性、热容量等
    # 这些通常需要更复杂的模型，这里仅作为占位符
    
    return df_out

def add_all_slag_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算和添加所有炉渣相关特征。
    
    集成所有炉渣特征计算的步骤：
    1. 计算各氧化物的质量和总炉渣质量。
    2. 计算各氧化物的百分比含量。
    3. 计算炉渣碱度。
    4. 估算炉渣的物理化学性质。
    
    Args:
        df: 输入DataFrame，包含铁水成分、钢水成分、辅料加入量等原始数据。
        
    Returns:
        添加了炉渣相关特征的DataFrame。
    """
    # 1. 计算各氧化物的质量和总炉渣质量
    df_with_masses = _calculate_individual_oxide_masses_and_total_slag(df)
    
    # 2. 计算各氧化物的百分比含量
    df_with_percents = df_with_masses.copy()
    
    # 确保总炉渣质量有效
    if 'feature_slag_total_mass_kg' in df_with_percents.columns:
        slag_total_mass = df_with_percents['feature_slag_total_mass_kg']
        
        # 列出需要计算百分比的氧化物
        oxide_masses = {
            'SiO2': 'feature_slag_SiO2_from_Si_kg',
            'MnO': 'feature_slag_MnO_from_Mn_kg',
            'P2O5': 'feature_slag_P2O5_from_P_kg',
            'CaO': 'feature_slag_CaO_total_kg',
            'MgO': 'feature_slag_MgO_total_kg',
            'Al2O3': 'feature_slag_Al2O3_total_kg',
            'FeO': 'feature_slag_FeO_kg'
        }
        
        # 计算每种氧化物的百分比含量
        for oxide_name, mass_col in oxide_masses.items():
            if mass_col in df_with_percents.columns:
                percent_col = f'feature_slag_{oxide_name}_percent'
                # Avoid division by zero, replace 0 with a very small number or NaN
                safe_slag_total_mass = slag_total_mass.replace(0, np.nan)
                df_with_percents[percent_col] = (df_with_percents[mass_col] / safe_slag_total_mass * 100).fillna(0)
                logger.info(f"Calculated percentage for {oxide_name}: {df_with_percents[percent_col].mean():.2f}%")
            else:
                 logger.warning(f"Mass column '{mass_col}' not found for {oxide_name} percentage calculation.")
    else:
        logger.warning("无法计算氧化物百分比：缺少 'feature_slag_total_mass_kg' 列")
    
    # 3. 计算炉渣碱度
    df_with_basicity = calculate_slag_basicity(df_with_percents)
    
    # 4. 估算炉渣的物理化学性质
    df_result = estimate_slag_physical_properties(df_with_basicity)
    
    return df_result

def add_quantified_process_parameters(df: pd.DataFrame) -> pd.DataFrame:
    """
    添加量化的工艺参数作为特征。
    这些通常是直接从数据中获取的参数，这里主要是为了明确它们可以作为特征使用。
    """
    df_out = df.copy()
    process_parameter_cols = {
        'feature_process_oxygen_rate': COL_PROCESS_OXYGEN_RATE,
        'feature_process_oxygen_volume': COL_PROCESS_OXYGEN_VOLUME,
        'feature_process_lance_height': COL_PROCESS_LANCE_HEIGHT,
        'feature_process_stirring_power': COL_PROCESS_STIRRING_POWER,
        # 用户可以根据自己的数据添加更多工艺参数
        # 'feature_process_flux_a_amount': COL_PROCESS_FLUX_A_AMOUNT_KG,
    }
    for feature_name, raw_col_name in process_parameter_cols.items():
        if raw_col_name in df_out.columns:
            df_out[feature_name] = df_out[raw_col_name]
            # print(f"信息: 已添加工艺参数特征 {feature_name}。")
    # else:
            # print(f"警告: 工艺参数源列 {raw_col_name} 未找到。")
    return df_out

def add_time_series_derived_features(
    df: pd.DataFrame,
    group_by_col: str, # 用于分组的列，如炉号 COL_TS_HEAT_ID
    time_col: str,     # 时间序列的时间戳列 COL_TS_TIMESTAMP
    target_ts_cols: List[str], # 需要处理的时间序列列名列表，如 [COL_TS_SENSOR_A_READING]
    lag_periods: Optional[List[int]] = None,
    rolling_windows: Optional[List[int]] = None
) -> pd.DataFrame:
    """
    为时间序列数据提取衍生特征（滞后、滑动窗口统计）。
    数据需要先按 group_by_col 分组，然后在组内按 time_col 排序。

    Args:
        df: 输入DataFrame。
        group_by_col: 分组列名（例如炉号）。
        time_col: 时间序列排序列名。
        target_ts_cols: 需要生成衍生特征的原始时间序列列名。
        lag_periods: 滞后周期列表, e.g., [1, 2, 3]。
        rolling_windows: 滑动窗口大小列表, e.g., [3, 5, 10]。
    """
    df_out = df.copy()
    if not (group_by_col in df_out.columns and time_col in df_out.columns):
        print(f"警告: 时间序列分组列 '{group_by_col}' 或时间戳列 '{time_col}' 未在数据中找到，跳过时间序列特征提取。")
        return df_out

    if lag_periods is None: lag_periods = []
    if rolling_windows is None: rolling_windows = []

    # print(f"信息: 开始为列 {target_ts_cols} 生成时间序列特征 (分组依据: {group_by_col}, 时间排序: {time_col})。")

    # 确保数据按分组和时间正确排序是关键
    df_out = df_out.sort_values(by=[group_by_col, time_col])
    
    grouped = df_out.groupby(group_by_col)

    for col_to_process in target_ts_cols:
        if col_to_process not in df_out.columns:
            print(f"警告: 目标时间序列列 {col_to_process} 未找到，跳过。")
            continue

        # 计算滞后特征
        for lag in lag_periods:
            feature_name = f'feature_ts_{col_to_process}_lag_{lag}'
            # Ensure grouped[col_to_process] is a Series, not a DataFrame, before shift
            if isinstance(grouped[col_to_process], pd.DataFrame):
                 df_out[feature_name] = grouped[col_to_process].iloc[:,0].shift(lag) # Assuming first column if DataFrame
            else:
                 df_out[feature_name] = grouped[col_to_process].shift(lag)
        
        # 计算滑动窗口统计特征
        for window in rolling_windows:
            # 均值
            mean_feat_name = f'feature_ts_{col_to_process}_roll_mean_{window}'
            # Corrected rolling operation for grouped data
            df_out[mean_feat_name] = grouped[col_to_process].rolling(window=window, min_periods=1).mean().reset_index(level=0, drop=True)
            # 标准差
            std_feat_name = f'feature_ts_{col_to_process}_roll_std_{window}'
            df_out[std_feat_name] = grouped[col_to_process].rolling(window=window, min_periods=1).std().reset_index(level=0, drop=True)
            # 可以添加更多: min, max, median, sum, var 等
            # max_feat_name = f'feature_ts_{col_to_process}_roll_max_{window}'
            # df_out[max_feat_name] = grouped[col_to_process].rolling(window=window, min_periods=1).max().reset_index(level=0, drop=True)

    # 时间序列分解 (如趋势、季节性、残差) 通常使用 statsmodels 库。
    # 这需要数据是清晰的单变量时间序列，并且具有足够的周期性。
    # 例如: from statsmodels.tsa.seasonal import seasonal_decompose
    # result = seasonal_decompose(df_group[col_to_process], model='additive', period=APPROPRIATE_PERIOD)
    # df_group[f'feature_ts_{col_to_process}_trend'] = result.trend
    # 这部分逻辑较为复杂，需要根据具体数据特性和周期进行调整，此处不作通用实现。
    # print(f"信息: 时间序列特征已为 {target_ts_cols} 生成完毕。")
    return df_out

def add_interaction_features(
    df: pd.DataFrame,
    manual_interaction_pairs: Optional[List[Tuple[str, str]]] = None,
    poly_degree: Optional[int] = None,
    poly_target_cols: Optional[List[str]] = None,
    poly_interaction_only: bool = False,
    poly_include_bias: bool = False
) -> pd.DataFrame:
    """
    添加特征交互项。
    可以手动指定交互对，或者使用多项式特征生成（来自sklearn.preprocessing.PolynomialFeatures）。
    """
    df_out = df.copy()

    # 1. 手动指定的交互特征对
    if manual_interaction_pairs:
        for col1, col2 in manual_interaction_pairs:
            if col1 in df_out.columns and col2 in df_out.columns:
                feature_name = f'feature_interact_{col1}_x_{col2}'
                df_out[feature_name] = df_out[col1] * df_out[col2]
                # logger.info(f"信息: 已创建手动交互特征 {feature_name}。")
            else: # Corresponds to: if col1 in df_out.columns and col2 in df_out.columns
                # This warning is for a specific pair failing
                logger.warning(f"警告: 创建手动交互特征对 ({col1} x {col2}) 失败，因为列 '{col1}' 或 '{col2}' 未在DataFrame中找到。")
    else: # Corresponds to: if manual_interaction_pairs
        logger.info("未提供手动交互特征对 (manual_interaction_pairs)，跳过手动创建部分。")

    # 2. 使用PolynomialFeatures生成多项式和交互特征
    if poly_degree and poly_target_cols:
        from sklearn.preprocessing import PolynomialFeatures
        
        existing_poly_target_cols = [col for col in poly_target_cols if col in df_out.columns]
        if not existing_poly_target_cols:
            print("警告: 未找到用于生成多项式特征的目标列，跳过PolynomialFeatures。")
            return df_out

        poly = PolynomialFeatures(
            degree=poly_degree,
            interaction_only=poly_interaction_only,
            include_bias=poly_include_bias
        )
        
        poly_features_array = poly.fit_transform(df_out[existing_poly_target_cols])
        poly_feature_names = poly.get_feature_names_out(existing_poly_target_cols)
        
        # 将生成的特征数组转换为DataFrame并合并
        # 注意：原始列可能会被包含在输出中（取决于degree和interaction_only），需要小心处理重复列名
        # get_feature_names_out 会给出有意义的列名
        
        for i, name in enumerate(poly_feature_names):
            # 清理一下列名，使其更可读
            clean_name = name.replace(" ", "_").replace("^", "pow")
            df_out[f'feature_poly_{clean_name}'] = poly_features_array[:, i]
        
        # print(f"信息: 已为列 {existing_poly_target_cols} 生成了 {poly_degree} 次多项式/交互特征。")

    # 更高级的特征交互发现方法（如基于树模型的特征重要性分析或决策树路径解析）
    # 不在此处直接实现，但用户可以考虑。
    return df_out

def add_physicochemical_derived_properties(df: pd.DataFrame) -> pd.DataFrame:
    """
    基于现有特征计算具有明确物理或化学意义的派生属性。
    这部分高度依赖领域知识和特定的物理化学公式或模型。
    用户需要根据自己的需求和知识库来实现这部分。
    """
    df_out = df.copy()
    # 示例：假设需要根据钢水温度和某种合金含量计算一个假想的"反应活性指数"
    # if COL_INTERACT_STEEL_TEMP_INITIAL in df_out.columns and COL_INTERACT_ALLOY_X_CONTENT in df_out.columns:
    #     df_out['feature_derived_reactivity_index'] = \
    #         (df_out[COL_INTERACT_STEEL_TEMP_INITIAL] / 1000) * (df_out[COL_INTERACT_ALLOY_X_CONTENT] + 0.1)
    #     print("信息: 已计算(示例)物理化学派生特征 'feature_derived_reactivity_index'。")
    
    logger.info("计算物理化学派生特征")
    return df_out

def calculate_phosphate_capacity(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算炉渣的磷容量系数和脱磷效率指数，基于炉渣成分和FactSage热力学计算。
    
    磷容量系数(Phosphate Capacity)可用来评估炉渣的脱磷能力，
    是表征炉渣-金属界面反应热力学倾向的重要参数。
    
    Args:
        df: 输入数据，包含炉渣各组分含量
        
    Returns:
        增加磷容量系数和脱磷效率指数特征的DataFrame
    """
    logger.info("计算炉渣磷容量系数和脱磷效率指数...")
    df_result = df.copy()
    
    # 检查输入数据是否包含必要的炉渣成分列
    required_slag_components = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                               'feature_slag_MgO_percent', 'feature_slag_FeO_percent']
    
    # Create default Series for missing components to avoid errors in calculations below
    for comp in required_slag_components:
        if comp not in df_result.columns:
            logger.warning(f"计算磷容量系数所需的炉渣成分 '{comp}' 缺失, 将使用默认值或0.")
            df_result[comp] = 0 # Default to 0 if missing for safety in calculations

    # 创建炉渣成分字典，使用默认值填充缺失的组分
    # These are now Series, so access them directly from df_result
    default_composition_values = {
        'feature_slag_CaO_percent': 45.0,
        'feature_slag_SiO2_percent': 15.0,
        'feature_slag_MgO_percent': 8.0,
        'feature_slag_FeO_percent': 20.0,
        'feature_slag_MnO_percent': 5.0,
        'feature_slag_P2O5_percent': 1.0,
        'feature_slag_Al2O3_percent': 3.0
    }
    
    slag_composition_series = {}
    for comp_col, default_val in default_composition_values.items():
        if comp_col in df_result.columns:
            slag_composition_series[comp_col.replace('feature_slag_','').replace('_percent','')] = df_result[comp_col].fillna(default_val)
        else:
            slag_composition_series[comp_col.replace('feature_slag_','').replace('_percent','')] = pd.Series([default_val] * len(df_result), index=df_result.index)

    # 计算光学碱度 (Optical Basicity)
    optical_basicity_values = {
        'CaO': 1.0, 'SiO2': 0.48, 'MgO': 0.78, 'FeO': 0.93,
        'MnO': 0.97, 'P2O5': 0.40, 'Al2O3': 0.60
    }
    
    molar_fractions_df = pd.DataFrame(index=df_result.index)
    for component_key, component_series in slag_composition_series.items():
        molar_mass_val = MOLAR_MASS.get(component_key, 70.0) 
        molar_fractions_df[component_key] = component_series / molar_mass_val
    
    total_moles_series = molar_fractions_df.sum(axis=1)
    # Avoid division by zero for total_moles_series
    safe_total_moles_series = total_moles_series.replace(0, np.nan) # Will make optical_basicity NaN if total_moles is 0

    optical_basicity_sum_terms = pd.Series(0.0, index=df_result.index)
    for component_key in slag_composition_series.keys(): # Use keys from slag_composition_series which are the short names
        normalized_molar_fraction = molar_fractions_df[component_key] / safe_total_moles_series
        optical_basicity_sum_terms += normalized_molar_fraction * optical_basicity_values.get(component_key, 0.7)
    
    df_result['feature_slag_optical_basicity'] = optical_basicity_sum_terms.fillna(0.7) # Fill NaN with a default if total moles was 0
    logger.info(f"Calculated 'feature_slag_optical_basicity'. Mean: {df_result['feature_slag_optical_basicity'].mean():.2f}")

    temperature_c = pd.Series(1600.0, index=df_result.index) # Default temperature
    if 'feature_steel_temperature_c' in df_result.columns: # Assuming this would be a target or known post-facto
        temperature_c = df_result['feature_steel_temperature_c'].fillna(1600.0)
    elif COL_HM_TEMP_C in df_result.columns:
        temperature_c = df_result[COL_HM_TEMP_C].fillna(1500.0) + 100 # HM temp + 100
    
    temperature_k = temperature_c + 273.15
    
    df_result['feature_slag_log_phosphate_capacity'] = 22.0 * df_result['feature_slag_optical_basicity'] - 11.0 + 0.02 * temperature_k
    logger.info(f"Calculated 'feature_slag_log_phosphate_capacity'. Mean: {df_result['feature_slag_log_phosphate_capacity'].mean():.2f}")
    
    df_result['feature_slag_phosphate_capacity'] = 10 ** df_result['feature_slag_log_phosphate_capacity']

    basicity_R2_for_dephos = df_result.get('feature_slag_basicity_R2', slag_composition_series['CaO'] / slag_composition_series['SiO2'].replace(0, np.nan)).fillna(2.5)
    feo_for_dephos = slag_composition_series['FeO']

    df_result['feature_slag_dephosphorization_index'] = (basicity_R2_for_dephos * feo_for_dephos) / temperature_k * 1000
    
    logger.info(f"炉渣光学碱度范围: {df_result['feature_slag_optical_basicity'].min():.2f}-{df_result['feature_slag_optical_basicity'].max():.2f}")
    logger.info(f"炉渣磷容量系数(log)范围: {df_result['feature_slag_log_phosphate_capacity'].min():.2f}-{df_result['feature_slag_log_phosphate_capacity'].max():.2f}")
    logger.info(f"脱磷效率指数范围: {df_result['feature_slag_dephosphorization_index'].min():.2f}-{df_result['feature_slag_dephosphorization_index'].max():.2f}")
    
    return df_result

def calculate_slag_phase_properties(df: pd.DataFrame) -> pd.DataFrame:
    """
    基于炉渣成分计算炉渣的相平衡特性，包括：
    - 液相百分比
    - 熔融温度区间
    - 固液界面特性
    
    该模型基于FactSage热力学数据库的计算结果构建的简化模型。
    
    Args:
        df: 输入数据，包含炉渣各组分含量
        
    Returns:
        增加炉渣相平衡特性特征的DataFrame
    """
    logger.info("计算炉渣相平衡特性...")
    df_result = df.copy()
    
    required_slag_components = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                               'feature_slag_MgO_percent', 'feature_slag_FeO_percent']
    for comp in required_slag_components:
        if comp not in df_result.columns:
            logger.warning(f"计算炉渣相平衡特性所需的炉渣成分 '{comp}' 缺失, 将使用默认值或0.")
            df_result[comp] = 0 

    default_composition_values = {
        'feature_slag_CaO_percent': 45.0, 'feature_slag_SiO2_percent': 15.0,
        'feature_slag_MgO_percent': 8.0,  'feature_slag_FeO_percent': 20.0,
        'feature_slag_MnO_percent': 5.0,  'feature_slag_P2O5_percent': 1.0,
        'feature_slag_Al2O3_percent': 3.0, 'feature_slag_Fe2O3_percent': 3.0
    }
    slag_composition = {}
    for comp_col, default_val in default_composition_values.items():
        slag_composition[comp_col.replace('feature_slag_','').replace('_percent','')] = df_result.get(comp_col, pd.Series([default_val] * len(df_result), index=df_result.index)).fillna(default_val)

    basicity = (slag_composition['CaO'] / slag_composition['SiO2'].replace(0, np.nan)).fillna(2.5) # Default basicity
    
    total_major = slag_composition['CaO'] + slag_composition['SiO2'] + slag_composition['FeO']
    safe_total_major = total_major.replace(0, np.nan)
    cao_norm = (slag_composition['CaO'] / safe_total_major * 100).fillna(0)
    sio2_norm = (slag_composition['SiO2'] / safe_total_major * 100).fillna(0)
    feo_norm = (slag_composition['FeO'] / safe_total_major * 100).fillna(0)
    
    liquidus_temp = (
        1200  
        - 3.5 * feo_norm  
        + 10 * (basicity - 2.5) ** 2  
        + 15 * slag_composition['MgO'] 
        - 8 * slag_composition['P2O5']  
        - 5 * slag_composition['MnO']   
    )
    
    solidus_temp = (
        liquidus_temp 
        - 150  
        + 10 * slag_composition['MgO']  
        + 8 * slag_composition['Al2O3'] 
        - 5 * slag_composition['FeO']  
    )
    
    temperature_c = pd.Series(1600.0, index=df_result.index)
    if 'feature_steel_temperature_c' in df_result.columns:
        temperature_c = df_result['feature_steel_temperature_c'].fillna(1600.0)
    elif COL_HM_TEMP_C in df_result.columns:
        temperature_c = df_result[COL_HM_TEMP_C].fillna(1500.0) + 100
    
    liquid_percent = pd.Series(0.0, index=df_result.index, dtype=float)
    solid_mask = temperature_c < solidus_temp
    liquid_mask = temperature_c > liquidus_temp
    pasty_mask = ~(solid_mask | liquid_mask)

    liquid_percent[solid_mask] = 0.0
    liquid_percent[liquid_mask] = 100.0
    
    # Avoid division by zero in pasty_mask if liquidus_temp == solidus_temp
    denominator_pasty = (liquidus_temp[pasty_mask] - solidus_temp[pasty_mask]).replace(0, np.nan)
    liquid_percent[pasty_mask] = (
        (temperature_c[pasty_mask] - solidus_temp[pasty_mask]) / 
        denominator_pasty * 100.0
    ).fillna(50.0) # Default to 50% if denominator was zero (e.g. pure substance)
    liquid_percent = liquid_percent.clip(0, 100) # Ensure it's within bounds

    df_result['feature_slag_liquidus_temp_c'] = liquidus_temp
    df_result['feature_slag_solidus_temp_c'] = solidus_temp
    df_result['feature_slag_liquid_percent'] = liquid_percent
    df_result['feature_slag_melting_range_c'] = (liquidus_temp - solidus_temp).clip(lower=0)

    logger.info(f"炉渣液相线温度范围: {df_result['feature_slag_liquidus_temp_c'].min():.1f}-{df_result['feature_slag_liquidus_temp_c'].max():.1f}°C")
    logger.info(f"炉渣液相百分比范围: {df_result['feature_slag_liquid_percent'].min():.1f}-{df_result['feature_slag_liquid_percent'].max():.1f}%")
    logger.info(f"炉渣熔融区间宽度范围: {df_result['feature_slag_melting_range_c'].min():.1f}-{df_result['feature_slag_melting_range_c'].max():.1f}°C")
    
    return df_result

def calculate_viscosity_from_factsage(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算炉渣粘度，基于FactSage热力学数据和网络结构模型。
    Args:
        df: 输入数据，包含炉渣各组分含量
    Returns:
        增加炉渣粘度和网络结构特征的DataFrame
    """
    logger.info("计算炉渣粘度和网络结构特性...")
    df_result = df.copy()
    
    required_slag_components = ['feature_slag_CaO_percent', 'feature_slag_SiO2_percent', 
                               'feature_slag_MgO_percent', 'feature_slag_FeO_percent']
    for comp in required_slag_components:
        if comp not in df_result.columns:
            logger.warning(f"计算炉渣粘度所需的炉渣成分 '{comp}' 缺失, 将使用默认值或0.")
            df_result[comp] = 0

    default_composition_values = {
        'feature_slag_CaO_percent': 45.0, 'feature_slag_SiO2_percent': 15.0,
        'feature_slag_MgO_percent': 8.0,  'feature_slag_FeO_percent': 20.0,
        'feature_slag_MnO_percent': 5.0,  'feature_slag_P2O5_percent': 1.0,
        'feature_slag_Al2O3_percent': 3.0, 'feature_slag_Fe2O3_percent': 3.0
    }
    slag_composition = {}
    for comp_col, default_val in default_composition_values.items():
        slag_composition[comp_col.replace('feature_slag_','').replace('_percent','')] = df_result.get(comp_col, pd.Series([default_val] * len(df_result), index=df_result.index)).fillna(default_val)

    network_formers_moles = (
        slag_composition['SiO2'] / MOLAR_MASS['SiO2'] + 
        slag_composition['P2O5'] / MOLAR_MASS['P2O5']
    )
    network_modifiers_moles = (
        slag_composition['CaO'] / MOLAR_MASS['CaO'] + 
        slag_composition['MgO'] / MOLAR_MASS['MgO'] + 
        slag_composition['FeO'] / MOLAR_MASS['FeO'] + 
        slag_composition['MnO'] / MOLAR_MASS['MnO']
    )
    amphoteric_al2o3_moles = slag_composition['Al2O3'] / MOLAR_MASS['Al2O3']
    amphoteric_fe2o3_moles = slag_composition['Fe2O3'] / MOLAR_MASS['Fe2O3']

    basicity = (slag_composition['CaO'] / slag_composition['SiO2'].replace(0, np.nan)).fillna(2.5)
    
    al2o3_as_former_ratio = np.where(basicity < 2.0, 0.7, 0.3)
    fe2o3_as_former_ratio = np.where(basicity < 1.5, 0.5, 0.1)
    
    adjusted_former_moles = network_formers_moles + \
                            al2o3_as_former_ratio * amphoteric_al2o3_moles + \
                            fe2o3_as_former_ratio * amphoteric_fe2o3_moles
    
    adjusted_modifier_moles = network_modifiers_moles + \
                             (1 - al2o3_as_former_ratio) * amphoteric_al2o3_moles + \
                             (1 - fe2o3_as_former_ratio) * amphoteric_fe2o3_moles
    
    total_adjusted_moles = adjusted_former_moles + adjusted_modifier_moles
    safe_total_adjusted_moles = total_adjusted_moles.replace(0, np.nan)
    network_former_mole_fraction = (adjusted_former_moles / safe_total_adjusted_moles).fillna(0.5) # Default if total moles is 0
    df_result['feature_slag_network_former_ratio'] = network_former_mole_fraction

    temperature_c = pd.Series(1600.0, index=df_result.index)
    if 'feature_steel_temperature_c' in df_result.columns:
        temperature_c = df_result['feature_steel_temperature_c'].fillna(1600.0)
    elif COL_HM_TEMP_C in df_result.columns:
        temperature_c = df_result[COL_HM_TEMP_C].fillna(1500.0) + 100
    temperature_k = temperature_c + 273.15
    
    A, B, C = -6.0, 30000, 10.0
    ln_viscosity_pas = A + B / temperature_k.replace(0, np.nan) + C * network_former_mole_fraction # Avoid div by zero for T
    viscosity_poise = np.exp(ln_viscosity_pas.fillna(np.log(0.5))) * 10 # Default viscosity 0.5 Pa.s if NaN

    liquid_percent_for_visc = df_result.get('feature_slag_liquid_percent', pd.Series(100.0, index=df_result.index)).fillna(100.0)
    liquid_fraction = liquid_percent_for_visc / 100.0
    effective_viscosity = viscosity_poise * (1 + 10 * (1 - liquid_fraction) ** 3)
    df_result['feature_slag_viscosity'] = effective_viscosity.clip(upper=1000.0)
    logger.info(f"炉渣粘度范围: {df_result['feature_slag_viscosity'].min():.2f}-{df_result['feature_slag_viscosity'].max():.2f} poise")
    logger.info(f"网络形成体比例范围: {df_result['feature_slag_network_former_ratio'].min():.2f}-{df_result['feature_slag_network_former_ratio'].max():.2f}")
    
    return df_result

def add_lance_dynamics_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    添加喷枪动态特征，基于喷枪位置、角度和供氧参数。
    Args:
        df: 输入数据，包含喷枪位置和供氧参数
    Returns:
        增加喷枪动态特征的DataFrame
    """
    logger.info("添加喷枪动态特征...")
    df_result = df.copy()
    
    lance_height_col = '枪位' if '枪位' in df_result.columns else COL_LANCE_HEIGHT_MM
    oxygen_flow_col = '累氧实际' if '累氧实际' in df_result.columns else COL_OXYGEN_TOTAL_NM3
    blowing_time_col = '吹氧时间s' if '吹氧时间s' in df_result.columns else COL_BLOWING_TIME_SEC
    lance_angle_col = '最大角度' if '最大角度' in df_result.columns else COL_LANCE_ANGLE_MAX

    if lance_height_col not in df_result.columns:
        logger.warning("缺少喷枪高度列，无法计算喷枪动态特征")
        return df_result
    # Ensure numeric, fillna with a typical value if missing to avoid downstream errors
    df_result[lance_height_col] = pd.to_numeric(df_result[lance_height_col], errors='coerce').fillna(1000)

    if oxygen_flow_col in df_result.columns and blowing_time_col in df_result.columns:
        df_result[oxygen_flow_col] = pd.to_numeric(df_result[oxygen_flow_col], errors='coerce').fillna(0)
        df_result[blowing_time_col] = pd.to_numeric(df_result[blowing_time_col], errors='coerce').fillna(1)
        
        # Avoid division by zero for blowing_time_col
        safe_blowing_time_min = (df_result[blowing_time_col] / 60).replace(0, np.nan)
        oxygen_flow_rate = (df_result[oxygen_flow_col] / safe_blowing_time_min).fillna(0)
        df_result['feature_lance_oxygen_flow_rate'] = oxygen_flow_rate
        
        lance_height_m = df_result[lance_height_col] / 1000
        # Avoid division by zero for lance_height_m squared
        safe_lance_height_m_sq = (lance_height_m ** 2).replace(0, np.nan)
        jet_momentum = (oxygen_flow_rate / safe_lance_height_m_sq).fillna(0)
        df_result['feature_lance_jet_momentum_index'] = jet_momentum
        logger.info(f"喷射动量指数范围: {jet_momentum.min():.1f}-{jet_momentum.max():.1f}")

        impact_depth = 0.05 * df_result['feature_lance_jet_momentum_index'] ** 0.5
        df_result['feature_lance_impact_depth_index'] = impact_depth
        logger.info(f"冲击深度指数范围: {impact_depth.min():.2f}-{impact_depth.max():.2f}")

        # Ensure lance_height_m for stirring calculation is safe
        surface_stirring = df_result['feature_lance_jet_momentum_index'] * lance_height_m.replace(0, 0.1) # Use small non-zero if 0
        df_result['feature_lance_surface_stirring_index'] = surface_stirring
        logger.info(f"表面搅拌强度指数范围: {surface_stirring.min():.1f}-{surface_stirring.max():.1f}")

    else:
        logger.warning("缺少供氧量或吹氧时间列，部分喷枪动态特征无法计算")
        df_result['feature_lance_oxygen_flow_rate'] = 0
        df_result['feature_lance_jet_momentum_index'] = 0
        df_result['feature_lance_impact_depth_index'] = 0
        df_result['feature_lance_surface_stirring_index'] = 0
    
    if lance_angle_col in df_result.columns:
        df_result[lance_angle_col] = pd.to_numeric(df_result[lance_angle_col], errors='coerce').fillna(0)
        angle_rad = np.radians(df_result[lance_angle_col])
        effective_momentum_factor = np.cos(angle_rad)
        df_result['feature_lance_effective_momentum'] = df_result.get('feature_lance_jet_momentum_index', 0) * effective_momentum_factor
        df_result['feature_lance_effective_impact_depth'] = df_result.get('feature_lance_impact_depth_index', 0) * effective_momentum_factor
        logger.info(f"考虑了喷枪角度的影响，角度范围: {df_result[lance_angle_col].min():.1f}°-{df_result[lance_angle_col].max():.1f}°")
    
    # Oxygen Utilization
    if lance_height_col in df_result.columns: # Already checked but good for clarity
        norm_height = (df_result[lance_height_col] - 800) / 400
        norm_height = norm_height.clip(0, 1)
        oxygen_utilization = 0.90 - 0.15 * norm_height
        if 'feature_slag_FeO_percent' in df_result.columns:
            feo_for_util = df_result['feature_slag_FeO_percent'].fillna(DEFAULT_SLAG_FEO_PERCENT)
            feo_factor = feo_for_util / 30.0
            feo_factor = feo_factor.clip(0.5, 1.5)
            # Avoid division by zero for feo_factor
            oxygen_utilization = (oxygen_utilization / feo_factor.replace(0, np.nan)).fillna(0.8) # Default if div by zero
        df_result['feature_lance_oxygen_utilization'] = oxygen_utilization
        logger.info(f"氧气利用率估算范围: {oxygen_utilization.min():.2f}-{oxygen_utilization.max():.2f}")
    else: # Should not happen if first check passes
        df_result['feature_lance_oxygen_utilization'] = 0.8 # Default

    return df_result

def add_slag_process_interactions(df: pd.DataFrame) -> pd.DataFrame:
    """
    创建炉渣特性与工艺参数之间的交互特征。
    Args:
        df: 输入数据，包含炉渣特性和工艺参数
    Returns:
        增加炉渣-工艺交互特征的DataFrame
    """
    logger.info("创建炉渣-工艺参数交互特征...")
    df_result = df.copy()
    
    slag_features_names = [
        'feature_slag_basicity_R2', 'feature_slag_FeO_percent', 'feature_slag_liquid_percent',
        'feature_slag_viscosity', 'feature_slag_optical_basicity', 'feature_slag_log_phosphate_capacity'
    ]
    process_features_names = [
        'feature_lance_jet_momentum_index', 'feature_lance_oxygen_flow_rate',
        'feature_lance_surface_stirring_index', 'feature_lance_oxygen_utilization',
        COL_LANCE_HEIGHT_MM if COL_LANCE_HEIGHT_MM in df_result.columns else '枪位',
        '吹氧时间s', '单位铁水供氧量'
    ]

    for s_feat in slag_features_names:
        for p_feat in process_features_names:
            if s_feat in df_result.columns and p_feat in df_result.columns:
                # Sanitize feature names for the interaction term
                s_feat_clean = s_feat.replace('feature_slag_','').replace('feature_lance_','').replace('feature_','')
                p_feat_clean = p_feat.replace('feature_slag_','').replace('feature_lance_','').replace('feature_','')
                interaction_name = f'feature_interact_{s_feat_clean}_x_{p_feat_clean}'
                df_result[interaction_name] = df_result[s_feat] * df_result[p_feat]
                logger.info(f"创建交互特征: {interaction_name}")
            # else:
                # logger.debug(f"Skipping interaction: {s_feat} x {p_feat} (one or both missing)")
    
    return df_result

def _calculate_slag_oxide_percentages(df: pd.DataFrame) -> pd.DataFrame:
    """
    基于各氧化物质量和总炉渣质量，计算各氧化物的百分比。
    确保在调用此函数前，COL_SLAG_TOTAL_MASS_KG ('feature_slag_total_mass_kg') 和
    各氧化物的质量列 (如 'feature_slag_SiO2_from_Si_kg') 已被计算。
    """
    df_result = df.copy()
    logger.info("开始计算炉渣各氧化物百分比...")

    oxide_mass_cols_map = {
        'feature_slag_SiO2_percent': 'feature_slag_SiO2_from_Si_kg',
        'feature_slag_MnO_percent': 'feature_slag_MnO_from_Mn_kg',
        'feature_slag_P2O5_percent': 'feature_slag_P2O5_from_P_kg',
        'feature_slag_CaO_percent': 'feature_slag_CaO_total_kg',
        'feature_slag_MgO_percent': 'feature_slag_MgO_total_kg',
        'feature_slag_Al2O3_percent': 'feature_slag_Al2O3_total_kg',
        'feature_slag_FeO_percent': 'feature_slag_FeO_kg' # FeO percent is now also calculated from its mass relative to total
    }

    if COL_SLAG_TOTAL_MASS_KG not in df_result.columns or df_result[COL_SLAG_TOTAL_MASS_KG].isna().all():
        logger.error(f"列 '{COL_SLAG_TOTAL_MASS_KG}' 缺失或全为NaN，无法计算氧化物百分比。")
        for percent_col in oxide_mass_cols_map.keys():
            if percent_col not in df_result.columns:
                df_result[percent_col] = 0 # Or np.nan
        return df_result
    
    total_slag_mass_safe = df_result[COL_SLAG_TOTAL_MASS_KG].replace(0, np.nan) 

    for percent_col, mass_col in oxide_mass_cols_map.items():
        if mass_col in df_result.columns and df_result[mass_col].notna().any():
            df_result[percent_col] = (df_result[mass_col] / total_slag_mass_safe) * 100
            df_result[percent_col].fillna(0, inplace=True) 
            logger.info(f"计算了 '{percent_col}', 平均值: {df_result[percent_col].mean():.2f}%")
        else:
            logger.warning(f"质量列 '{mass_col}' 未找到或全空，无法计算 '{percent_col}'. 已填充为0。")
            if percent_col not in df_result.columns: # Ensure column exists if it was expected
                 df_result[percent_col] = 0
            else:
                 df_result[percent_col].fillna(0, inplace=True) # Fill if exists but calculation failed
                 
    # Recalculate FeO percent based on FeO_kg and total_slag_mass if COL_SLAG_FEO_MEASURED_PERCENT was not used or was problematic
    # This ensures feature_slag_FeO_percent is consistent with feature_slag_FeO_kg and feature_slag_total_mass_kg
    if 'feature_slag_FeO_kg' in df_result.columns and total_slag_mass_safe is not None:
        df_result['feature_slag_FeO_percent'] = (df_result['feature_slag_FeO_kg'] / total_slag_mass_safe * 100).fillna(DEFAULT_SLAG_FEO_PERCENT)
        logger.info(f"Re-calculated/ensured 'feature_slag_FeO_percent' based on FeO_kg. Mean: {df_result['feature_slag_FeO_percent'].mean():.2f}%")

    logger.info("炉渣各氧化物百分比计算完成。")
    return df_result

def add_advanced_slag_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    整合所有基于FactSage的高级炉渣特征计算。
    """
    logger.info("计算基于FactSage的高级炉渣特征")
    df_result = df.copy()
    logger.info(f"[add_advanced_slag_features START] df_result columns: {df_result.columns.tolist()}")

    # Step 1: Ensure base oxide masses and initial total slag (pre-Fe2O3, pre-refined FeO) are there
    # _calculate_individual_oxide_masses_and_total_slag should be called before this function.
    
    # Step 2: Calculate FeO and Fe2O3 content and refine total slag mass
    # This was previously a separate function `calculate_fe2o3_content` which is now integrated or simplified
    # For now, assume FeO_kg is already calculated by _calculate_individual_oxide_masses_and_total_slag
    # and FeO_percent is either measured or default. We will use these to calculate other oxide percentages.
    # A proper Fe2O3 calculation is complex and might require thermodynamic modeling or assumptions.
    # For simplicity, if Fe2O3 is needed, it could be a small fixed percentage or derived.
    if 'feature_slag_Fe2O3_percent' not in df_result.columns:
        # Simplified: Assume a small default Fe2O3 percentage if not otherwise provided/calculated
        # df_result['feature_slag_Fe2O3_percent'] = 1.0 
        # logger.info("'feature_slag_Fe2O3_percent' not found, defaulted to 1.0%")
        pass # Let's assume it might not be strictly needed by downstream functions for now or handled by them

    # Step 3: Calculate all oxide percentages based on their masses and the *final* total slag mass
    df_result = _calculate_slag_oxide_percentages(df_result)
    logger.info(f"[AFTER _calculate_slag_oxide_percentages in add_advanced_slag_features] df_result columns: {df_result.columns.tolist()}")

    # Step 4: Calculate advanced properties that depend on these percentages
    df_result = calculate_phosphate_capacity(df_result)
    df_result = calculate_slag_phase_properties(df_result)
    df_result = calculate_viscosity_from_factsage(df_result)
    
    logger.info(f"[add_advanced_slag_features END] df_result columns: {df_result.columns.tolist()}")
    return df_result

def _calculate_individual_oxide_masses_and_total_slag(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算各种氧化物的质量以及总炉渣量。
    """
    logger.info("开始计算各个氧化物的质量...")
    df_result = df.copy()

    def convert_to_numeric_and_log(df_ref, col_name_original, default_val=0):
        if col_name_original in df_ref.columns:
            # logger.info(f"原始 '{col_name_original}' 列 dtype: {df_ref[col_name_original].dtype}")
            # logger.info(f"原始 '{col_name_original}' 列 head:\n{df_ref[col_name_original].head(2)}")
            numeric_series = pd.to_numeric(df_ref[col_name_original], errors='coerce').fillna(default_val)
            # logger.info(f"'{col_name_original}' (转换后) head:\n{numeric_series.head(2)}")
            return numeric_series
        else:
            logger.warning(f"'{col_name_original}' 列不存在，将创建全为 {default_val} 的系列。")
            return pd.Series([default_val] * len(df_ref), index=df_ref.index)

    iron_mass_series = convert_to_numeric_and_log(df_result, COL_HM_MASS_KG, default_val=80000) # Default if missing
    steel_mass_series = convert_to_numeric_and_log(df_result, COL_STEEL_MASS_KG, default_val=iron_mass_series) # Default to iron mass if missing
    scrap_mass_series = convert_to_numeric_and_log(df_result, COL_SCRAP_MASS_KG, default_val=0)

    element_oxide_map = {
        COL_HM_SI_PERCENT: (COL_STEEL_SI_FINAL_PERCENT, 'Si_to_SiO2', 'feature_slag_SiO2_from_Si_kg', 0.05),
        COL_HM_MN_PERCENT: (COL_STEEL_MN_FINAL_PERCENT, 'Mn_to_MnO', 'feature_slag_MnO_from_Mn_kg', 0.4),
        COL_HM_P_PERCENT: (COL_STEEL_P_FINAL_PERCENT, 'P_to_P2O5', 'feature_slag_P2O5_from_P_kg', 0.1)
    }

    for iron_col, (steel_col_name, ratio_key, feature_name, default_steel_factor) in element_oxide_map.items():
        element_in_iron_pct = convert_to_numeric_and_log(df_result, iron_col, 0)
        element_in_iron_kg = element_in_iron_pct * iron_mass_series / 100
        
        element_in_steel_pct = convert_to_numeric_and_log(df_result, steel_col_name, np.nan) # Use NaN to detect missing
        element_in_steel_kg = pd.Series(0.0, index=df_result.index)
        if element_in_steel_pct.notna().any(): # If any steel data is present
            element_in_steel_kg = element_in_steel_pct * steel_mass_series / 100
        else:
            logger.warning(f"钢水成分列 '{steel_col_name}' 未找到或全空，使用默认氧化率估算钢中残余 {iron_col.replace(COL_HM_MASS_KG,'')}")
            element_in_steel_kg = element_in_iron_kg * default_steel_factor
            
        oxidized_element_kg = (element_in_iron_kg - element_in_steel_kg).clip(lower=0)
        df_result[feature_name] = oxidized_element_kg * ELEMENT_TO_OXIDE_MASS_RATIO[ratio_key]
        logger.info(f"计算了 {feature_name}: {df_result[feature_name].mean():.2f} kg (平均值)")

    lime_kg = convert_to_numeric_and_log(df_result, COL_LIME_ADDITION_KG, 0)
    dolomite_kg = convert_to_numeric_and_log(df_result, COL_DOLOMITE_ADDITION_KG, 0)
    bauxite_kg = convert_to_numeric_and_log(df_result, COL_BAUXITE_ADDITION_KG, 0)

    df_result['feature_slag_CaO_from_lime_kg'] = lime_kg * FLUX_COMPONENT_RATIO.get('CaO_in_Lime', 0.90)
    df_result['feature_slag_CaO_from_dolomite_kg'] = dolomite_kg * FLUX_COMPONENT_RATIO.get('CaO_in_Dolomite', 0.58)
    df_result['feature_slag_CaO_total_kg'] = df_result['feature_slag_CaO_from_lime_kg'] + df_result['feature_slag_CaO_from_dolomite_kg']
    logger.info(f"总CaO质量: {df_result['feature_slag_CaO_total_kg'].mean():.2f} kg (平均值)")
    
    df_result['feature_slag_MgO_total_kg'] = dolomite_kg * FLUX_COMPONENT_RATIO.get('MgO_in_Dolomite', 0.40)
    logger.info(f"MgO质量: {df_result['feature_slag_MgO_total_kg'].mean():.2f} kg (平均值)")
    
    df_result['feature_slag_Al2O3_total_kg'] = bauxite_kg * FLUX_COMPONENT_RATIO.get('Al2O3_in_Bauxite', 0.70)
    if COL_BAUXITE_ADDITION_KG not in df_result.columns:
         df_result['feature_slag_Al2O3_total_kg'] = FLUX_COMPONENT_RATIO.get('Al2O3_default_kg', 10) # Default small amount if no bauxite col
         logger.info("'铝矾土' 列未在原始数据中找到，Al2O3质量使用默认值10kg.")
    logger.info(f"Al2O3质量: {df_result['feature_slag_Al2O3_total_kg'].mean():.2f} kg (平均值)")

    known_oxide_cols_pre_feo = [
        'feature_slag_SiO2_from_Si_kg', 'feature_slag_MnO_from_Mn_kg', 'feature_slag_P2O5_from_P_kg',
        'feature_slag_CaO_total_kg', 'feature_slag_MgO_total_kg', 'feature_slag_Al2O3_total_kg'
    ]
    # Ensure all these columns exist before summing, fill with 0 if one was missed in creation due to lack of inputs
    for col in known_oxide_cols_pre_feo:
        if col not in df_result.columns:
            df_result[col] = 0.0
            logger.warning(f"Column '{col}' was missing before summing known_oxide_cols_pre_feo, filled with 0.")
            
    df_result['feature_slag_known_oxides_kg_pre_feo'] = df_result[known_oxide_cols_pre_feo].sum(axis=1)
    logger.info(f"feature_slag_known_oxides_kg_pre_feo (不含FeO的氧化物总和) 计算完成. 平均值: {df_result['feature_slag_known_oxides_kg_pre_feo'].mean():.2f} kg")

    # FeO Calculation (crucial for total slag mass)
    feo_percent_series = convert_to_numeric_and_log(df_result, COL_SLAG_FEO_MEASURED_PERCENT, np.nan)
    # Use default if measured is NaN or invalid (e.g. 0, or >100 if not pre-capped)
    df_result['feature_slag_FeO_percent'] = feo_percent_series.where(feo_percent_series.notna() & (feo_percent_series > 0) & (feo_percent_series < 100), DEFAULT_SLAG_FEO_PERCENT)
    logger.info(f"feature_slag_FeO_percent 确定. 平均值: {df_result['feature_slag_FeO_percent'].mean():.2f}%")

    feo_fraction = df_result['feature_slag_FeO_percent'] / 100.0
    denominator_for_feo_calc = (1.0 - feo_fraction).replace(0, np.nan) # Avoid division by zero or by negative if feo_fraction >= 1
    
    df_result['feature_slag_FeO_kg'] = (df_result['feature_slag_known_oxides_kg_pre_feo'] * feo_fraction / denominator_for_feo_calc)
    # If calculation resulted in NaN (e.g. FeO was 100%), estimate FeO_kg based on iron mass.
    # This is a fallback; ideally, FeO % should not be 100 if other oxides are present.
    estimated_feo_kg_fallback = iron_mass_series * (DEFAULT_SLAG_FEO_PERCENT / 100.0) * 0.15 # e.g. 15% of iron mass as slag, then FeO portion
    df_result['feature_slag_FeO_kg'].fillna(estimated_feo_kg_fallback, inplace=True)
    df_result['feature_slag_FeO_kg'] = df_result['feature_slag_FeO_kg'].clip(lower=0) # Ensure non-negative
    logger.info(f"feature_slag_FeO_kg 计算完成. 平均值: {df_result['feature_slag_FeO_kg'].mean():.2f} kg")

    df_result[COL_SLAG_TOTAL_MASS_KG] = df_result['feature_slag_known_oxides_kg_pre_feo'] + df_result['feature_slag_FeO_kg']
    logger.info(f"最终总炉渣质量 ({COL_SLAG_TOTAL_MASS_KG}) 计算完成. 平均值: {df_result[COL_SLAG_TOTAL_MASS_KG].mean():.2f} kg")

    if 'feature_slag_known_oxides_kg' in ACTUAL_MODEL_INPUT_FEATURES: # This is one of the target features
        df_result['feature_slag_known_oxides_kg'] = df_result['feature_slag_known_oxides_kg_pre_feo']
    
    if 'feature_slag_known_oxides_kg_pre_feo' in df_result.columns: # Clean up temporary column
        df_result.drop(columns=['feature_slag_known_oxides_kg_pre_feo'], inplace=True)

    logger.info("炉渣 FeO 和总质量相关计算已更新。")
    return df_result

# --- 主特征工程函数 ---
def engineer_all_features(
    df_input: pd.DataFrame,
    # --- 各模块启用开关 ---
    enable_slag: bool = True,
    enable_process_params: bool = True,
    enable_time_series: bool = True, 
    enable_interactions: bool = True,
    enable_physicochem_props: bool = True, 
    enable_advanced_slag: bool = True,  
    enable_lance_dynamics: bool = True, 
    # --- 时间序列特征配置 ---
    ts_group_col: str = COL_TS_HEAT_ID,
    ts_time_col: str = COL_TS_TIMESTAMP,
    ts_target_cols_list: Optional[List[str]] = None, 
    ts_lag_periods_list: Optional[List[int]] = None, 
    ts_rolling_windows_list: Optional[List[int]] = None, 
    # --- 交互特征配置 ---
    interactions_manual_pairs: Optional[List[Tuple[str, str]]] = None,
    interactions_poly_config: Optional[Dict] = None 
) -> pd.DataFrame:
    """
    主函数，按顺序调用各个特征工程模块。
    用户需要根据自己的数据和需求调整这里的调用和参数。
    """
    logger.info("开始运行 engineer_all_features...")
    df_processed = df_input.copy()
    logger.info(f"[engineer_all_features START] df_processed (df_input copy) columns: {df_processed.columns.tolist()}")

    if enable_slag:
        logger.info("--- 正在处理炉渣相关特征 (基础氧化物质量和总炉渣) ---")
        df_processed = _calculate_individual_oxide_masses_and_total_slag(df_processed)
        logger.info(f"[AFTER _calculate_individual_oxide_masses_and_total_slag] df_processed columns count: {len(df_processed.columns)}")
        
        if enable_advanced_slag:
            logger.info("--- 正在处理高级炉渣特征 (百分比, 物化性质) ---")
            df_processed = add_advanced_slag_features(df_processed) 
            # Basic slag basicity R2 is now part of ACTUAL_MODEL_INPUT_FEATURES and should be calculated by add_advanced_slag_features or its sub-functions.
            # If calculate_slag_basicity is called here, it might be redundant or operate on already existing 'feature_slag_basicity_R2'
            # Let's ensure calculate_slag_basicity is robustly called within add_advanced_slag_features if needed, or ensure its outputs are available.
            # For now, assuming add_advanced_slag_features handles all required advanced slag features including basicity R2.
            # If 'feature_slag_basicity_R2' is not created by add_advanced_slag_features, it will be caught by the final alignment step.
            logger.info(f"[AFTER add_advanced_slag_features] df_processed columns count: {len(df_processed.columns)}")
        
        slag_features_created_count = len([col for col in df_processed.columns if col.startswith('feature_slag_')])
        if slag_features_created_count > 0:
            logger.info(f"已创建 {slag_features_created_count} 个炉渣相关特征")
        else:
            logger.warning("未能创建任何炉渣相关特征，请检查输入数据和 _calculate_individual_oxide_masses_and_total_slag / add_advanced_slag_features 函数")

    if enable_process_params:
        logger.info("--- 正在处理工艺参数特征 ---")
        df_processed = add_quantified_process_parameters(df_processed)
        
        if enable_lance_dynamics:
            logger.info("--- 正在处理枪位动态特征 ---")
            df_processed = add_lance_dynamics_features(df_processed)

    if enable_time_series:
        logger.info("--- 正在处理时间序列特征 ---")
        if not ts_target_cols_list:
            logger.warning("未提供时间序列目标列 (ts_target_cols_list)，跳过时间序列特征生成。")
        else:
            df_processed = add_time_series_derived_features(
                df_processed,
                group_by_col=ts_group_col,
                time_col=ts_time_col,
                target_ts_cols=ts_target_cols_list,
                lag_periods=ts_lag_periods_list if ts_lag_periods_list else [],
                rolling_windows=ts_rolling_windows_list if ts_rolling_windows_list else []
            )

    if enable_interactions:
        logger.info("--- 正在处理交互特征 ---")
        poly_degree_val, poly_target_cols_val = None, None
        poly_interaction_only_val, poly_include_bias_val = False, False
        if interactions_poly_config:
            poly_degree_val = interactions_poly_config.get('degree')
            poly_target_cols_val = interactions_poly_config.get('target_cols')
            poly_interaction_only_val = interactions_poly_config.get('interaction_only', False)
            poly_include_bias_val = interactions_poly_config.get('include_bias', False)
            
        df_processed = add_interaction_features(
            df_processed,
            manual_interaction_pairs=interactions_manual_pairs,
            poly_degree=poly_degree_val,
            poly_target_cols=poly_target_cols_val,
            poly_interaction_only=poly_interaction_only_val,
            poly_include_bias=poly_include_bias_val
        )
        
        if enable_slag and enable_process_params and (enable_advanced_slag or enable_lance_dynamics):
            logger.info("--- 正在处理炉渣-工艺参数交互特征 --- (add_slag_process_interactions)")
            df_processed = add_slag_process_interactions(df_processed)

    if enable_physicochem_props:
        logger.info("--- 正在处理物理化学派生属性 (add_physicochemical_derived_properties - currently a placeholder) ---")
        df_processed = add_physicochemical_derived_properties(df_processed)

    logger.info(f"特征工程模块化步骤完成。当前总特征数: {len(df_processed.columns)}.")
    logger.info(f"开始对齐到期望的 {len(ACTUAL_MODEL_INPUT_FEATURES)} 个输入特征: {ACTUAL_MODEL_INPUT_FEATURES[:5]}...")

    final_feature_columns = list(ACTUAL_MODEL_INPUT_FEATURES) 
    original_columns_in_df_processed = df_processed.columns.tolist()
    df_aligned = pd.DataFrame(index=df_processed.index)
    missing_features_filled = []
    present_features_used = []

    for col in final_feature_columns:
        if col in original_columns_in_df_processed:
            df_aligned[col] = df_processed[col]
            present_features_used.append(col)
        else:
            df_aligned[col] = 0  
            missing_features_filled.append(col)
            logger.warning(f"  期望特征 '{col}' 在生成步骤中缺失，已自动填充为0.")

    if missing_features_filled:
        logger.warning(f"总共 {len(missing_features_filled)} 个期望特征在生成步骤中缺失并填充为0.")
    
    logger.info(f"从原始生成特征中采纳了 {len(present_features_used)} 个特征.")

    if not df_aligned.columns.equals(pd.Index(final_feature_columns)):
        logger.error("CRITICAL: df_aligned 的列与 final_feature_columns 不完全匹配或顺序不一致！")
        try:
            df_aligned = df_aligned[final_feature_columns]
            logger.warning("尝试通过重排强制对齐列顺序。")
        except KeyError as e:
            logger.error(f"强制重排失败: {e}. 特征集可能不完整。")
            still_missing_after_align_attempt = [c for c in final_feature_columns if c not in df_aligned.columns]
            if still_missing_after_align_attempt:
                 logger.error(f"重排尝试后仍然缺失的特征: {still_missing_after_align_attempt}")

    generated_but_not_in_target = set(original_columns_in_df_processed) - set(final_feature_columns)
    if generated_but_not_in_target:
        logger.info(f"{len(generated_but_not_in_target)} 个特征在工程化中生成但最终未被选中到 {len(ACTUAL_MODEL_INPUT_FEATURES)} 个输入特征中 (被舍弃). ({list(generated_but_not_in_target)[:10]}...)")
            
    logger.info(f"特征工程处理完毕。最终返回的特征数量: {df_aligned.shape[1]}.")
    
    if not df_aligned.empty:
        nan_cols = df_aligned.isnull().any()
        num_nan_cols = nan_cols.sum()
        if num_nan_cols > 0:
            logger.warning(f"  最终特征集有 {num_nan_cols} 列包含NaN值: {df_aligned.columns[nan_cols].tolist()}")
        
        all_zero_cols = (df_aligned == 0).all()
        num_all_zero_cols = all_zero_cols.sum()
        if num_all_zero_cols > 0:
            logger.warning(f"  最终特征集有 {num_all_zero_cols} 列所有值都为0: {df_aligned.columns[all_zero_cols].tolist()}")
    else:
        logger.warning("特征工程结果为空 DataFrame。")

    return df_aligned

# --- 使用示例 (将此部分放在您的数据加载和预处理脚本中) ---
if __name__ == '__main__':
    pass # Example usage removed for brevity in this context
