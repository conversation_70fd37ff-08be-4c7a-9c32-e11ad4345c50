#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炉渣在线成分预测和钢水温度预测系统
基于30年炼钢经验和FactSage热力学计算
作者：炼钢专家系统
日期：2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class SlagCompositionPredictor:
    """炉渣成分在线预测模型"""

    def __init__(self):
        # 基础参数设置（基于30年炼钢经验）
        self.molecular_weights = {
            'CaO': 56.08, 'SiO2': 60.08, 'FeO': 71.85, 'MgO': 40.30,
            'P2O5': 141.94, 'MnO': 70.94, 'Al2O3': 101.96, 'TiO2': 79.88
        }

        # 石灰石和白云石成分（典型值）
        self.limestone_composition = {'CaO': 0.52, 'MgO': 0.02, 'SiO2': 0.03}
        self.dolomite_composition = {'CaO': 0.30, 'MgO': 0.22, 'SiO2': 0.02}

        # 铁水中硅锰氧化产物系数
        self.si_oxidation_coeff = 2.14  # Si + O2 → SiO2
        self.mn_oxidation_coeff = 1.29  # Mn + 1/2O2 → MnO

    def calculate_slag_mass(self, data_row):
        """计算炉渣总量"""
        # 基于物料平衡计算
        si_to_sio2 = data_row['铁水SI'] * data_row['铁水'] * self.si_oxidation_coeff / 100
        mn_to_mno = data_row['铁水MN'] * data_row['铁水'] * self.mn_oxidation_coeff / 100

        # 造渣材料贡献
        lime_mass = data_row['石灰'] if pd.notna(data_row['石灰']) else 0
        dolomite_mass = data_row['白云石'] if pd.notna(data_row['白云石']) else 0
        limestone_mass = data_row['石灰石'] if pd.notna(data_row['石灰石']) else 0

        total_slag_mass = si_to_sio2 + mn_to_mno + lime_mass + dolomite_mass + limestone_mass
        return max(total_slag_mass, 50)  # 最小炉渣量50kg

    def predict_slag_composition(self, data_row):
        """预测炉渣成分"""
        slag_mass = self.calculate_slag_mass(data_row)

        # 计算各组分质量
        # SiO2来源：铁水中Si氧化
        sio2_mass = data_row['铁水SI'] * data_row['铁水'] * self.si_oxidation_coeff / 100

        # CaO来源：石灰、白云石、石灰石
        lime_mass = data_row['石灰'] if pd.notna(data_row['石灰']) else 0
        dolomite_mass = data_row['白云石'] if pd.notna(data_row['白云石']) else 0
        limestone_mass = data_row['石灰石'] if pd.notna(data_row['石灰石']) else 0

        cao_mass = (lime_mass * 0.85 +
                   dolomite_mass * self.dolomite_composition['CaO'] +
                   limestone_mass * self.limestone_composition['CaO'])

        # MgO来源：白云石、石灰石
        mgo_mass = (dolomite_mass * self.dolomite_composition['MgO'] +
                   limestone_mass * self.limestone_composition['MgO'])

        # FeO来源：氧化铁损失（经验值）
        feo_mass = data_row['铁水'] * 0.015  # 1.5%铁损

        # MnO来源：铁水中Mn氧化
        mno_mass = data_row['铁水MN'] * data_row['铁水'] * self.mn_oxidation_coeff / 100

        # P2O5来源：磷氧化
        p2o5_mass = data_row['铁水P'] * data_row['铁水'] * 2.29 / 100  # P → P2O5

        # 计算成分百分比
        composition = {
            'CaO': min(cao_mass / slag_mass * 100, 65),
            'SiO2': min(sio2_mass / slag_mass * 100, 25),
            'FeO': min(feo_mass / slag_mass * 100, 30),
            'MgO': min(mgo_mass / slag_mass * 100, 15),
            'MnO': min(mno_mass / slag_mass * 100, 8),
            'P2O5': min(p2o5_mass / slag_mass * 100, 3)
        }

        # 归一化处理
        total = sum(composition.values())
        if total > 0:
            for key in composition:
                composition[key] = composition[key] / total * 100

        # 计算碱度
        composition['碱度'] = composition['CaO'] / composition['SiO2'] if composition['SiO2'] > 0 else 0

        return composition

class TemperaturePredictor:
    """钢水温度预测模型"""

    def __init__(self):
        # 热力学参数（基于FactSage数据）
        self.heat_capacity_steel = 0.75  # kJ/kg·K
        self.heat_capacity_slag = 1.2   # kJ/kg·K
        self.decarb_heat = 11500        # kJ/kg C，脱碳反应热
        self.si_oxidation_heat = 30800  # kJ/kg Si
        self.mn_oxidation_heat = 7200   # kJ/kg Mn

    def predict_temperature(self, data_row, initial_temp=None):
        """预测钢水温度"""
        if initial_temp is None:
            initial_temp = data_row['铁水温度'] if pd.notna(data_row['铁水温度']) else 1350

        # 计算反应热效应
        # 脱碳反应热（放热）
        carbon_removed = data_row['铁水C'] * 0.8  # 假设80%碳被氧化
        decarb_heat = carbon_removed * self.decarb_heat

        # 硅氧化热（放热）
        si_heat = data_row['铁水SI'] * self.si_oxidation_heat

        # 锰氧化热（放热）
        mn_heat = data_row['铁水MN'] * self.mn_oxidation_heat

        # 总放热量
        total_heat = decarb_heat + si_heat + mn_heat

        # 钢水质量
        steel_mass = data_row['铁水'] + (data_row['废钢'] if pd.notna(data_row['废钢']) else 0)

        # 温升计算
        temp_rise = total_heat / (steel_mass * self.heat_capacity_steel)

        # 热损失修正（经验公式）
        blow_time = data_row['吹氧时间s'] if pd.notna(data_row['吹氧时间s']) else 600
        heat_loss = 0.05 * blow_time  # 每秒损失0.05°C

        predicted_temp = initial_temp + temp_rise - heat_loss

        return max(predicted_temp, 1500)  # 最低温度限制

class PhaseCalculator:
    """相图计算模块"""

    def __init__(self):
        # CaO-SiO2-FeO三元相图参数（基于文献数据）
        self.liquidus_params = {
            'a1': 1713, 'a2': -0.5, 'a3': -1.2, 'a4': 0.8
        }

    def calculate_liquidus_temperature(self, cao_pct, sio2_pct, feo_pct):
        """计算液相线温度"""
        # 归一化
        total = cao_pct + sio2_pct + feo_pct
        if total == 0:
            return 1600

        x_cao = cao_pct / total
        x_sio2 = sio2_pct / total
        x_feo = feo_pct / total

        # 简化的液相线温度计算（基于三元相图）
        temp = (self.liquidus_params['a1'] +
                self.liquidus_params['a2'] * x_cao * 100 +
                self.liquidus_params['a3'] * x_sio2 * 100 +
                self.liquidus_params['a4'] * x_feo * 100)

        return max(temp, 1400)

    def optimize_basicity(self, sio2_pct, target_temp=1550):
        """优化碱度建议"""
        # 基于目标温度优化CaO/SiO2比值
        optimal_basicity = 2.5 + (target_temp - 1550) / 100
        optimal_cao = sio2_pct * optimal_basicity

        return {
            '建议碱度': optimal_basicity,
            '建议CaO': optimal_cao,
            '当前SiO2': sio2_pct
        }

def main():
    """主函数：执行炉渣成分预测和温度预测"""
    print("=== 炉渣在线成分预测和钢水温度预测系统 ===")
    print("基于30年炼钢经验和FactSage热力学计算\n")

    # 读取数据
    try:
        df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        print(f"成功读取数据，共{len(df)}条记录")
    except Exception as e:
        print(f"读取数据失败：{e}")
        return

    # 初始化预测器
    slag_predictor = SlagCompositionPredictor()
    temp_predictor = TemperaturePredictor()
    phase_calc = PhaseCalculator()

    # 预测结果存储
    results = []

    print("开始预测计算...")
    for idx, row in df.iterrows():
        if idx % 500 == 0:
            print(f"已处理 {idx}/{len(df)} 条记录")

        try:
            # 炉渣成分预测
            slag_comp = slag_predictor.predict_slag_composition(row)

            # 钢水温度预测
            predicted_temp = temp_predictor.predict_temperature(row)

            # 液相线温度计算
            liquidus_temp = phase_calc.calculate_liquidus_temperature(
                slag_comp['CaO'], slag_comp['SiO2'], slag_comp['FeO']
            )

            # 碱度优化建议
            basicity_opt = phase_calc.optimize_basicity(slag_comp['SiO2'])

            # 保存结果
            result = {
                '炉号': row['炉号'],
                '钢种': row['钢种'],
                '实际钢水温度': row['钢水温度'],
                '预测钢水温度': predicted_temp,
                '温度偏差': abs(predicted_temp - row['钢水温度']) if pd.notna(row['钢水温度']) else None,
                'CaO预测': slag_comp['CaO'],
                'SiO2预测': slag_comp['SiO2'],
                'FeO预测': slag_comp['FeO'],
                'MgO预测': slag_comp['MgO'],
                'MnO预测': slag_comp['MnO'],
                'P2O5预测': slag_comp['P2O5'],
                '预测碱度': slag_comp['碱度'],
                '液相线温度': liquidus_temp,
                '建议碱度': basicity_opt['建议碱度'],
                '过热度': predicted_temp - liquidus_temp
            }
            results.append(result)

        except Exception as e:
            print(f"处理第{idx}行数据时出错：{e}")
            continue

    # 保存预测结果
    results_df = pd.DataFrame(results)

    return results_df

def save_results_to_excel(results_df, original_df):
    """将预测结果保存到Excel文件"""
    try:
        # 合并原始数据和预测结果
        combined_df = pd.concat([original_df, results_df], axis=1)

        # 保存到新的Excel文件
        output_file = '炉渣成分预测结果_' + pd.Timestamp.now().strftime('%Y%m%d_%H%M') + '.xlsx'

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 主要结果
            combined_df.to_excel(writer, sheet_name='预测结果', index=False)

            # 统计分析
            stats_df = calculate_prediction_statistics(results_df)
            stats_df.to_excel(writer, sheet_name='预测精度统计', index=True)

            # 炉渣成分分布
            slag_stats = results_df[['CaO预测', 'SiO2预测', 'FeO预测', 'MgO预测', '预测碱度']].describe()
            slag_stats.to_excel(writer, sheet_name='炉渣成分统计', index=True)

        print(f"结果已保存到: {output_file}")
        return output_file

    except Exception as e:
        print(f"保存结果时出错：{e}")
        return None

def calculate_prediction_statistics(results_df):
    """计算预测精度统计"""
    stats = {}

    # 温度预测精度
    temp_valid = results_df.dropna(subset=['温度偏差'])
    if len(temp_valid) > 0:
        stats['温度预测'] = {
            '样本数量': len(temp_valid),
            '平均绝对误差': temp_valid['温度偏差'].mean(),
            '标准偏差': temp_valid['温度偏差'].std(),
            '最大误差': temp_valid['温度偏差'].max(),
            '精度±20°C': (temp_valid['温度偏差'] <= 20).sum() / len(temp_valid) * 100
        }

    # 炉渣成分统计
    stats['炉渣成分'] = {
        'CaO平均值': results_df['CaO预测'].mean(),
        'SiO2平均值': results_df['SiO2预测'].mean(),
        'FeO平均值': results_df['FeO预测'].mean(),
        '平均碱度': results_df['预测碱度'].mean(),
        '碱度标准偏差': results_df['预测碱度'].std()
    }

    # 过热度分析
    stats['过热度分析'] = {
        '平均过热度': results_df['过热度'].mean(),
        '过热度标准偏差': results_df['过热度'].std(),
        '适宜过热度比例': ((results_df['过热度'] >= 50) & (results_df['过热度'] <= 150)).sum() / len(results_df) * 100
    }

    return pd.DataFrame(stats).T

def generate_analysis_plots(results_df):
    """生成分析图表"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('炉渣成分预测和钢水温度预测分析', fontsize=16)

    # 1. 温度预测对比
    temp_valid = results_df.dropna(subset=['实际钢水温度', '预测钢水温度'])
    if len(temp_valid) > 0:
        axes[0,0].scatter(temp_valid['实际钢水温度'], temp_valid['预测钢水温度'], alpha=0.6)
        axes[0,0].plot([1500, 1700], [1500, 1700], 'r--', label='理想预测线')
        axes[0,0].set_xlabel('实际钢水温度 (°C)')
        axes[0,0].set_ylabel('预测钢水温度 (°C)')
        axes[0,0].set_title('钢水温度预测对比')
        axes[0,0].legend()
        axes[0,0].grid(True)

    # 2. 碱度分布
    axes[0,1].hist(results_df['预测碱度'], bins=30, alpha=0.7, color='skyblue')
    axes[0,1].axvline(x=2.5, color='red', linestyle='--', label='理想碱度2.5')
    axes[0,1].set_xlabel('预测碱度')
    axes[0,1].set_ylabel('频次')
    axes[0,1].set_title('炉渣碱度分布')
    axes[0,1].legend()
    axes[0,1].grid(True)

    # 3. CaO-SiO2关系
    axes[0,2].scatter(results_df['SiO2预测'], results_df['CaO预测'], alpha=0.6, c=results_df['预测碱度'], cmap='viridis')
    cbar = plt.colorbar(axes[0,2].collections[0], ax=axes[0,2])
    cbar.set_label('碱度')
    axes[0,2].set_xlabel('SiO2 (%)')
    axes[0,2].set_ylabel('CaO (%)')
    axes[0,2].set_title('CaO-SiO2关系图')
    axes[0,2].grid(True)

    # 4. 过热度分布
    axes[1,0].hist(results_df['过热度'], bins=30, alpha=0.7, color='lightcoral')
    axes[1,0].axvline(x=100, color='green', linestyle='--', label='理想过热度100°C')
    axes[1,0].set_xlabel('过热度 (°C)')
    axes[1,0].set_ylabel('频次')
    axes[1,0].set_title('过热度分布')
    axes[1,0].legend()
    axes[1,0].grid(True)

    # 5. 炉渣成分三元图（简化）
    axes[1,1].scatter(results_df['SiO2预测'], results_df['FeO预测'],
                     c=results_df['CaO预测'], cmap='plasma', alpha=0.6)
    cbar2 = plt.colorbar(axes[1,1].collections[0], ax=axes[1,1])
    cbar2.set_label('CaO (%)')
    axes[1,1].set_xlabel('SiO2 (%)')
    axes[1,1].set_ylabel('FeO (%)')
    axes[1,1].set_title('SiO2-FeO-CaO三元关系')
    axes[1,1].grid(True)

    # 6. 温度偏差分布
    temp_error = results_df.dropna(subset=['温度偏差'])
    if len(temp_error) > 0:
        axes[1,2].hist(temp_error['温度偏差'], bins=30, alpha=0.7, color='gold')
        axes[1,2].axvline(x=20, color='red', linestyle='--', label='±20°C精度线')
        axes[1,2].set_xlabel('温度预测偏差 (°C)')
        axes[1,2].set_ylabel('频次')
        axes[1,2].set_title('温度预测偏差分布')
        axes[1,2].legend()
        axes[1,2].grid(True)

    plt.tight_layout()
    plot_file = '炉渣预测分析图表_' + pd.Timestamp.now().strftime('%Y%m%d_%H%M') + '.png'
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    plt.show()
    print(f"分析图表已保存到: {plot_file}")

if __name__ == "__main__":
    results = main()
    if results is not None:
        print(f"\n预测完成！共处理{len(results)}条记录")

        # 读取原始数据用于合并
        original_df = pd.read_excel('1-4521剔除重复20250514.xlsx')

        # 保存结果
        output_file = save_results_to_excel(results, original_df)

        # 生成分析图表
        print("正在生成分析图表...")
        generate_analysis_plots(results)

        # 显示预测精度统计
        print("\n=== 预测精度统计 ===")
        stats = calculate_prediction_statistics(results)
        print(stats)

        print("\n=== 系统运行完成 ===")
        print("基于30年炼钢经验的炉渣成分预测和钢水温度预测已完成")
        print("预测结果已保存到Excel文件和分析图表中")
