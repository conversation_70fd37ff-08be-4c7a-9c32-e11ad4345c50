import pickle

# 加载特征名称
with open('results_with_slag/feature_names.pkl', 'rb') as f:
    features = pickle.load(f)

print('总特征数:', len(features))
print('\n所有特征:')
for i, feat in enumerate(features):
    print(f'{i+1:2d}. {feat}')

# 按类别分组显示
print('\n=== 特征分类 ===')

basic_features = []
slag_features = []
thermal_features = []
interaction_features = []

for feat in features:
    if 'slag_' in feat:
        slag_features.append(feat)
    elif any(thermal in feat for thermal in ['heat', 'temp', 'theoretical']):
        thermal_features.append(feat)
    elif '_x_' in feat or '_div_' in feat:
        interaction_features.append(feat)
    else:
        basic_features.append(feat)

print(f'\n基础特征 ({len(basic_features)}个):')
for feat in basic_features:
    print(f'  - {feat}')

print(f'\n炉渣特征 ({len(slag_features)}个):')
for feat in slag_features:
    print(f'  - {feat}')

print(f'\n热平衡特征 ({len(thermal_features)}个):')
for feat in thermal_features:
    print(f'  - {feat}')

print(f'\n交互特征 ({len(interaction_features)}个):')
for feat in interaction_features:
    print(f'  - {feat}')
