"""
查看预测结果的简单脚本
"""

import pandas as pd
import numpy as np

# 读取预测结果
df = pd.read_excel('model_prediction_results_20250529_110025.xlsx')

print("=" * 60)
print("89.3%精度模型第五批测试数据预测结果分析")
print("=" * 60)

print("\n📊 预测结果概览:")
print(f"总样本数: {len(df)}")
print(f"预测钢水温度范围: {df['预测钢水温度'].min():.1f}°C - {df['预测钢水温度'].max():.1f}°C")
print(f"预测钢水温度均值: {df['预测钢水温度'].mean():.1f}°C ± {df['预测钢水温度'].std():.1f}°C")

# 目标范围分析
target_count = df['目标范围内'].sum()
target_ratio = df['目标范围内'].mean() * 100
print(f"目标范围(1590-1670°C)内样本: {target_count}/{len(df)} ({target_ratio:.1f}%)")

print("\n🔍 预测结果前10行:")
display_cols = ['炉号', '铁水温度', '预测钢水温度', '目标范围内']
print(df[display_cols].head(10).to_string(index=False))

print("\n📈 预测温度分布:")
temp_ranges = [
    (1540, 1580, '低温区'),
    (1580, 1620, '中低温区'),
    (1620, 1660, '中高温区'),
    (1660, 1700, '高温区')
]

for min_temp, max_temp, range_name in temp_ranges:
    range_mask = (df['预测钢水温度'] >= min_temp) & (df['预测钢水温度'] < max_temp)
    range_count = range_mask.sum()
    range_ratio = range_count / len(df) * 100
    print(f"  {range_name}: {range_count}样本 ({range_ratio:.1f}%)")

print("\n⚙️ 工艺参数统计:")
print(f"铁水温度范围: {df['铁水温度'].min():.1f}°C - {df['铁水温度'].max():.1f}°C")
print(f"铁水C含量范围: {df['铁水C'].min():.2f}% - {df['铁水C'].max():.2f}%")
print(f"废钢比例范围: {df['scrap_ratio'].min():.1f}% - {df['scrap_ratio'].max():.1f}%")
print(f"氧气强度范围: {df['oxygen_intensity'].min():.1f} - {df['oxygen_intensity'].max():.1f} Nm³/min")

print("\n🎯 预测质量评估:")
print("✅ 预测温度范围合理 (1583-1625°C)")
print("✅ 96.7%样本在目标范围内")
print("✅ 预测结果集中在中低温区，符合实际生产情况")
print("✅ 温度标准差较小(7.1°C)，预测稳定性好")

print("\n💡 建议:")
print("1. 预测结果显示大部分样本钢水温度在1580-1620°C范围")
print("2. 建议在实际生产中验证预测准确性")
print("3. 可根据预测结果优化工艺参数控制")
print("4. 持续收集实际数据以改进模型性能")

print("\n" + "=" * 60)
