"""
Utility functions for the steel temperature prediction model.
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import pickle
import logging
from typing import Dict, List, Tuple, Union, Any
import re # Added import for regex

# Set up logging
# logging.basicConfig( # <-- REMOVE THIS BLOCK
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
logger = logging.getLogger(__name__)

def create_directory(directory: str) -> None:
    """Create directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"Created directory: {directory}")

def save_model(model: Any, filename: str) -> None:
    """Save model to file."""
    with open(filename, 'wb') as f:
        pickle.dump(model, f)
    logger.info(f"Model saved to {filename}")

def load_model(filename: str) -> Any:
    """Load model from file."""
    with open(filename, 'rb') as f:
        model = pickle.load(f)
    logger.info(f"Model loaded from {filename}")
    return model

def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """Calculate regression metrics."""
    mae = mean_absolute_error(y_true, y_pred)
    rmse = np.sqrt(mean_squared_error(y_true, y_pred))
    r2 = r2_score(y_true, y_pred)
    
    # Calculate hit rate within ±20°C
    hit_rate_20 = np.mean(np.abs(y_true - y_pred) <= 20) * 100
    
    return {
        "mae": mae,
        "rmse": rmse,
        "r2": r2,
        "hit_rate_20": hit_rate_20
    }

def plot_actual_vs_predicted(y_true: np.ndarray, y_pred: np.ndarray, 
                            title: str = "Actual vs Predicted", 
                            filename: str = None) -> None:
    """Plot actual vs predicted values."""
    plt.figure(figsize=(10, 6))
    plt.scatter(y_true, y_pred, alpha=0.5)
    
    # Add perfect prediction line
    min_val = min(np.min(y_true), np.min(y_pred))
    max_val = max(np.max(y_true), np.max(y_pred))
    plt.plot([min_val, max_val], [min_val, max_val], 'r--')
    
    # Add ±20°C lines
    plt.plot([min_val, max_val], [min_val + 20, max_val + 20], 'g--', alpha=0.5)
    plt.plot([min_val, max_val], [min_val - 20, max_val - 20], 'g--', alpha=0.5)
    
    plt.xlabel("Actual Temperature (°C)")
    plt.ylabel("Predicted Temperature (°C)")
    plt.title(title)
    
    # Add metrics to plot
    metrics = calculate_metrics(y_true, y_pred)
    plt.text(
        0.05, 0.95, 
        f"MAE: {metrics['mae']:.2f}°C\nRMSE: {metrics['rmse']:.2f}°C\n"
        f"R²: {metrics['r2']:.3f}\nHit Rate (±20°C): {metrics['hit_rate_20']:.2f}%",
        transform=plt.gca().transAxes,
        bbox=dict(facecolor='white', alpha=0.8)
    )
    
    plt.grid(True, alpha=0.3)
    
    if filename:
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {filename}")
    
    plt.show()

def plot_error_distribution(y_true: np.ndarray, y_pred: np.ndarray, 
                           title: str = "Error Distribution", 
                           filename: str = None) -> None:
    """Plot error distribution."""
    errors = y_true - y_pred
    
    plt.figure(figsize=(10, 6))
    sns.histplot(errors, kde=True)
    
    plt.axvline(x=0, color='r', linestyle='--')
    plt.axvline(x=20, color='g', linestyle='--')
    plt.axvline(x=-20, color='g', linestyle='--')
    
    plt.xlabel("Prediction Error (°C)")
    plt.ylabel("Frequency")
    plt.title(title)
    
    # Add metrics to plot
    within_20 = np.sum(np.abs(errors) <= 20)
    total = len(errors)
    hit_rate = (within_20 / total) * 100
    
    plt.text(
        0.05, 0.95, 
        f"Mean Error: {np.mean(errors):.2f}°C\nStd Dev: {np.std(errors):.2f}°C\n"
        f"Within ±20°C: {within_20}/{total} ({hit_rate:.2f}%)",
        transform=plt.gca().transAxes,
        bbox=dict(facecolor='white', alpha=0.8)
    )
    
    if filename:
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {filename}")
    
    plt.show()

def plot_feature_importance(feature_names: List[str], importances: np.ndarray, 
                           title: str = "Feature Importance", 
                           filename: str = None,
                           top_n: int = 20) -> None:
    """Plot feature importance."""
    # Sort features by importance
    indices = np.argsort(importances)[::-1]
    
    # Take top N features
    if top_n and len(feature_names) > top_n:
        indices = indices[:top_n]
    
    plt.figure(figsize=(12, 8))
    plt.barh(range(len(indices)), importances[indices], align='center')
    plt.yticks(range(len(indices)), [feature_names[i] for i in indices])
    plt.xlabel('Importance')
    plt.title(title)
    
    if filename:
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        logger.info(f"Plot saved to {filename}")
    
    plt.show()

def calculate_uncertainty(models: List[Any], X: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """Calculate prediction uncertainty using ensemble variance."""
    predictions = np.array([model.predict(X) for model in models])
    mean_pred = np.mean(predictions, axis=0)
    std_pred = np.std(predictions, axis=0)
    
    return mean_pred, std_pred

def clean_feature_names(df: pd.DataFrame) -> pd.DataFrame:
    """Clean feature names for LightGBM compatibility.
    Replaces special JSON characters and other problematic characters with underscores.
    Ensures names start with a letter or underscore if they start with a number after cleaning.
    """
    logger.info("Cleaning feature names for LightGBM compatibility...")
    new_cols = []
    for col in df.columns:
        # Replace common problematic characters (especially for JSON and other libraries)
        # Keep alphanumeric and underscore, replace others with underscore
        new_col = re.sub(r'[^A-Za-z0-9_]', '_', str(col))
        
        # LightGBM (and some other tools) might not like names starting with a number
        if new_col and new_col[0].isdigit():
            new_col = "_" + new_col
            
        # Remove multiple underscores that might have resulted from replacements
        new_col = re.sub(r'_+', '_', new_col)
        
        # Remove leading/trailing underscores that are not necessary
        new_col = new_col.strip('_')
        
        # Ensure the column name is not empty after cleaning, if so, use a placeholder
        if not new_col:
            new_col = f"feature_{len(new_cols)}" # Fallback for empty names
            
        new_cols.append(new_col)
    
    # Ensure uniqueness if cleaning created duplicates
    if len(set(new_cols)) < len(new_cols):
        logger.warning("Duplicate feature names generated after cleaning. Appending suffixes to ensure uniqueness.")
        # Simple way to make them unique: append count for duplicates
        counts = {}
        final_cols = []
        for item in new_cols:
            if item in counts:
                counts[item] += 1
                final_cols.append(f"{item}_{counts[item]}")
            else:
                counts[item] = 0
                final_cols.append(item)
        new_cols = final_cols

    df.columns = new_cols
    logger.info("Feature names cleaned.")
    return df

def format_time(seconds: float) -> str:
    """
    将秒数格式化为可读时间。
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.2f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.2f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.2f}小时"
