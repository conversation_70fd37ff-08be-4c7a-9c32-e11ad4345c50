"""
预测第五批测试数据的钢水温度（冶金规律版本）
- 保留SVR模型，移除Ridge和Lasso模型
- 添加最后2分钟添加材料的校正因子：每100kg降低5°C
- 基于冶金规律设计更合理的预测方法
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any
from sklearn.preprocessing import StandardScaler

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch5_prediction_metallurgical.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_directory(directory_path: str) -> None:
    """创建目录"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        logger.info(f"创建目录: {directory_path}")

def load_model(model_path: str) -> Any:
    """加载模型"""
    with open(model_path, 'rb') as f:
        model = pickle.load(f)
    return model

def load_batch5_data(file_path: str) -> pd.DataFrame:
    """
    加载第五批测试数据并统一列名

    Args:
        file_path: 数据文件路径

    Returns:
        加载的数据DataFrame
    """
    logger.info(f"加载第五批测试数据: {file_path}")
    try:
        data = pd.read_excel(file_path)
        logger.info(f"数据加载成功，原始形状: {data.shape}")

        # 统一列名，使其与训练数据一致
        column_mapping = {
            '炉子最大倾角': '最大角度',
            '平均流速M3/h': '气体流量流速平均',
            '最小流速': '最低流速',
            '最后2分钟加料': '最后2分钟',
            '总氧化碳(kg)': '气体总C'
        }

        # 重命名列
        for old_col, new_col in column_mapping.items():
            if old_col in data.columns:
                data.rename(columns={old_col: new_col}, inplace=True)
                logger.info(f"列名重命名: {old_col} -> {new_col}")

        logger.info(f"数据处理后形状: {data.shape}")
        return data
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        raise

def load_trained_models(models_dir: str = "results") -> Dict[str, Any]:
    """
    加载训练好的模型，排除Ridge和Lasso两个模型

    Args:
        models_dir: 模型保存目录

    Returns:
        模型字典 {模型名称: 模型对象}
    """
    logger.info(f"从 {models_dir} 加载训练好的模型")
    models = {}

    # 加载顺序思维模型
    try:
        sequential_model_path = os.path.join(models_dir, "sequential_thinking_model.pkl")
        if os.path.exists(sequential_model_path):
            models["Sequential Thinking Model"] = load_model(sequential_model_path)
            logger.info("顺序思维模型加载成功")
    except Exception as e:
        logger.error(f"加载顺序思维模型失败: {e}")

    # 加载基础模型，排除Ridge和Lasso两个模型
    # 根据用户指示，我们需要移除Ridge和Lasso模型，保留SVR模型
    base_model_names = ["xgboost", "lightgbm", "random_forest", "svr"]  # 移除了"ridge"和"lasso"
    for name in base_model_names:
        try:
            model_path = os.path.join(models_dir, f"{name}_model.pkl")
            if os.path.exists(model_path):
                models[name] = load_model(model_path)
                logger.info(f"{name} 模型加载成功")
        except Exception as e:
            logger.error(f"加载 {name} 模型失败: {e}")

    logger.info(f"共加载 {len(models)} 个模型")
    return models

def apply_material_correction(df: pd.DataFrame, predictions: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """
    应用最后2分钟添加材料的校正因子：每100kg降低5°C

    Args:
        df: 原始数据DataFrame
        predictions: 模型预测结果字典

    Returns:
        校正后的预测结果字典
    """
    logger.info("应用最后2分钟添加材料的校正因子")

    corrected_predictions = {}

    # 检查是否有"最后2分钟"列
    if '最后2分钟' in df.columns:
        # 计算校正值：每100kg降低5°C
        correction = df['最后2分钟'] * (-5/100)
        logger.info(f"校正范围: {correction.min():.2f}°C 到 {correction.max():.2f}°C")

        # 应用校正到每个模型的预测结果
        for name, pred in predictions.items():
            corrected_predictions[name] = pred + correction.values
            logger.info(f"应用校正到 {name} 模型，校正前范围: {np.min(pred):.2f}°C - {np.max(pred):.2f}°C, "
                       f"校正后范围: {np.min(corrected_predictions[name]):.2f}°C - {np.max(corrected_predictions[name]):.2f}°C")
    else:
        logger.warning("数据中没有'最后2分钟'列，无法应用校正因子")
        corrected_predictions = predictions

    return corrected_predictions

def predict_based_on_metallurgy(df: pd.DataFrame) -> np.ndarray:
    """
    基于冶金规律进行预测

    Args:
        df: 数据DataFrame

    Returns:
        预测结果数组
    """
    logger.info("基于冶金规律进行预测")

    # 创建预测结果数组
    predictions = np.zeros(len(df))

    # 对每个样本进行预测
    for i, (_, row) in enumerate(df.iterrows()):
        # 基础温度：铁水温度
        base_temp = row['铁水温度']

        # 1. 考虑铁水温度的影响
        # 铁水温度越高，出钢温度越高
        predictions[i] = base_temp

        # 2. 考虑吹氧时间的影响
        # 吹氧时间越长，温度升高越多（但有上限）
        if '吹氧时间s' in row:
            blow_time_min = row['吹氧时间s'] / 60  # 转换为分钟
            temp_increase_blow = min(80, blow_time_min * 2)  # 每分钟升温2°C，最多升温80°C
            predictions[i] += temp_increase_blow

        # 3. 考虑铁水C和Si含量的影响
        # C和Si含量越高，氧化放热越多，温度升高越多
        if 'CO中C(kg)' in row and row['CO中C(kg)'] > 0:
            c_oxidation = row['CO中C(kg)'] * 0.1  # 每kg碳氧化升温0.1°C
            predictions[i] += c_oxidation

        if '铁水SI' in row:
            si_content = row['铁水SI']
            si_oxidation = si_content * 25  # 每1%Si氧化升温约25°C
            predictions[i] += si_oxidation

        # 4. 考虑废钢比例的影响
        # 废钢比例越高，温度越低
        if '铁水' in row and '废钢' in row and row['铁水'] > 0:
            scrap_ratio = row['废钢'] / row['铁水']
            temp_decrease_scrap = scrap_ratio * 50  # 废钢比例每增加1，温度降低50°C
            predictions[i] -= temp_decrease_scrap

        # 5. 考虑间隔时间的影响
        # 间隔时间越长，散热越多，温度越低
        if '间隔时间min' in row:
            interval_time = row['间隔时间min']
            temp_decrease_interval = interval_time * 0.5  # 每分钟散热0.5°C
            predictions[i] -= temp_decrease_interval

        # 6. 考虑钢种的影响
        # 不同钢种有不同的目标温度
        if '钢种' in row:
            steel_type = str(row['钢种'])  # 确保钢种是字符串类型
            # 高碳钢需要更高温度
            if any(high_carbon in steel_type for high_carbon in ['65Mn', 'C72DA']):
                predictions[i] += 15
            # 低碳钢需要较低温度
            elif any(low_carbon in steel_type for low_carbon in ['Q235', 'A', 'B']):
                predictions[i] -= 10

        # 7. 温度上限和下限
        # 确保预测温度在合理范围内
        predictions[i] = max(1500, min(1650, predictions[i]))

    logger.info(f"冶金规律预测完成，预测范围: {np.min(predictions):.2f}°C - {np.max(predictions):.2f}°C")
    return predictions

def predict_batch5(batch5_data: pd.DataFrame, models: Dict[str, Any]) -> pd.DataFrame:
    """
    对第五批数据进行预测

    Args:
        batch5_data: 第五批测试数据
        models: 模型字典

    Returns:
        包含预测结果的DataFrame
    """
    logger.info("开始预处理第五批数据")

    # 预处理数据
    batch5_processed = batch5_data.copy()

    # 工程化特征
    logger.info("开始特征工程")
    try:
        # 使用与训练时相同的特征工程流程
        from steel_temp_prediction.feature_engineering import create_basic_features, create_metallurgical_features

        # 创建基础特征
        batch5_processed = create_basic_features(batch5_processed)

        # 创建冶金特征
        batch5_processed = create_metallurgical_features(batch5_processed)

        logger.info("特征工程完成")
    except Exception as e:
        logger.error(f"特征工程失败: {e}")
        logger.info("使用原始数据进行预测")
        batch5_processed = batch5_data.copy()

    # 准备预测特征
    logger.info("准备预测特征")
    try:
        # 保存原始数据，用于结果展示和冶金规律预测
        original_data = batch5_data.copy()

        # 基于冶金规律进行预测
        metallurgical_predictions = predict_based_on_metallurgy(original_data)

        logger.info("预测特征准备完成")
    except Exception as e:
        logger.error(f"准备预测特征失败: {e}")
        raise

    # 创建预测结果字典
    predictions = {
        "冶金规律模型": metallurgical_predictions
    }

    # 应用最后2分钟添加材料的校正因子
    predictions = apply_material_correction(original_data, predictions)

    # 创建结果DataFrame
    logger.info("整合预测结果")
    results = pd.DataFrame()

    # 添加原始数据的关键列
    if '炉号' in original_data.columns:
        results['炉号'] = original_data['炉号']
    if '钢种' in original_data.columns:
        results['钢种'] = original_data['钢种']
    if '铁水温度' in original_data.columns:
        results['铁水温度'] = original_data['铁水温度']
    if '最后2分钟' in original_data.columns:
        results['最后2分钟添加量'] = original_data['最后2分钟']

    # 添加各模型的预测结果
    for name, pred in predictions.items():
        results[f'{name}_预测温度'] = pred

    # 使用冶金规律模型的结果作为综合预测温度
    results['综合预测温度'] = predictions["冶金规律模型"]

    logger.info("预测完成")
    return results

def save_prediction_results(results: pd.DataFrame, output_path: str = "batch5_predictions_metallurgical.xlsx") -> None:
    """
    保存预测结果

    Args:
        results: 预测结果DataFrame
        output_path: 输出文件路径
    """
    logger.info(f"保存预测结果到: {output_path}")
    try:
        results.to_excel(output_path, index=False)
        logger.info("预测结果保存成功")
    except Exception as e:
        logger.error(f"保存预测结果失败: {e}")
        raise

def analyze_prediction_results(results: pd.DataFrame) -> None:
    """
    分析预测结果

    Args:
        results: 预测结果DataFrame
    """
    logger.info("分析预测结果")

    # 计算综合预测温度的统计信息
    mean_temp = results['综合预测温度'].mean()
    std_temp = results['综合预测温度'].std()
    min_temp = results['综合预测温度'].min()
    max_temp = results['综合预测温度'].max()

    logger.info(f"综合预测温度统计:")
    logger.info(f"  平均值: {mean_temp:.2f}°C")
    logger.info(f"  标准差: {std_temp:.2f}°C")
    logger.info(f"  最小值: {min_temp:.2f}°C")
    logger.info(f"  最大值: {max_temp:.2f}°C")

    # 如果有钢种信息，按钢种分析
    if '钢种' in results.columns:
        logger.info("按钢种分析预测温度:")
        for steel_type, group in results.groupby('钢种'):
            logger.info(f"  钢种 {steel_type}:")
            logger.info(f"    样本数: {len(group)}")
            logger.info(f"    平均预测温度: {group['综合预测温度'].mean():.2f}°C")
            logger.info(f"    标准差: {group['综合预测温度'].std():.2f}°C")

    # 创建预测温度分布图
    plt.figure(figsize=(10, 6))
    plt.hist(results['综合预测温度'], bins=20, alpha=0.7)
    plt.axvline(mean_temp, color='r', linestyle='--', label=f'平均值: {mean_temp:.2f}°C')
    plt.title('第五批数据预测温度分布')
    plt.xlabel('预测温度 (°C)')
    plt.ylabel('频次')
    plt.legend()
    plt.grid(alpha=0.3)
    plt.savefig('batch5_temperature_distribution_metallurgical.png', dpi=300, bbox_inches='tight')
    logger.info("预测温度分布图已保存")

    # 如果有铁水温度，分析铁水温度与预测温度的关系
    if '铁水温度' in results.columns:
        plt.figure(figsize=(10, 6))
        plt.scatter(results['铁水温度'], results['综合预测温度'], alpha=0.5)
        plt.title('铁水温度与预测出钢温度关系')
        plt.xlabel('铁水温度 (°C)')
        plt.ylabel('预测出钢温度 (°C)')
        plt.grid(alpha=0.3)
        plt.savefig('batch5_hotmetal_vs_prediction_metallurgical.png', dpi=300, bbox_inches='tight')
        logger.info("铁水温度与预测温度关系图已保存")

def main():
    """主函数"""
    logger.info("开始处理第五批测试数据（冶金规律版本）")

    # 第五批数据文件路径
    batch5_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"

    # 检查文件是否存在
    if not os.path.exists(batch5_file):
        logger.error(f"文件不存在: {batch5_file}")
        print(f"错误: 找不到文件 {batch5_file}")
        return

    # 创建结果目录
    create_directory("results")

    # 加载第五批数据
    batch5_data = load_batch5_data(batch5_file)

    # 加载训练好的模型（排除Ridge和Lasso模型）
    models = load_trained_models()

    # 预测第五批数据
    results = predict_batch5(batch5_data, models)

    # 保存预测结果
    save_prediction_results(results)

    # 分析预测结果
    analyze_prediction_results(results)

    logger.info("第五批数据处理完成（冶金规律版本）")
    print("第五批数据预测完成，结果已保存到 batch5_predictions_metallurgical.xlsx")

if __name__ == "__main__":
    main()
