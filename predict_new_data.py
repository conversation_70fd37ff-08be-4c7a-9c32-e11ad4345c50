"""
使用训练好的钢水温度预测模型对新数据进行预测。
这个脚本加载"4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"并进行预测。
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Any, Optional
import time

# 确保模块可以被导入
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='prediction_results.log'
)
logger = logging.getLogger(__name__)
# 同时输出到控制台
console = logging.StreamHandler()
console.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console.setFormatter(formatter)
logger.addHandler(console)

# 导入所需模块
try:
    from steel_temp_prediction.feature_engineering import engineer_all_features
    from steel_temp_prediction.model_development import train_sequential_thinking_model
except ImportError:
    # 如果上面的导入失败，尝试直接导入（当脚本在包内运行时）
    from feature_engineering import engineer_all_features
    from model_development import train_sequential_thinking_model

def load_data(file_path: str) -> pd.DataFrame:
    """
    加载Excel数据文件。
    
    Args:
        file_path: Excel文件路径
    
    Returns:
        加载的DataFrame
    """
    logger.info(f"加载数据文件: {file_path}")
    try:
        df = pd.read_excel(file_path)
        logger.info(f"成功加载数据，共 {len(df)} 行, {len(df.columns)} 列")
        return df
    except Exception as e:
        logger.error(f"加载数据时出错: {str(e)}")
        raise

def clean_and_prepare_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    清洗和准备数据。
    
    Args:
        df: 原始DataFrame
    
    Returns:
        清洗后的DataFrame
    """
    logger.info("开始清洗和准备数据...")
    
    # 复制数据，避免修改原始数据
    df_clean = df.copy()
    
    # 1. 检查并删除重复项
    duplicates = df_clean.duplicated()
    if duplicates.any():
        logger.info(f"删除 {duplicates.sum()} 行重复数据")
        df_clean = df_clean.drop_duplicates()
    
    # 2. 检查列名，确保与模型训练数据一致
    required_columns = [
        '铁水', '铁水温度', '铁水SI', '铁水MN', '铁水P', '铁水C',
        '废钢', '钢水SI', '钢水MN', '钢水P',
        '石灰', '白云石', '累氧实际', '吹氧时间s', '枪位'
    ]
    
    missing_columns = [col for col in required_columns if col not in df_clean.columns]
    if missing_columns:
        logger.warning(f"缺少必要的列: {missing_columns}")
    
    # 3. 检查并处理缺失值
    missing_values = df_clean.isnull().sum()
    if missing_values.any():
        logger.info("处理缺失值:")
        for col in missing_values[missing_values > 0].index:
            missing_count = missing_values[col]
            missing_percent = missing_count / len(df_clean) * 100
            logger.info(f"  列 '{col}' 有 {missing_count} 缺失值 ({missing_percent:.2f}%)")
            
            # 对数值列使用中位数填充
            if df_clean[col].dtype in ['int64', 'float64']:
                median_value = df_clean[col].median()
                df_clean[col].fillna(median_value, inplace=True)
                logger.info(f"    已用中位数 {median_value} 填充")
            # 对分类列使用众数填充
            else:
                mode_value = df_clean[col].mode()[0]
                df_clean[col].fillna(mode_value, inplace=True)
                logger.info(f"    已用众数 '{mode_value}' 填充")
    
    # 4. 检查异常值
    for col in df_clean.select_dtypes(include=['int64', 'float64']).columns:
        mean = df_clean[col].mean()
        std = df_clean[col].std()
        lower_bound = mean - 3 * std
        upper_bound = mean + 3 * std
        
        outliers = df_clean[(df_clean[col] < lower_bound) | (df_clean[col] > upper_bound)]
        if len(outliers) > 0:
            logger.info(f"  列 '{col}' 有 {len(outliers)} 个异常值")
            # 记录但不处理异常值，交给特征工程步骤处理
    
    logger.info(f"数据清洗和准备完成，剩余 {len(df_clean)} 行")
    return df_clean

def train_or_load_model(X_train: pd.DataFrame, y_train: pd.Series, 
                       model_path: str = 'steel_temp_model.pkl',
                       force_train: bool = False) -> Any:
    """
    训练新模型或加载已有模型。
    
    Args:
        X_train: 训练特征
        y_train: 训练目标
        model_path: 模型保存/加载路径
        force_train: 强制重新训练
        
    Returns:
        训练好的或加载的模型
    """
    if os.path.exists(model_path) and not force_train:
        logger.info(f"加载已有模型: {model_path}")
        try:
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            logger.info("模型加载成功")
            return model
        except Exception as e:
            logger.warning(f"加载模型时出错: {str(e)}，将重新训练模型")
            force_train = True
    
    if force_train or not os.path.exists(model_path):
        logger.info("开始训练新模型...")
        
        # 配置超参数调优设置
        tune_base_models = {
            'xgboost': 5,     # 对XGBoost进行5次调优迭代
            'lightgbm': 5     # 对LightGBM进行5次调优迭代
        }
        
        # 训练模型
        start_time = time.time()
        model = train_sequential_thinking_model(
            X_train, y_train,
            tune_base_model_n_iters=tune_base_models,
            tune_meta_model_n_iter=10
        )
        training_time = time.time() - start_time
        logger.info(f"模型训练完成，耗时 {training_time:.2f} 秒")
        
        # 保存模型
        logger.info(f"保存模型到: {model_path}")
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        return model

def process_features_and_predict(df: pd.DataFrame, model, has_target: bool = True) -> pd.DataFrame:
    """
    对数据进行特征工程并使用模型预测。
    
    Args:
        df: 输入数据
        model: 预测模型
        has_target: 数据是否包含目标变量
        
    Returns:
        带预测结果的DataFrame
    """
    logger.info("开始特征工程...")
    
    # 进行特征工程
    df_featured = engineer_all_features(
        df,
        enable_slag=True,
        enable_process_params=True,
        enable_time_series=False,  # 假设没有时间序列
        enable_interactions=True,
        enable_physicochem_props=True,
        enable_advanced_slag=True,
        enable_lance_dynamics=True
    )
    
    logger.info(f"特征工程完成，特征数: {len(df_featured.columns)}")
    
    # 准备数据
    if has_target and '出钢温度' in df_featured.columns:
        X = df_featured.select_dtypes(include=['float64', 'int64']).drop(columns=['出钢温度'], errors='ignore')
        y_true = df_featured['出钢温度']
    else:
        X = df_featured.select_dtypes(include=['float64', 'int64'])
        y_true = None
    
    # 数据清洗 - 处理inf和极大值
    logger.info("清洗特征数据，处理inf和异常值...")
    
    # 处理磷容量系数
    if 'feature_slag_phosphate_capacity' in X.columns:
        logger.info(f"对磷容量系数取对数，原始范围: "
                  f"{X['feature_slag_phosphate_capacity'].min():.2e}-{X['feature_slag_phosphate_capacity'].max():.2e}")
        # 先确保所有值都是正数，然后取对数
        X['feature_slag_phosphate_capacity'] = np.where(
            X['feature_slag_phosphate_capacity'] > 0,
            np.log10(X['feature_slag_phosphate_capacity']),
            0
        )
    
    # 处理其他可能的极大值特征
    for col in X.columns:
        if X[col].max() > 1e20:
            logger.info(f"对特征 {col} 取对数")
            X[col] = np.where(X[col] > 0, np.log10(X[col]), 0)
    
    # 替换inf和-inf
    X = X.replace([np.inf, -np.inf], np.nan)
    
    # 处理缺失值
    X = X.fillna(X.median())
    
    # 处理极值
    for col in X.columns:
        median = X[col].median()
        std = X[col].std()
        upper_limit = median + 5 * std
        lower_limit = median - 5 * std
        X[col] = X[col].clip(lower_limit, upper_limit)
    
    # 检查是否有问题的特征
    col_stats = X.describe().transpose()[['min', 'max', 'mean', 'std']]
    problematic_cols = col_stats[(col_stats['max'] > 1e10) | (col_stats['min'] < -1e10) | 
                              col_stats['std'].isna() | np.isinf(col_stats['std'])]
    
    if not problematic_cols.empty:
        for idx, row in problematic_cols.iterrows():
            logger.warning(f"删除有问题的特征: {idx}")
            X = X.drop(columns=[idx])
    
    # 进行预测
    logger.info("开始预测...")
    y_pred = model.predict(X)
    
    # 创建结果DataFrame
    result_df = df.copy()
    result_df['预测温度'] = y_pred
    
    # 如果有真实温度，计算指标
    if has_target and y_true is not None:
        result_df['温度绝对误差'] = np.abs(y_true - y_pred)
        result_df['是否命中'] = result_df['温度绝对误差'] <= 20  # 误差在±20°C内算命中
        
        # 计算评估指标
        mae = np.mean(np.abs(y_true - y_pred))
        rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
        hit_rate_20 = np.mean(np.abs(y_true - y_pred) <= 20) * 100
        
        logger.info(f"评估结果:")
        logger.info(f"  平均绝对误差 (MAE): {mae:.2f}°C")
        logger.info(f"  均方根误差 (RMSE): {rmse:.2f}°C")
        logger.info(f"  命中率 (±20°C): {hit_rate_20:.2f}%")
    
    logger.info("预测完成")
    return result_df

def visualize_results(result_df: pd.DataFrame, output_path: str = 'prediction_results.png'):
    """
    可视化预测结果。
    
    Args:
        result_df: 带预测结果的DataFrame
        output_path: 输出图像路径
    """
    if '出钢温度' not in result_df.columns:
        logger.warning("无真实温度数据，无法创建对比可视化")
        return
    
    logger.info("可视化预测结果...")
    
    # 创建真实温度vs预测温度散点图
    plt.figure(figsize=(12, 8))
    
    # 主散点图
    plt.scatter(result_df['出钢温度'], result_df['预测温度'], alpha=0.7)
    
    # 理想预测线
    min_temp = min(result_df['出钢温度'].min(), result_df['预测温度'].min())
    max_temp = max(result_df['出钢温度'].max(), result_df['预测温度'].max())
    plt.plot([min_temp, max_temp], [min_temp, max_temp], 'r--', label='理想预测线')
    
    # ±20°C范围
    plt.fill_between([min_temp, max_temp], [min_temp+20, max_temp+20], [min_temp-20, max_temp-20], 
                    color='green', alpha=0.1, label='±20°C范围')
    
    # 添加标签和标题
    plt.xlabel('实际温度 (°C)')
    plt.ylabel('预测温度 (°C)')
    plt.title('钢水温度预测结果')
    plt.grid(alpha=0.3)
    plt.legend()
    
    # 计算并显示统计信息
    mae = np.mean(np.abs(result_df['出钢温度'] - result_df['预测温度']))
    rmse = np.sqrt(np.mean((result_df['出钢温度'] - result_df['预测温度']) ** 2))
    hit_rate = np.mean(np.abs(result_df['出钢温度'] - result_df['预测温度']) <= 20) * 100
    
    plt.figtext(0.15, 0.15, f'MAE: {mae:.2f}°C\nRMSE: {rmse:.2f}°C\n命中率(±20°C): {hit_rate:.2f}%',
               bbox=dict(facecolor='white', alpha=0.8))
    
    # 保存图像
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logger.info(f"预测结果可视化已保存到: {output_path}")
    
    # 创建误差分布直方图
    plt.figure(figsize=(10, 6))
    errors = result_df['预测温度'] - result_df['出钢温度']
    plt.hist(errors, bins=30, alpha=0.7, color='blue')
    plt.axvline(x=0, color='r', linestyle='--')
    plt.axvline(x=20, color='g', linestyle='--')
    plt.axvline(x=-20, color='g', linestyle='--')
    plt.xlabel('预测误差 (°C)')
    plt.ylabel('频率')
    plt.title('预测误差分布')
    plt.grid(alpha=0.3)
    plt.savefig('error_distribution.png', dpi=300, bbox_inches='tight')
    logger.info("误差分布图已保存到: error_distribution.png")

def save_prediction_results(result_df: pd.DataFrame, output_path: str = 'prediction_results.xlsx'):
    """
    保存预测结果到Excel文件。
    
    Args:
        result_df: 带预测结果的DataFrame
        output_path: 输出文件路径
    """
    logger.info(f"保存预测结果到: {output_path}")
    try:
        result_df.to_excel(output_path, index=False)
        logger.info("预测结果已成功保存")
    except Exception as e:
        logger.error(f"保存结果时出错: {str(e)}")

def main():
    """
    主函数，处理钢水温度预测流程。
    """
    logger.info("开始钢水温度预测")
    
    # 设置文件路径 - 修复文件路径问题
    data_file = os.path.join(parent_dir, "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx")
    # 添加文件路径诊断
    logger.info(f"数据文件路径: {data_file}")
    logger.info(f"文件是否存在: {os.path.exists(data_file)}")
    
    if not os.path.exists(data_file):
        # 尝试直接在当前目录查找
        alt_data_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"
        if os.path.exists(alt_data_file):
            data_file = alt_data_file
            logger.info(f"使用替代路径: {data_file}")
        else:
            # 列出目录中的文件，帮助诊断
            logger.info(f"当前目录: {os.getcwd()}")
            logger.info(f"上级目录: {parent_dir}")
            logger.info("当前目录文件列表:")
            for file in os.listdir(current_dir):
                logger.info(f"  - {file}")
            logger.info("上级目录文件列表:")
            for file in os.listdir(parent_dir):
                logger.info(f"  - {file}")
            
            # 尝试查找匹配的Excel文件
            excel_files = [f for f in os.listdir(parent_dir) if f.endswith('.xlsx')]
            if excel_files:
                data_file = os.path.join(parent_dir, excel_files[0])
                logger.info(f"使用找到的第一个Excel文件: {data_file}")
            else:
                logger.error("找不到测试数据文件，终止预测")
                return
    
    model_file = os.path.join(current_dir, "steel_temp_model.pkl")
    
    # 1. 加载数据
    df = load_data(data_file)
    
    # 2. 数据清洗和准备
    df_clean = clean_and_prepare_data(df)
    
    # 3. 检查是否需要训练新模型
    has_target = '出钢温度' in df_clean.columns
    
    if has_target:
        logger.info("数据中包含目标变量'出钢温度'，将用于评估模型性能")
        
        # 先进行特征工程以准备训练数据
        logger.info("准备训练数据...")
        df_featured_train = engineer_all_features(
            df_clean,
            enable_slag=True,
            enable_process_params=True,
            enable_time_series=False,
            enable_interactions=True,
            enable_physicochem_props=True,
            enable_advanced_slag=True,
            enable_lance_dynamics=True
        )
        
        # 准备训练数据
        X_train = df_featured_train.select_dtypes(include=['float64', 'int64']).drop(columns=['出钢温度'], errors='ignore')
        y_train = df_featured_train['出钢温度']
        
        # 数据清洗
        for col in X_train.columns:
            if 'phosphate_capacity' in col or X_train[col].max() > 1e20:
                X_train[col] = np.where(X_train[col] > 0, np.log10(X_train[col]), 0)
        
        X_train = X_train.replace([np.inf, -np.inf], np.nan)
        X_train = X_train.fillna(X_train.median())
        
        for col in X_train.columns:
            median = X_train[col].median()
            std = X_train[col].std()
            upper_limit = median + 5 * std
            lower_limit = median - 5 * std
            X_train[col] = X_train[col].clip(lower_limit, upper_limit)
        
        # 检查并删除问题特征
        col_stats = X_train.describe().transpose()[['min', 'max', 'mean', 'std']]
        problematic_cols = col_stats[(col_stats['max'] > 1e10) | (col_stats['min'] < -1e10) | 
                                  col_stats['std'].isna() | np.isinf(col_stats['std'])]
        
        if not problematic_cols.empty:
            for idx, row in problematic_cols.iterrows():
                logger.warning(f"删除有问题的训练特征: {idx}")
                X_train = X_train.drop(columns=[idx])
        
        # 是否强制重新训练模型
        force_train = False  # 如果需要重新训练，设为True
        
        # 训练或加载模型
        model = train_or_load_model(X_train, y_train, model_file, force_train)
    else:
        logger.info("数据中不包含目标变量'出钢温度'，只能进行预测")
        
        # 尝试加载现有模型
        if os.path.exists(model_file):
            logger.info(f"加载现有模型: {model_file}")
            with open(model_file, 'rb') as f:
                model = pickle.load(f)
        else:
            logger.error("没有找到现有模型，无法进行预测。请先提供带有'出钢温度'的数据进行训练。")
            return
    
    # 4. 特征工程和预测
    result_df = process_features_and_predict(df_clean, model, has_target)
    
    # 5. 可视化结果
    if has_target:
        visualize_results(result_df)
    
    # 6. 保存结果
    save_prediction_results(result_df)
    
    logger.info("钢水温度预测完成")

if __name__ == "__main__":
    main() 