"""
Model evaluation module for the steel temperature prediction model.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import cross_val_predict, KFold
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import logging
from typing import Dict, List, Tuple, Union, Any, Optional
import os

from steel_temp_prediction.config import EVALUATION_METRICS, CV_FOLDS
from steel_temp_prediction.utils import calculate_metrics, plot_actual_vs_predicted, plot_error_distribution, plot_feature_importance

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def evaluate_model(model: Any, X_test: pd.DataFrame, y_test: Optional[pd.Series],
                 feature_names: Optional[List[str]] = None,
                 model_name: str = "Model") -> Dict[str, float]:
    """
    Evaluate a model on test data.

    Args:
        model: Trained model
        X_test: Test features
        y_test: Test target (can be None if in prediction mode)
        feature_names: Feature names for feature importance plot
        model_name: Name of the model for plots

    Returns:
        Dictionary of evaluation metrics
    """
    logger.info(f"Evaluating {model_name}...")

    # Make predictions
    y_pred = model.predict(X_test)

    metrics: Dict[str, float] = {
            "mae": float('nan'),
            "rmse": float('nan'),
            "r2": float('nan'),
            "hit_rate_20": float('nan')
        }

    # Check if y_test is available for metric calculation
    if y_test is None or y_test.isnull().all(): #.all() is safer if it's an empty series with NaNs
        if y_test is None:
            logger.info(f"Test target (y_test) is None. Assuming prediction mode. Skipping evaluation metrics calculation.")
        else: # y_test.isnull().all()
            logger.info(f"Test target contains only NaN values. Skipping evaluation metrics calculation.")

        # Save predictions to file
        # Ensure RESULTS_DIR is accessible or defined here, or pass it
        # For now, using relative path as in original code
        results_dir_path = "results" # Assuming RESULTS_DIR from config might not be imported/available directly
        
        # Create DataFrame for predictions
        predictions_df = pd.DataFrame({'Predicted_Steel_Temperature': y_pred}, index=X_test.index)
        
        # Include original '炉号' if it's in X_test's columns (it might have been dropped if not numeric)
        # Or if it's part of a MultiIndex.
        # This part needs to be robust to how X_test is structured.
        # A simple approach is to just save the index as is.
        # If '炉号' was part of the original test_df and made it to X_test.index, it's saved.
        
        # Construct filename
        # Ensure RESULTS_DIR exists (main.py should create it)
        pred_filename = os.path.join(results_dir_path, f"{model_name.lower().replace(' ', '_')}_predictions.csv")
        try:
            # Ensure directory exists (it should be created by main.py's create_directory(RESULTS_DIR))
            os.makedirs(results_dir_path, exist_ok=True) 
            predictions_df.to_csv(pred_filename, index=True) # Save with index
            logger.info(f"Saved predictions to {pred_filename}")
        except Exception as e:
            logger.error(f"Error saving predictions to {pred_filename}: {e}")

    else: # y_test is available and has non-NaN values
        # Calculate metrics
        metrics = calculate_metrics(y_test, y_pred)

        # Log metrics
        logger.info(f"{model_name} metrics:")
        logger.info(f"  MAE: {metrics['mae']:.2f}°C")
        logger.info(f"  RMSE: {metrics['rmse']:.2f}°C")
        logger.info(f"  R²: {metrics['r2']:.3f}")
        logger.info(f"  Hit Rate (±20°C): {metrics['hit_rate_20']:.2f}%")

        # Plot actual vs predicted
        plot_actual_vs_predicted(
            y_test, y_pred,
            title=f"{model_name}: Actual vs Predicted",
            filename=os.path.join("results", f"{model_name.lower().replace(' ', '_')}_actual_vs_pred.png")
        )

        # Plot error distribution
        plot_error_distribution(
            y_test, y_pred,
            title=f"{model_name}: Error Distribution",
            filename=os.path.join("results", f"{model_name.lower().replace(' ', '_')}_error_dist.png")
        )

        # Save predictions with actual values
        results_dir_path = "results"
        os.makedirs(results_dir_path, exist_ok=True)
        
        # Create DataFrame for predictions
        actual_pred_df = pd.DataFrame({'Actual_Steel_Temperature': y_test, 'Predicted_Steel_Temperature': y_pred}, index=X_test.index)
        pred_filename = os.path.join(results_dir_path, f"{model_name.lower().replace(' ', '_')}_actual_vs_predictions.csv")
        try:
            actual_pred_df.to_csv(pred_filename, index=True)
            logger.info(f"Saved actual and predicted values to {pred_filename}")
        except Exception as e:
            logger.error(f"Error saving actual and predicted values to {pred_filename}: {e}")

    # Plot feature importance if available (common to both cases)
    if hasattr(model, 'feature_importances_') and feature_names is not None and len(feature_names) == len(model.feature_importances_):
        plot_feature_importance(
            feature_names, model.feature_importances_,
            title=f"{model_name}: Feature Importance",
            filename=os.path.join("results", f"{model_name.lower().replace(' ', '_')}_feature_importance.png")
        )
    elif hasattr(model, 'coef_') and feature_names is not None and len(feature_names) == len(model.coef_): # For linear models
        plot_feature_importance(
            feature_names, model.coef_,
            title=f"{model_name}: Feature Coefficients",
            filename=os.path.join("results", f"{model_name.lower().replace(' ', '_')}_feature_coefficients.png")
        )

    return metrics

def cross_validate_model(model: Any, X: pd.DataFrame, y: pd.Series,
                       n_splits: int = CV_FOLDS) -> Dict[str, float]:
    """
    Perform cross-validation on a model.

    Args:
        model: Model to cross-validate
        X: Features
        y: Target
        n_splits: Number of cross-validation splits

    Returns:
        Dictionary of cross-validation metrics
    """
    logger.info(f"Performing {n_splits}-fold cross-validation...")

    # Create cross-validation folds
    cv = KFold(n_splits=n_splits, shuffle=True, random_state=42)

    # Make cross-validation predictions
    y_pred_cv = cross_val_predict(model, X, y, cv=cv)

    # Calculate metrics
    metrics = calculate_metrics(y, y_pred_cv)

    # Log metrics
    logger.info(f"Cross-validation metrics:")
    logger.info(f"  MAE: {metrics['mae']:.2f}°C")
    logger.info(f"  RMSE: {metrics['rmse']:.2f}°C")
    logger.info(f"  R²: {metrics['r2']:.3f}")
    logger.info(f"  Hit Rate (±20°C): {metrics['hit_rate_20']:.2f}%")

    # Plot cross-validation results
    plot_actual_vs_predicted(
        y, y_pred_cv,
        title="Cross-Validation: Actual vs Predicted",
        filename="results/cross_validation_actual_vs_pred.png"
    )

    plot_error_distribution(
        y, y_pred_cv,
        title="Cross-Validation: Error Distribution",
        filename="results/cross_validation_error_dist.png"
    )

    return metrics

def analyze_errors(y_true: Optional[pd.Series], y_pred: np.ndarray,
                 X: pd.DataFrame, threshold: float = 20.0) -> None:
    """
    Analyze prediction errors to identify patterns.

    Args:
        y_true: True target values (can be None if in prediction mode)
        y_pred: Predicted target values
        X: Features
        threshold: Error threshold in °C
    """
    logger.info(f"Analyzing prediction errors (threshold: ±{threshold}°C)...")

    # Check if y_true is available for error analysis
    if y_true is None or y_true.isnull().all():
        if y_true is None:
            logger.info(f"True target values (y_true) are None. Assuming prediction mode. Skipping error analysis plots and metrics.")
        else:
            logger.info(f"True target values (y_true) contain only NaN values. Skipping error analysis plots and metrics.")

        # Save predictions with features for later analysis if needed
        # This file is generic, consider if it should be model-specific if analyze_errors is called per model
        # Currently, main.py calls it once for the best_model
        results_dir_path = "results"
        os.makedirs(results_dir_path, exist_ok=True)

        # Create DataFrame for predictions with features
        # Ensure X has the same index as y_pred if y_pred came from X
        # y_pred is a numpy array, X is a DataFrame. Best to use X.index
        pred_features_df = X.copy() # Start with features
        pred_features_df['Predicted_Steel_Temperature'] = y_pred # Add predictions
        
        pred_filename = os.path.join(results_dir_path, "predictions_with_features.csv")
        try:
            pred_features_df.to_csv(pred_filename, index=True) # Save with index
            logger.info(f"Saved predictions with features to {pred_filename}")
        except Exception as e:
            logger.error(f"Error saving predictions with features to {pred_filename}: {e}")
        return

    # Calculate errors
    errors = y_true - y_pred
    abs_errors = np.abs(errors)

    # Create DataFrame with errors
    error_df = pd.DataFrame({
        'true': y_true,
        'pred': y_pred,
        'error': errors,
        'abs_error': abs_errors
    })

    # Add features to error DataFrame
    for col in X.columns:
        error_df[col] = X[col].values

    # Classify errors
    error_df['error_category'] = pd.cut(
        error_df['error'],
        bins=[-float('inf'), -2*threshold, -threshold, threshold, 2*threshold, float('inf')],
        labels=['Large Negative', 'Negative', 'Within Threshold', 'Positive', 'Large Positive']
    )

    # Count errors by category
    error_counts = error_df['error_category'].value_counts()
    logger.info(f"Error categories:\n{error_counts}")

    # Calculate hit rate
    hit_rate = (error_df['error_category'] == 'Within Threshold').mean() * 100
    logger.info(f"Hit rate (±{threshold}°C): {hit_rate:.2f}%")

    # Plot error distribution by category
    plt.figure(figsize=(10, 6))
    sns.countplot(x='error_category', data=error_df, palette='viridis')
    plt.title(f"Error Categories (Threshold: ±{threshold}°C)")
    plt.xlabel("Error Category")
    plt.ylabel("Count")
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join("results", "error_categories.png"), dpi=300, bbox_inches='tight')
    plt.close()

    # Analyze correlation between features and errors
    error_corr = error_df.drop(['true', 'pred', 'error_category'], axis=1).corrwith(error_df['error'])
    error_corr = error_corr.sort_values(ascending=False)

    logger.info(f"Top correlations with error:")
    for feature, corr in error_corr.head(10).items():
        logger.info(f"  {feature}: {corr:.3f}")

    # Plot top correlations
    plt.figure(figsize=(12, 6))
    error_corr.head(15).plot(kind='bar')
    plt.title("Feature Correlation with Prediction Error")
    plt.xlabel("Feature")
    plt.ylabel("Correlation")
    plt.tight_layout()
    plt.savefig(os.path.join("results", "error_correlation.png"), dpi=300, bbox_inches='tight')
    plt.close()

    # Analyze large errors
    large_errors = error_df[abs_errors > threshold].sort_values('abs_error', ascending=False)
    logger.info(f"Number of large errors (>{threshold}°C): {len(large_errors)}")

    if len(large_errors) > 0:
        # Analyze patterns in large errors
        logger.info(f"Average values for instances with large errors:")
        large_error_means = large_errors.mean()
        all_means = error_df.mean()

        for feature in X.columns:
            if feature in large_error_means and feature in all_means:
                diff_pct = (large_error_means[feature] - all_means[feature]) / all_means[feature] * 100
                if abs(diff_pct) > 10:  # Only show features with >10% difference
                    logger.info(f"  {feature}: {large_error_means[feature]:.3f} "
                              f"(All: {all_means[feature]:.3f}, Diff: {diff_pct:.1f}%)")

        # Create summary of large errors for specific features (example)
        if not large_errors.empty:
            logger.info("Summary of large errors for key features (mean values):")
            key_features_for_error_analysis = [col for col in ['铁水温度', '铁水SI', '铁水C', '废钢', '累氧实际', '吹氧时间s'] if col in large_errors.columns]
            if key_features_for_error_analysis:
                logger.info(large_errors[key_features_for_error_analysis].mean().to_string())
            else:
                logger.info("No pre-defined key features found in the large error dataframe for summary.")
        
        # Save detailed large errors to CSV
        large_errors_filename = os.path.join("results", "large_errors_analysis.csv")
        try:
            large_errors.to_csv(large_errors_filename, index=True)
            logger.info(f"Detailed large error analysis saved to {large_errors_filename}")
        except Exception as e:
            logger.error(f"Error saving large error analysis to {large_errors_filename}: {e}")

def compare_models(models: Dict[str, Any], X_test: pd.DataFrame, y_test: Optional[pd.Series]) -> Optional[str]:
    """
    Compare models based on R² score and return the best model name.
    If y_test is None, returns a default model or None.

    Args:
        models: Dictionary of trained models {name: model_object}
        X_test: Test features
        y_test: Test target (can be None if in prediction mode)

    Returns:
        Name of the best model, or None if no models or y_test is unavailable.
    """
    if not models:
        logger.warning("No models provided for comparison.")
        return None

    if y_test is None or y_test.isnull().all():
        logger.warning("y_test is not available for model comparison based on R2 score.")
        # Defaulting to a preferred model if available, or the first one
        if "Sequential Thinking Model" in models:
            logger.info("Defaulting to 'Sequential Thinking Model' as best model due to lack of y_test for comparison.")
            return "Sequential Thinking Model"
        
        default_model_name = list(models.keys())[0]
        logger.info(f"Defaulting to the first model '{default_model_name}' as best model due to lack of y_test for comparison.")
        return default_model_name

    logger.info("Comparing models based on R² score...")
    best_r2 = -float('inf')
    best_model_name: Optional[str] = None
    model_r2_scores: Dict[str, float] = {}

    for name, model in models.items():
        try:
            y_pred = model.predict(X_test)
            current_r2 = r2_score(y_test, y_pred)
            model_r2_scores[name] = current_r2
            logger.info(f"  {name}: R² = {current_r2:.4f}")
            if current_r2 > best_r2:
                best_r2 = current_r2
                best_model_name = name
        except Exception as e:
            logger.error(f"Error evaluating model {name} for comparison: {e}")
            model_r2_scores[name] = -float('inf') # Penalize models that error during prediction/scoring

    if best_model_name:
        logger.info(f"Best model based on R² score: {best_model_name} (R² = {best_r2:.4f})")
    else:
        logger.warning("Could not determine best model from comparison.")
        # Fallback if all models errored or other issues
        if models: # Pick first one as a last resort if no R2 was better than -inf
            best_model_name = list(models.keys())[0]
            logger.info(f"Falling back to the first model available: {best_model_name}")

    return best_model_name
