"""
阶段2：扩大超参数搜索空间
目标：在阶段1基础上，大幅扩大超参数搜索空间，提升模型性能
预期提升：从74.4%提升到78-80%
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import warnings
from typing import Dict, List, Tuple, Union, Any, Optional
from datetime import datetime

# 核心机器学习库
from sklearn.model_selection import train_test_split, TimeSeriesSplit, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import xgboost as xgb
import lightgbm as lgb

# CatBoost
try:
    import catboost as cb
    CATBOOST_AVAILABLE = True
    print("✅ CatBoost successfully loaded")
except ImportError:
    CATBOOST_AVAILABLE = False
    print("❌ CatBoost not available")

# 超参数优化
try:
    import optuna
    from optuna.samplers import TPESampler
    from optuna.pruners import MedianPruner
    OPTUNA_AVAILABLE = True
    print("✅ Optuna successfully loaded")
except ImportError:
    OPTUNA_AVAILABLE = False
    print("❌ Optuna not available")

# TensorFlow - 修复版本
TF_AVAILABLE = False
try:
    os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

    import tensorflow as tf
    tf.get_logger().setLevel('ERROR')
    tf.config.set_visible_devices([], 'GPU')

    from tensorflow import keras
    from tensorflow.keras import layers, Model
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

    TF_AVAILABLE = True
    print("✅ TensorFlow successfully loaded")

except Exception as e:
    TF_AVAILABLE = False
    print(f"❌ TensorFlow not available: {e}")

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f"step2_expanded_hyperopt_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Step2ExpandedHyperparameterOptimizer:
    """阶段2：扩大超参数搜索空间优化器"""

    def __init__(self):
        self.models = {}
        self.best_params = {}
        self.scalers = {}
        self.label_encoders = {}
        self.study_results = {}

        # 目标范围参数
        self.target_range = (1590, 1670)
        self.target_tolerance = 20

        # 多目标优化权重
        self.multi_objective_weights = {
            'accuracy': 0.6,      # 目标精度权重（提高）
            'stability': 0.2,     # 预测稳定性权重
            'generalization': 0.15, # 泛化能力权重
            'physics': 0.05       # 物理一致性权重
        }

    def multi_objective_loss(self, y_true: np.ndarray, y_pred: np.ndarray,
                           y_val_true: np.ndarray = None, y_val_pred: np.ndarray = None) -> float:
        """多目标优化损失函数"""

        # 1. 精度损失（目标范围内的命中率）
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])
        if target_mask.sum() > 0:
            target_y_true = y_true[target_mask]
            target_y_pred = y_pred[target_mask]
            hit_rate = np.mean(np.abs(target_y_true - target_y_pred) <= self.target_tolerance)
            accuracy_loss = (1 - hit_rate) * 1000
        else:
            accuracy_loss = 1000

        # 2. 稳定性损失（预测方差）
        stability_loss = np.var(y_pred) / 100

        # 3. 泛化能力损失（训练集vs验证集性能差异）
        if y_val_true is not None and y_val_pred is not None:
            train_mae = np.mean(np.abs(y_true - y_pred))
            val_mae = np.mean(np.abs(y_val_true - y_val_pred))
            generalization_loss = abs(val_mae - train_mae) * 10
        else:
            generalization_loss = 0

        # 4. 物理一致性损失（预测值在合理范围内）
        physics_loss = np.mean((y_pred < 1500) | (y_pred > 1750)) * 500

        # 组合损失
        total_loss = (self.multi_objective_weights['accuracy'] * accuracy_loss +
                     self.multi_objective_weights['stability'] * stability_loss +
                     self.multi_objective_weights['generalization'] * generalization_loss +
                     self.multi_objective_weights['physics'] * physics_loss)

        return total_loss

    def calculate_target_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray, tolerance: int = 20) -> float:
        """计算目标范围内的命中率"""
        target_mask = (y_true >= self.target_range[0]) & (y_true <= self.target_range[1])

        if target_mask.sum() == 0:
            return 0.0

        target_y_true = y_true[target_mask]
        target_y_pred = y_pred[target_mask]

        accuracy = np.mean(np.abs(target_y_true - target_y_pred) <= tolerance) * 100
        return accuracy

    def robust_data_cleaning(self, df: pd.DataFrame) -> pd.DataFrame:
        """稳健的数据清理"""
        logger.info("开始数据清理")

        df_clean = df.copy()

        # 安全数值转换
        def safe_convert(value, default=0.0):
            if pd.isna(value):
                return default
            try:
                if isinstance(value, str):
                    value = value.replace('t', '').replace('kg', '').replace('°C', '').replace('℃', '')
                    value = value.replace('Nm³', '').replace('m³', '').replace('%', '').strip()
                return float(value)
            except:
                return default

        # 处理数值列
        numeric_columns = ['铁水温度', '铁水C', '铁水SI', '铁水MN', '铁水P', '铁水S',
                          '铁水', '废钢', '石灰', '白云石', '累氧实际', '吹氧时间s']

        for col in numeric_columns:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].apply(safe_convert)

        # 移除无穷大值
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)

        # 基于物理约束的异常值检测
        constraints = {
            '铁水温度': (1250, 1500),
            '铁水C': (3.0, 5.5),
            '铁水SI': (0.1, 1.5),
            '铁水MN': (0.05, 1.0),
            '铁水P': (0.05, 0.3),
            '铁水': (60, 120),
            '废钢': (0, 50),
            '累氧实际': (3000, 7000),
            '吹氧时间s': (300, 1200)
        }

        for col, (min_val, max_val) in constraints.items():
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].clip(min_val, max_val)

        # 目标变量清理
        if '钢水温度' in df_clean.columns:
            df_clean['钢水温度'] = df_clean['钢水温度'].apply(safe_convert)
            df_clean = df_clean[(df_clean['钢水温度'] >= 1500) & (df_clean['钢水温度'] <= 1750)]

        logger.info(f"数据清理完成，保留{len(df_clean)}条记录")
        return df_clean

    def create_enhanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建增强特征"""
        logger.info("创建增强特征")

        df_features = df.copy()

        # 1. 基础工程特征
        df_features['scrap_ratio'] = df_features['废钢'] / (df_features['铁水'] + df_features['废钢'] + 1e-6)
        df_features['oxygen_intensity'] = df_features['累氧实际'] / (df_features['吹氧时间s'] / 60 + 1e-6)
        df_features['oxygen_per_hotmetal'] = df_features['累氧实际'] / (df_features['铁水'] + 1e-6)
        df_features['lime_per_hotmetal'] = df_features['石灰'] / (df_features['铁水'] + 1e-6)

        # 2. 成分交互特征
        df_features['C_SI_interaction'] = df_features['铁水C'] * df_features['铁水SI']
        df_features['C_MN_interaction'] = df_features['铁水C'] * df_features['铁水MN']
        df_features['SI_MN_interaction'] = df_features['铁水SI'] * df_features['铁水MN']
        df_features['C_P_interaction'] = df_features['铁水C'] * df_features['铁水P']

        # 3. 温度相关特征
        df_features['temp_carbon_ratio'] = df_features['铁水温度'] / (df_features['铁水C'] + 1e-6)
        df_features['temp_oxygen_ratio'] = df_features['铁水温度'] / (df_features['oxygen_intensity'] + 1e-6)
        df_features['temp_scrap_interaction'] = df_features['铁水温度'] * df_features['scrap_ratio']

        # 4. 高阶特征（新增）
        df_features['carbon_squared'] = df_features['铁水C'] ** 2
        df_features['silicon_squared'] = df_features['铁水SI'] ** 2
        df_features['oxygen_squared'] = df_features['oxygen_intensity'] ** 2

        # 5. 比率特征（新增）
        df_features['C_to_SI_ratio'] = df_features['铁水C'] / (df_features['铁水SI'] + 1e-6)
        df_features['MN_to_P_ratio'] = df_features['铁水MN'] / (df_features['铁水P'] + 1e-6)
        df_features['lime_to_scrap_ratio'] = df_features['石灰'] / (df_features['废钢'] + 1e-6)

        # 6. 物理约束特征
        for idx, row in df_features.iterrows():
            try:
                hot_metal_temp = row['铁水温度']
                c_content = row['铁水C'] / 100
                si_content = row['铁水SI'] / 100
                scrap_ratio = row['scrap_ratio']

                # 理论温升
                oxidation_heat = c_content * 15 + si_content * 25
                scrap_cooling = scrap_ratio * 50
                theoretical_temp_rise = oxidation_heat - scrap_cooling
                theoretical_temp_rise = np.clip(theoretical_temp_rise, 50, 400)

                df_features.loc[idx, 'theoretical_temp_rise'] = theoretical_temp_rise
                df_features.loc[idx, 'theoretical_end_temp'] = hot_metal_temp + theoretical_temp_rise

            except Exception as e:
                logger.warning(f"计算第{idx}行物理特征时出错: {e}")
                df_features.loc[idx, 'theoretical_temp_rise'] = 100
                df_features.loc[idx, 'theoretical_end_temp'] = row['铁水温度'] + 100

        # 7. 钢种分类特征
        if '钢种' in df_features.columns:
            def classify_steel_grade(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70', 'C72DA', 'SWRCH']):
                    return '高碳钢'
                elif any(x in steel_type for x in ['ER50', 'ML40CR', '40CR', '20CRMO']):
                    return '合金钢'
                elif any(x in steel_type for x in ['304', '316', '321', '430']):
                    return '不锈钢'
                else:
                    return '普通钢'

            df_features['steel_category'] = df_features['钢种'].apply(classify_steel_grade)

            def estimate_carbon_content(steel_type):
                steel_type = str(steel_type).upper()
                if any(x in steel_type for x in ['65MN', '70']):
                    return 'high_carbon'
                elif any(x in steel_type for x in ['20', '16MN']):
                    return 'low_carbon'
                else:
                    return 'medium_carbon'

            df_features['carbon_grade'] = df_features['钢种'].apply(estimate_carbon_content)

        # 填充缺失值
        numeric_cols = df_features.select_dtypes(include=[np.number]).columns
        df_features[numeric_cols] = df_features[numeric_cols].fillna(df_features[numeric_cols].median())

        logger.info("增强特征创建完成")
        return df_features

    def prepare_data_for_models(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.Series, List[str]]:
        """为模型准备数据"""
        logger.info("准备模型数据")

        exclude_cols = ['炉号', '出钢重量估算', 'Unnamed: 4', '钢水温度']

        feature_cols = [col for col in df.columns if col not in exclude_cols]
        X = df[feature_cols].copy()

        if '钢水温度' in df.columns:
            y = df['钢水温度'].copy()
        else:
            y = pd.Series([0] * len(df))

        # 识别分类特征
        categorical_features = []
        for col in X.columns:
            if X[col].dtype == 'object' or col in ['steel_category', 'carbon_grade']:
                categorical_features.append(col)

        # 处理分类特征
        for col in categorical_features:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                X[col] = self.label_encoders[col].fit_transform(X[col].astype(str))
            else:
                try:
                    X[col] = self.label_encoders[col].transform(X[col].astype(str))
                except ValueError:
                    mode_value = X[col].mode()[0] if not X[col].mode().empty else 0
                    X[col] = X[col].apply(lambda x: self.label_encoders[col].transform([str(x)])[0]
                                         if str(x) in self.label_encoders[col].classes_ else mode_value)

        # 最终数据清理
        numeric_cols = X.select_dtypes(include=[np.number]).columns
        categorical_cols = X.select_dtypes(include=['object']).columns

        if len(numeric_cols) > 0:
            X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())

        if len(categorical_cols) > 0:
            for col in categorical_cols:
                X[col] = X[col].fillna(X[col].mode()[0] if not X[col].mode().empty else 'unknown')

        logger.info(f"数据准备完成：{X.shape[1]}个特征，{len(categorical_features)}个分类特征")
        return X, y, categorical_features

    def expanded_hyperparameter_optimization(self, X: pd.DataFrame, y: pd.Series,
                                           categorical_features: List[str]) -> Dict[str, Any]:
        """扩大的超参数搜索空间优化"""
        logger.info("开始扩大超参数搜索空间优化")

        if not OPTUNA_AVAILABLE:
            logger.warning("Optuna不可用，使用默认参数")
            return self.train_baseline_models(X, y, categorical_features)

        optimization_results = {}

        # 1. XGBoost扩大搜索空间
        def optimize_xgboost_expanded(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 3000),  # 大幅扩大
                'max_depth': trial.suggest_int('max_depth', 3, 25),           # 扩大范围
                'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.3, log=True),  # 对数分布
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'colsample_bylevel': trial.suggest_float('colsample_bylevel', 0.6, 1.0),
                'colsample_bynode': trial.suggest_float('colsample_bynode', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 50, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 50, log=True),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 20),
                'gamma': trial.suggest_float('gamma', 0, 20),
                'max_delta_step': trial.suggest_int('max_delta_step', 0, 10),  # 新增
                'scale_pos_weight': trial.suggest_float('scale_pos_weight', 0.5, 2.0),  # 新增
                'random_state': 42
            }

            model = xgb.XGBRegressor(**params)

            # 使用时间序列交叉验证
            tscv = TimeSeriesSplit(n_splits=5)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("优化XGBoost超参数（扩大搜索空间）...")
        study_xgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=30),  # 增加启动试验
            pruner=MedianPruner(n_startup_trials=15, n_warmup_steps=20)
        )
        study_xgb.optimize(optimize_xgboost_expanded, n_trials=150, timeout=5400)  # 增加试验次数和时间

        optimization_results['XGBoost_Enhanced'] = {
            'best_params': study_xgb.best_params,
            'best_score': study_xgb.best_value,
            'study': study_xgb
        }

        # 2. LightGBM扩大搜索空间
        def optimize_lightgbm_expanded(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 3000),
                'max_depth': trial.suggest_int('max_depth', 3, 25),
                'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 50, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 50, log=True),
                'min_child_samples': trial.suggest_int('min_child_samples', 5, 200),
                'min_child_weight': trial.suggest_float('min_child_weight', 1e-3, 20, log=True),
                'num_leaves': trial.suggest_int('num_leaves', 10, 500),
                'feature_fraction': trial.suggest_float('feature_fraction', 0.6, 1.0),
                'bagging_fraction': trial.suggest_float('bagging_fraction', 0.6, 1.0),
                'bagging_freq': trial.suggest_int('bagging_freq', 1, 10),
                'min_data_in_leaf': trial.suggest_int('min_data_in_leaf', 10, 100),  # 新增
                'lambda_l1': trial.suggest_float('lambda_l1', 0, 10, log=True),      # 新增
                'lambda_l2': trial.suggest_float('lambda_l2', 0, 10, log=True),      # 新增
                'random_state': 42,
                'verbose': -1
            }

            model = lgb.LGBMRegressor(**params)

            tscv = TimeSeriesSplit(n_splits=5)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("优化LightGBM超参数（扩大搜索空间）...")
        study_lgb = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=30),
            pruner=MedianPruner(n_startup_trials=15, n_warmup_steps=20)
        )
        study_lgb.optimize(optimize_lightgbm_expanded, n_trials=150, timeout=5400)

        optimization_results['LightGBM_Enhanced'] = {
            'best_params': study_lgb.best_params,
            'best_score': study_lgb.best_value,
            'study': study_lgb
        }

        # 3. CatBoost扩大搜索空间
        if CATBOOST_AVAILABLE:
            def optimize_catboost_expanded(trial):
                params = {
                    'iterations': trial.suggest_int('iterations', 100, 3000),
                    'depth': trial.suggest_int('depth', 3, 16),
                    'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.3, log=True),
                    'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 0.1, 50, log=True),
                    'border_count': trial.suggest_int('border_count', 32, 255),
                    'bagging_temperature': trial.suggest_float('bagging_temperature', 0, 20),
                    'random_strength': trial.suggest_float('random_strength', 0, 20),
                    'one_hot_max_size': trial.suggest_int('one_hot_max_size', 2, 50),
                    'leaf_estimation_iterations': trial.suggest_int('leaf_estimation_iterations', 1, 20),
                    'leaf_estimation_method': trial.suggest_categorical('leaf_estimation_method', ['Newton', 'Gradient']),  # 新增
                    'bootstrap_type': trial.suggest_categorical('bootstrap_type', ['Bayesian', 'Bernoulli', 'MVS']),  # 新增
                    'subsample': trial.suggest_float('subsample', 0.6, 1.0),  # 新增
                    'random_state': 42,
                    'verbose': False
                }

                cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
                model = cb.CatBoostRegressor(cat_features=cat_features_idx, **params)

                tscv = TimeSeriesSplit(n_splits=5)
                scores = []

                for train_idx, val_idx in tscv.split(X):
                    X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                    y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                    model.fit(X_train_cv, y_train_cv)
                    y_pred_cv = model.predict(X_val_cv)

                    score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                    scores.append(score)

                return np.mean(scores)

            logger.info("优化CatBoost超参数（扩大搜索空间）...")
            study_cat = optuna.create_study(
                direction='minimize',
                sampler=TPESampler(seed=42, n_startup_trials=25),
                pruner=MedianPruner(n_startup_trials=12, n_warmup_steps=18)
            )
            study_cat.optimize(optimize_catboost_expanded, n_trials=120, timeout=4500)

            optimization_results['CatBoost_Enhanced'] = {
                'best_params': study_cat.best_params,
                'best_score': study_cat.best_value,
                'study': study_cat
            }

        # 4. RandomForest扩大搜索空间
        def optimize_randomforest_expanded(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 200, 2000),
                'max_depth': trial.suggest_int('max_depth', 5, 30),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', 0.3, 0.5, 0.7, 1.0]),
                'bootstrap': trial.suggest_categorical('bootstrap', [True, False]),
                'max_samples': trial.suggest_float('max_samples', 0.5, 1.0),  # 新增
                'min_weight_fraction_leaf': trial.suggest_float('min_weight_fraction_leaf', 0.0, 0.1),  # 新增
                'max_leaf_nodes': trial.suggest_int('max_leaf_nodes', 10, 1000),  # 新增
                'random_state': 42
            }

            model = RandomForestRegressor(**params)

            tscv = TimeSeriesSplit(n_splits=5)
            scores = []

            for train_idx, val_idx in tscv.split(X):
                X_train_cv, X_val_cv = X.iloc[train_idx], X.iloc[val_idx]
                y_train_cv, y_val_cv = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train_cv, y_train_cv)
                y_pred_cv = model.predict(X_val_cv)

                score = self.multi_objective_loss(y_val_cv.values, y_pred_cv)
                scores.append(score)

            return np.mean(scores)

        logger.info("优化RandomForest超参数（扩大搜索空间）...")
        study_rf = optuna.create_study(
            direction='minimize',
            sampler=TPESampler(seed=42, n_startup_trials=20),
            pruner=MedianPruner(n_startup_trials=10, n_warmup_steps=15)
        )
        study_rf.optimize(optimize_randomforest_expanded, n_trials=100, timeout=3600)

        optimization_results['RandomForest_Enhanced'] = {
            'best_params': study_rf.best_params,
            'best_score': study_rf.best_value,
            'study': study_rf
        }

        self.study_results = optimization_results
        logger.info("扩大超参数搜索空间优化完成")

        return optimization_results

    def train_optimized_models(self, X: pd.DataFrame, y: pd.Series,
                             categorical_features: List[str],
                             optimization_results: Dict[str, Any]) -> Dict[str, Any]:
        """使用优化后的超参数训练模型"""
        logger.info("使用优化后的超参数训练模型")

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        models = {}

        # 1. 训练优化后的XGBoost
        if 'XGBoost_Enhanced' in optimization_results:
            logger.info("训练优化后的XGBoost模型...")
            best_params = optimization_results['XGBoost_Enhanced']['best_params']

            xgb_model = xgb.XGBRegressor(**best_params)
            xgb_model.fit(X_train, y_train)
            y_pred_xgb = xgb_model.predict(X_test)

            models['XGBoost_Optimized'] = {
                'model': xgb_model,
                'mae': mean_absolute_error(y_test, y_pred_xgb),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_xgb)),
                'r2': r2_score(y_test, y_pred_xgb),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_xgb, 15),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_xgb
            }

        # 2. 训练优化后的LightGBM
        if 'LightGBM_Enhanced' in optimization_results:
            logger.info("训练优化后的LightGBM模型...")
            best_params = optimization_results['LightGBM_Enhanced']['best_params']

            lgb_model = lgb.LGBMRegressor(**best_params)
            lgb_model.fit(X_train, y_train)
            y_pred_lgb = lgb_model.predict(X_test)

            models['LightGBM_Optimized'] = {
                'model': lgb_model,
                'mae': mean_absolute_error(y_test, y_pred_lgb),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_lgb)),
                'r2': r2_score(y_test, y_pred_lgb),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_lgb, 15),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_lgb
            }

        # 3. 训练优化后的CatBoost
        if 'CatBoost_Enhanced' in optimization_results and CATBOOST_AVAILABLE:
            logger.info("训练优化后的CatBoost模型...")
            best_params = optimization_results['CatBoost_Enhanced']['best_params']

            cat_features_idx = [X.columns.get_loc(col) for col in categorical_features if col in X.columns]
            cat_model = cb.CatBoostRegressor(cat_features=cat_features_idx, **best_params)
            cat_model.fit(X_train, y_train)
            y_pred_cat = cat_model.predict(X_test)

            models['CatBoost_Optimized'] = {
                'model': cat_model,
                'mae': mean_absolute_error(y_test, y_pred_cat),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_cat)),
                'r2': r2_score(y_test, y_pred_cat),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_cat, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_cat, 15),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_cat
            }

        # 4. 训练优化后的RandomForest
        if 'RandomForest_Enhanced' in optimization_results:
            logger.info("训练优化后的RandomForest模型...")
            best_params = optimization_results['RandomForest_Enhanced']['best_params']

            rf_model = RandomForestRegressor(**best_params)
            rf_model.fit(X_train, y_train)
            y_pred_rf = rf_model.predict(X_test)

            models['RandomForest_Optimized'] = {
                'model': rf_model,
                'mae': mean_absolute_error(y_test, y_pred_rf),
                'rmse': np.sqrt(mean_squared_error(y_test, y_pred_rf)),
                'r2': r2_score(y_test, y_pred_rf),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, y_pred_rf, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, y_pred_rf, 15),
                'best_params': best_params,
                'y_test': y_test,
                'y_pred': y_pred_rf
            }

        # 5. 创建集成模型
        if len(models) >= 2:
            logger.info("创建集成模型...")

            # 基于性能的权重分配
            weights = {}
            total_accuracy = 0

            for name, result in models.items():
                accuracy = result['target_accuracy_20']
                mae = result['mae']

                # 综合权重：精度权重70% + MAE权重30%
                weight = (accuracy / 100) * 0.7 + (1 / (mae / 15)) * 0.3
                weights[name] = weight
                total_accuracy += accuracy

            # 归一化权重
            total_weight = sum(weights.values())
            for name in weights:
                weights[name] /= total_weight

            # 集成预测
            ensemble_pred = np.zeros(len(y_test))
            for name, result in models.items():
                ensemble_pred += weights[name] * result['y_pred']

            models['Ensemble_Optimized'] = {
                'mae': mean_absolute_error(y_test, ensemble_pred),
                'rmse': np.sqrt(mean_squared_error(y_test, ensemble_pred)),
                'r2': r2_score(y_test, ensemble_pred),
                'target_accuracy_20': self.calculate_target_accuracy(y_test.values, ensemble_pred, 20),
                'target_accuracy_15': self.calculate_target_accuracy(y_test.values, ensemble_pred, 15),
                'weights': weights,
                'y_test': y_test,
                'y_pred': ensemble_pred
            }

        self.models = models
        return models

def main():
    """主函数 - 阶段2：扩大超参数搜索空间"""
    logger.info("=== 阶段2：扩大超参数搜索空间 ===")
    logger.info("目标：在阶段1基础上，大幅扩大超参数搜索空间，提升模型性能")
    logger.info("预期提升：从74.4%提升到78-80%")

    try:
        # 1. 检查环境
        logger.info("=== 环境检查 ===")
        logger.info(f"TensorFlow可用: {TF_AVAILABLE}")
        logger.info(f"CatBoost可用: {CATBOOST_AVAILABLE}")
        logger.info(f"Optuna可用: {OPTUNA_AVAILABLE}")

        if not OPTUNA_AVAILABLE:
            logger.error("Optuna不可用，无法进行超参数优化")
            return

        # 2. 数据加载
        logger.info("=== 数据加载 ===")
        train_df = pd.read_excel('1-4521剔除重复20250514.xlsx')
        test_df = pd.read_excel('4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx')

        logger.info(f"训练数据: {train_df.shape}")
        logger.info(f"测试数据: {test_df.shape}")

        # 3. 创建优化器
        optimizer = Step2ExpandedHyperparameterOptimizer()

        # 4. 数据清理
        logger.info("=== 数据清理 ===")
        train_cleaned = optimizer.robust_data_cleaning(train_df)
        test_cleaned = optimizer.robust_data_cleaning(test_df)

        logger.info(f"训练数据清理后: {train_cleaned.shape}")
        logger.info(f"测试数据清理后: {test_cleaned.shape}")

        # 5. 增强特征工程
        logger.info("=== 增强特征工程 ===")
        train_features = optimizer.create_enhanced_features(train_cleaned)
        test_features = optimizer.create_enhanced_features(test_cleaned)

        # 6. 准备建模数据
        logger.info("=== 数据准备 ===")
        X_train, y_train, categorical_features = optimizer.prepare_data_for_models(train_features)
        X_test, y_test_dummy, _ = optimizer.prepare_data_for_models(test_features)

        # 确保训练和测试数据特征一致
        common_features = list(set(X_train.columns) & set(X_test.columns))
        X_train = X_train[common_features]
        X_test = X_test[common_features]

        logger.info(f"最终特征数: {len(common_features)}")
        logger.info(f"分类特征数: {len(categorical_features)}")
        logger.info(f"训练样本数: {len(X_train)}")
        logger.info(f"测试样本数: {len(X_test)}")

        # 7. 扩大超参数搜索空间优化
        logger.info("=== 扩大超参数搜索空间优化 ===")
        optimization_results = optimizer.expanded_hyperparameter_optimization(X_train, y_train, categorical_features)

        if not optimization_results:
            logger.error("超参数优化失败")
            return

        logger.info(f"成功优化{len(optimization_results)}个模型")

        # 8. 训练优化后的模型
        logger.info("=== 训练优化后的模型 ===")
        model_results = optimizer.train_optimized_models(X_train, y_train, categorical_features, optimization_results)

        if not model_results:
            logger.error("没有成功训练的模型")
            return

        logger.info(f"成功训练{len(model_results)}个模型")

        # 9. 结果评估
        logger.info("=== 结果评估 ===")

        # 显示各模型性能
        logger.info("各模型性能对比:")
        for name, result in model_results.items():
            logger.info(f"  {name}:")
            logger.info(f"    MAE: {result['mae']:.1f}°C")
            logger.info(f"    RMSE: {result['rmse']:.1f}°C")
            logger.info(f"    R²: {result['r2']:.4f}")
            logger.info(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%")
            logger.info(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%")

        # 找出最佳模型
        best_model_name = max(model_results.keys(), key=lambda x: model_results[x]['target_accuracy_20'])
        best_accuracy = model_results[best_model_name]['target_accuracy_20']

        logger.info(f"\n最佳模型: {best_model_name}")
        logger.info(f"最佳精度: {best_accuracy:.1f}%")

        # 10. 生成报告
        logger.info("=== 生成报告 ===")

        report_file = f"step2_expanded_hyperopt_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("阶段2：扩大超参数搜索空间报告\n")
            f.write("=" * 50 + "\n\n")

            f.write("🎯 目标: 扩大超参数搜索空间，提升模型性能\n")
            f.write("预期提升：从74.4%提升到78-80%\n\n")

            f.write("🔧 优化技术:\n")
            f.write("1. 大幅扩大超参数搜索范围\n")
            f.write("2. 增加新的超参数维度\n")
            f.write("3. 使用对数分布采样\n")
            f.write("4. 多目标优化损失函数\n")
            f.write("5. 时间序列交叉验证\n\n")

            f.write("📊 模型性能:\n")
            for name, result in model_results.items():
                f.write(f"  {name}:\n")
                f.write(f"    MAE: {result['mae']:.1f}°C\n")
                f.write(f"    目标范围±20°C精度: {result['target_accuracy_20']:.1f}%\n")
                f.write(f"    目标范围±15°C精度: {result['target_accuracy_15']:.1f}%\n\n")

            f.write(f"🏆 最佳模型: {best_model_name} ({best_accuracy:.1f}%)\n\n")

            # 计算提升幅度
            baseline_accuracy = 74.4  # 阶段1的最佳精度
            improvement = best_accuracy - baseline_accuracy

            f.write("📈 性能提升分析:\n")
            f.write(f"  阶段1最佳精度: {baseline_accuracy:.1f}%\n")
            f.write(f"  阶段2最佳精度: {best_accuracy:.1f}%\n")
            f.write(f"  绝对提升: +{improvement:.1f}%\n")
            f.write(f"  相对提升: +{improvement/baseline_accuracy*100:.1f}%\n\n")

            f.write("✅ 阶段2完成状态:\n")
            if best_accuracy >= 80:
                f.write("  🎉 超额完成目标！精度达到80%+\n")
                f.write("  ✅ 可以进入阶段3：实施多目标优化\n")
            elif best_accuracy >= 78:
                f.write("  🎯 成功达到目标！精度达到78%+\n")
                f.write("  ✅ 可以进入阶段3：实施多目标优化\n")
            elif improvement >= 2:
                f.write("  ⚡ 有显著提升！\n")
                f.write("  ✅ 可以进入阶段3，继续优化\n")
            else:
                f.write("  ⚠️ 提升有限，建议进一步调优\n")
                f.write("  🔧 可以尝试更多超参数组合\n")

        logger.info(f"报告已保存到: {report_file}")

        # 11. 最终总结
        logger.info("=== 阶段2总结 ===")
        logger.info(f"成功训练{len(model_results)}个优化模型")
        logger.info(f"最佳模型精度: {best_accuracy:.1f}%")
        logger.info(f"相比阶段1提升: +{improvement:.1f}%")

        if best_accuracy >= 80:
            logger.info("🎉🎉🎉 阶段2超额完成！可以进入阶段3！🎉🎉🎉")
        elif best_accuracy >= 78:
            logger.info("🎯 阶段2成功完成！可以进入阶段3！")
        elif improvement >= 2:
            logger.info("⚡ 阶段2有显著提升！可以进入阶段3！")
        else:
            logger.info("🔧 阶段2需要进一步优化")

        return model_results

    except Exception as e:
        logger.error(f"阶段2运行出错: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    main()
