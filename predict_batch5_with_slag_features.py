"""
第五批测试数据预测 - 整合炉渣成分特征版本
基于数据分析报告的Stage 1-3优化策略
- 移除Ridge和Lasso模型，保留SVR模型
- 添加炉渣成分特征
- 添加热平衡特征
- 应用最后2分钟添加材料的校正因子
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import pickle
from typing import Dict, List, Tuple, Union, Any
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
import xgboost as xgb
import lightgbm as lgb
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("batch5_slag_prediction.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SlagFeatureEngineer:
    """炉渣特征工程器"""

    def __init__(self):
        # 氧化反应系数
        self.oxidation_coeffs = {
            'Si_to_SiO2': 2.14, 'Mn_to_MnO': 1.29,
            'P_to_P2O5': 2.29, 'Fe_to_FeO': 1.29
        }

        # 造渣材料成分
        self.flux_compositions = {
            'lime': {'CaO': 0.88, 'MgO': 0.02, 'SiO2': 0.03},
            'dolomite': {'CaO': 0.32, 'MgO': 0.20, 'SiO2': 0.02},
            'limestone': {'CaO': 0.52, 'MgO': 0.02, 'SiO2': 0.04}
        }

        # 反应热数据（kJ/kg）
        self.reaction_heats = {
            'C_to_CO': 10100, 'C_to_CO2': 32800,
            'Si_oxidation': 30800, 'Mn_oxidation': 7200,
            'P_oxidation': 24000, 'Fe_oxidation': 4800
        }

    def safe_convert(self, value, default=0.0):
        """安全数值转换"""
        if pd.isna(value):
            return default
        try:
            return float(value)
        except:
            return default

    def create_slag_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建炉渣特征"""
        logger.info("开始创建炉渣特征")

        df_slag = df.copy()

        # 初始化炉渣特征列
        slag_features = [
            'slag_CaO_percent', 'slag_SiO2_percent', 'slag_FeO_percent',
            'slag_MgO_percent', 'slag_MnO_percent', 'slag_P2O5_percent',
            'slag_basicity', 'slag_rate', 'slag_iron_oxides_percent'
        ]

        for feature in slag_features:
            df_slag[feature] = 0.0

        # 计算各氧化物含量
        for idx, row in df_slag.iterrows():
            try:
                # 基础数据
                hot_metal_mass = self.safe_convert(row['铁水'], 90) * 1000  # kg

                # 氧化产物计算
                si_oxidized = hot_metal_mass * self.safe_convert(row['铁水SI'], 0.4) * 0.95 / 100
                sio2_from_si = si_oxidized * self.oxidation_coeffs['Si_to_SiO2']

                mn_oxidized = hot_metal_mass * self.safe_convert(row['铁水MN'], 0.17) * 0.80 / 100
                mno_from_mn = mn_oxidized * self.oxidation_coeffs['Mn_to_MnO']

                p_oxidized = hot_metal_mass * self.safe_convert(row['铁水P'], 0.13) * 0.85 / 100
                p2o5_from_p = p_oxidized * self.oxidation_coeffs['P_to_P2O5']

                fe_loss = hot_metal_mass * 0.015  # 1.5%铁损
                feo_from_fe = fe_loss * self.oxidation_coeffs['Fe_to_FeO']

                # 造渣材料贡献
                lime_mass = self.safe_convert(row.get('石灰', 0))
                dolomite_mass = self.safe_convert(row.get('白云石', 0))
                limestone_mass = self.safe_convert(row.get('石灰石', 0))

                # CaO计算
                cao_from_lime = lime_mass * self.flux_compositions['lime']['CaO']
                cao_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['CaO']
                cao_from_limestone = limestone_mass * self.flux_compositions['limestone']['CaO']
                total_cao = cao_from_lime + cao_from_dolomite + cao_from_limestone

                # MgO计算
                mgo_from_lime = lime_mass * self.flux_compositions['lime']['MgO']
                mgo_from_dolomite = dolomite_mass * self.flux_compositions['dolomite']['MgO']
                mgo_from_limestone = limestone_mass * self.flux_compositions['limestone']['MgO']
                mgo_from_refractory = hot_metal_mass * 0.15 / 1000  # 耐火材料贡献
                total_mgo = mgo_from_lime + mgo_from_dolomite + mgo_from_limestone + mgo_from_refractory

                # SiO2计算
                sio2_from_flux = (lime_mass * self.flux_compositions['lime']['SiO2'] +
                                 dolomite_mass * self.flux_compositions['dolomite']['SiO2'] +
                                 limestone_mass * self.flux_compositions['limestone']['SiO2'])
                sio2_from_refractory = hot_metal_mass * 0.08 / 1000
                total_sio2 = sio2_from_si + sio2_from_flux + sio2_from_refractory

                # 总炉渣量
                total_slag = total_cao + total_sio2 + feo_from_fe + total_mgo + mno_from_mn + p2o5_from_p

                if total_slag > 0:
                    # 炉渣成分百分比
                    df_slag.loc[idx, 'slag_CaO_percent'] = total_cao / total_slag * 100
                    df_slag.loc[idx, 'slag_SiO2_percent'] = total_sio2 / total_slag * 100
                    df_slag.loc[idx, 'slag_FeO_percent'] = feo_from_fe / total_slag * 100
                    df_slag.loc[idx, 'slag_MgO_percent'] = total_mgo / total_slag * 100
                    df_slag.loc[idx, 'slag_MnO_percent'] = mno_from_mn / total_slag * 100
                    df_slag.loc[idx, 'slag_P2O5_percent'] = p2o5_from_p / total_slag * 100

                    # 碱度
                    df_slag.loc[idx, 'slag_basicity'] = total_cao / total_sio2 if total_sio2 > 0 else 2.8

                    # 炉渣率
                    df_slag.loc[idx, 'slag_rate'] = total_slag / hot_metal_mass * 100

                    # 铁氧化物含量
                    df_slag.loc[idx, 'slag_iron_oxides_percent'] = feo_from_fe / total_slag * 100
                else:
                    # 默认值
                    df_slag.loc[idx, 'slag_CaO_percent'] = 45.0
                    df_slag.loc[idx, 'slag_SiO2_percent'] = 16.0
                    df_slag.loc[idx, 'slag_FeO_percent'] = 20.0
                    df_slag.loc[idx, 'slag_MgO_percent'] = 10.0
                    df_slag.loc[idx, 'slag_MnO_percent'] = 7.0
                    df_slag.loc[idx, 'slag_P2O5_percent'] = 2.0
                    df_slag.loc[idx, 'slag_basicity'] = 2.8
                    df_slag.loc[idx, 'slag_rate'] = 8.0
                    df_slag.loc[idx, 'slag_iron_oxides_percent'] = 20.0

            except Exception as e:
                logger.warning(f"计算第{idx}行炉渣特征时出错: {e}")
                continue

        logger.info("炉渣特征创建完成")
        return df_slag

    def create_thermal_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建热平衡特征"""
        logger.info("开始创建热平衡特征")

        df_thermal = df.copy()

        # 初始化热平衡特征列
        thermal_features = [
            'total_oxidation_heat', 'theoretical_temp_rise', 'theoretical_end_temp'
        ]

        for feature in thermal_features:
            df_thermal[feature] = 0.0

        for idx, row in df_thermal.iterrows():
            try:
                # 基础数据
                hot_metal_mass = self.safe_convert(row['铁水'], 90)
                scrap_mass = self.safe_convert(row.get('废钢', 0), 20)
                hot_metal_temp = self.safe_convert(row['铁水温度'], 1350)

                # 氧化反应热
                c_oxidized = hot_metal_mass * self.safe_convert(row['铁水C'], 4.2) * 0.88 / 100  # 88%脱碳率
                si_oxidized = hot_metal_mass * self.safe_convert(row['铁水SI'], 0.4) * 0.95 / 100
                mn_oxidized = hot_metal_mass * self.safe_convert(row['铁水MN'], 0.17) * 0.80 / 100
                p_oxidized = hot_metal_mass * self.safe_convert(row['铁水P'], 0.13) * 0.85 / 100

                # 考虑CO/CO2比例（70%CO, 30%CO2）
                co_ratio = 0.7
                co2_ratio = 0.3

                decarb_heat = (c_oxidized * co_ratio * self.reaction_heats['C_to_CO'] +
                              c_oxidized * co2_ratio * self.reaction_heats['C_to_CO2'])

                si_heat = si_oxidized * self.reaction_heats['Si_oxidation']
                mn_heat = mn_oxidized * self.reaction_heats['Mn_oxidation']
                p_heat = p_oxidized * self.reaction_heats['P_oxidation']

                total_oxidation_heat = decarb_heat + si_heat + mn_heat + p_heat
                df_thermal.loc[idx, 'total_oxidation_heat'] = total_oxidation_heat

                # 废钢熔化耗热
                cp_steel = 0.65  # kJ/kg·K
                melting_heat = 1200  # kJ/kg
                scrap_heat_consumption = scrap_mass * 1000 * (cp_steel * (1600 - 25) + melting_heat)

                # 净热量
                net_heat = total_oxidation_heat - scrap_heat_consumption

                # 理论温升
                total_steel = (hot_metal_mass + scrap_mass) * 1000
                cp_iron = 0.75  # kJ/kg·K
                if total_steel > 0:
                    temp_rise = net_heat / (total_steel * cp_iron)
                    df_thermal.loc[idx, 'theoretical_temp_rise'] = temp_rise
                    df_thermal.loc[idx, 'theoretical_end_temp'] = hot_metal_temp + temp_rise
                else:
                    df_thermal.loc[idx, 'theoretical_temp_rise'] = 0
                    df_thermal.loc[idx, 'theoretical_end_temp'] = hot_metal_temp

            except Exception as e:
                logger.warning(f"计算第{idx}行热平衡特征时出错: {e}")
                continue

        logger.info("热平衡特征创建完成")
        return df_thermal

def load_batch5_data(file_path: str) -> pd.DataFrame:
    """加载第五批测试数据并统一列名"""
    logger.info(f"加载第五批测试数据: {file_path}")
    try:
        data = pd.read_excel(file_path)
        logger.info(f"数据加载成功，原始形状: {data.shape}")

        # 统一列名，使其与训练数据一致
        column_mapping = {
            '炉子最大倾角': '最大角度',
            '平均流速M3/h': '气体流量流速平均',
            '最小流速': '最低流速',
            '最后2分钟加料': '最后2分钟',
            '总氧化碳(kg)': '气体总C'
        }

        # 重命名列
        for old_col, new_col in column_mapping.items():
            if old_col in data.columns:
                data.rename(columns={old_col: new_col}, inplace=True)
                logger.info(f"列名重命名: {old_col} -> {new_col}")

        logger.info(f"数据处理后形状: {data.shape}")
        return data
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        raise

def load_trained_models(models_dir: str = "results") -> Dict[str, Any]:
    """加载训练好的模型，排除Ridge和Lasso两个模型"""
    logger.info(f"从 {models_dir} 加载训练好的模型")
    models = {}

    # 加载顺序思维模型
    try:
        sequential_model_path = os.path.join(models_dir, "sequential_thinking_model.pkl")
        if os.path.exists(sequential_model_path):
            with open(sequential_model_path, 'rb') as f:
                models["Sequential Thinking Model"] = pickle.load(f)
            logger.info("顺序思维模型加载成功")
    except Exception as e:
        logger.error(f"加载顺序思维模型失败: {e}")

    # 加载基础模型，排除Ridge和Lasso两个模型
    base_model_names = ["xgboost", "lightgbm", "random_forest", "svr"]  # 移除了"ridge"和"lasso"
    for name in base_model_names:
        try:
            model_path = os.path.join(models_dir, f"{name}_model.pkl")
            if os.path.exists(model_path):
                with open(model_path, 'rb') as f:
                    models[name] = pickle.load(f)
                logger.info(f"{name} 模型加载成功")
        except Exception as e:
            logger.error(f"加载 {name} 模型失败: {e}")

    logger.info(f"共加载 {len(models)} 个模型")
    return models

def apply_material_correction(df: pd.DataFrame, predictions: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """应用最后2分钟添加材料的校正因子：每100kg降低5°C"""
    logger.info("应用最后2分钟添加材料的校正因子")

    corrected_predictions = {}

    # 检查是否有"最后2分钟"列
    if '最后2分钟' in df.columns:
        # 计算校正值：每100kg降低5°C
        correction = df['最后2分钟'] * (-5/100)
        logger.info(f"校正范围: {correction.min():.2f}°C 到 {correction.max():.2f}°C")

        # 应用校正到每个模型的预测结果
        for name, pred in predictions.items():
            corrected_predictions[name] = pred + correction.values
            logger.info(f"应用校正到 {name} 模型")
    else:
        logger.warning("数据中没有'最后2分钟'列，无法应用校正因子")
        corrected_predictions = predictions

    return corrected_predictions

def predict_based_on_metallurgy_and_slag(df: pd.DataFrame) -> np.ndarray:
    """基于冶金规律和炉渣特征进行预测"""
    logger.info("基于冶金规律和炉渣特征进行预测")

    # 创建预测结果数组
    predictions = np.zeros(len(df))

    # 对每个样本进行预测
    for i, (_, row) in enumerate(df.iterrows()):
        # 基础温度：铁水温度
        base_temp = row['铁水温度']
        predictions[i] = base_temp

        # 1. 基于理论终点温度
        if 'theoretical_end_temp' in row:
            theoretical_temp = row['theoretical_end_temp']
            if not pd.isna(theoretical_temp) and theoretical_temp > 1400:
                predictions[i] = theoretical_temp

        # 2. 考虑炉渣碱度的影响
        if 'slag_basicity' in row:
            basicity = row['slag_basicity']
            if basicity > 3.5:  # 碱度过高，温度可能偏低
                predictions[i] -= 10
            elif basicity < 2.0:  # 碱度过低，温度可能偏高
                predictions[i] += 15

        # 3. 考虑炉渣FeO含量的影响
        if 'slag_FeO_percent' in row:
            feo_percent = row['slag_FeO_percent']
            if feo_percent > 25:  # FeO含量高，氧化性强，温度高
                predictions[i] += 20
            elif feo_percent < 15:  # FeO含量低，温度可能偏低
                predictions[i] -= 10

        # 4. 考虑吹氧时间的影响
        if '吹氧时间s' in row:
            blow_time_min = row['吹氧时间s'] / 60  # 转换为分钟
            temp_increase_blow = min(80, blow_time_min * 2)  # 每分钟升温2°C，最多升温80°C
            predictions[i] += temp_increase_blow

        # 5. 考虑废钢比例的影响
        if '铁水' in row and '废钢' in row and row['铁水'] > 0:
            scrap_ratio = row['废钢'] / row['铁水']
            temp_decrease_scrap = scrap_ratio * 50  # 废钢比例每增加1，温度降低50°C
            predictions[i] -= temp_decrease_scrap

        # 6. 考虑钢种的影响
        if '钢种' in row:
            steel_type = str(row['钢种'])
            # 高碳钢需要更高温度
            if any(high_carbon in steel_type for high_carbon in ['65Mn', 'C72DA']):
                predictions[i] += 15
            # 低碳钢需要较低温度
            elif any(low_carbon in steel_type for low_carbon in ['Q235', 'A', 'B']):
                predictions[i] -= 10

        # 7. 温度上限和下限
        predictions[i] = max(1500, min(1650, predictions[i]))

    logger.info(f"冶金规律+炉渣特征预测完成，预测范围: {np.min(predictions):.2f}°C - {np.max(predictions):.2f}°C")
    return predictions

def predict_batch5(batch5_data: pd.DataFrame, models: Dict[str, Any]) -> pd.DataFrame:
    """对第五批数据进行预测"""
    logger.info("开始预处理第五批数据")

    # 创建特征工程器
    slag_engineer = SlagFeatureEngineer()

    # 创建炉渣特征
    batch5_with_slag = slag_engineer.create_slag_features(batch5_data)

    # 创建热平衡特征
    batch5_with_thermal = slag_engineer.create_thermal_features(batch5_with_slag)

    logger.info("特征工程完成")

    # 基于冶金规律和炉渣特征进行预测
    metallurgical_predictions = predict_based_on_metallurgy_and_slag(batch5_with_thermal)

    # 创建预测结果字典
    predictions = {
        "冶金规律+炉渣模型": metallurgical_predictions
    }

    # 应用最后2分钟添加材料的校正因子
    predictions = apply_material_correction(batch5_data, predictions)

    # 创建结果DataFrame
    logger.info("整合预测结果")
    results = pd.DataFrame()

    # 添加原始数据的关键列
    if '炉号' in batch5_data.columns:
        results['炉号'] = batch5_data['炉号']
    if '钢种' in batch5_data.columns:
        results['钢种'] = batch5_data['钢种']
    if '铁水温度' in batch5_data.columns:
        results['铁水温度'] = batch5_data['铁水温度']
    if '最后2分钟' in batch5_data.columns:
        results['最后2分钟添加量'] = batch5_data['最后2分钟']

    # 添加炉渣特征
    slag_features = ['slag_CaO_percent', 'slag_SiO2_percent', 'slag_FeO_percent', 'slag_basicity']
    for feature in slag_features:
        if feature in batch5_with_thermal.columns:
            results[feature] = batch5_with_thermal[feature]

    # 添加热平衡特征
    thermal_features = ['theoretical_end_temp', 'total_oxidation_heat']
    for feature in thermal_features:
        if feature in batch5_with_thermal.columns:
            results[feature] = batch5_with_thermal[feature]

    # 添加各模型的预测结果
    for name, pred in predictions.items():
        results[f'{name}_预测温度'] = pred

    # 使用冶金规律+炉渣模型的结果作为综合预测温度
    results['综合预测温度'] = predictions["冶金规律+炉渣模型"]

    logger.info("预测完成")
    return results

def save_prediction_results(results: pd.DataFrame, output_path: str = "batch5_predictions_with_slag.xlsx") -> None:
    """保存预测结果"""
    logger.info(f"保存预测结果到: {output_path}")
    try:
        results.to_excel(output_path, index=False)
        logger.info("预测结果保存成功")
    except Exception as e:
        logger.error(f"保存预测结果失败: {e}")
        raise

def analyze_prediction_results(results: pd.DataFrame) -> None:
    """分析预测结果"""
    logger.info("分析预测结果")

    # 计算综合预测温度的统计信息
    mean_temp = results['综合预测温度'].mean()
    std_temp = results['综合预测温度'].std()
    min_temp = results['综合预测温度'].min()
    max_temp = results['综合预测温度'].max()

    logger.info(f"综合预测温度统计:")
    logger.info(f"  平均值: {mean_temp:.2f}°C")
    logger.info(f"  标准差: {std_temp:.2f}°C")
    logger.info(f"  最小值: {min_temp:.2f}°C")
    logger.info(f"  最大值: {max_temp:.2f}°C")

    # 如果有钢种信息，按钢种分析
    if '钢种' in results.columns:
        logger.info("按钢种分析预测温度:")
        for steel_type, group in results.groupby('钢种'):
            logger.info(f"  钢种 {steel_type}:")
            logger.info(f"    样本数: {len(group)}")
            logger.info(f"    平均预测温度: {group['综合预测温度'].mean():.2f}°C")
            logger.info(f"    标准差: {group['综合预测温度'].std():.2f}°C")

    # 创建预测温度分布图
    plt.figure(figsize=(10, 6))
    plt.hist(results['综合预测温度'], bins=20, alpha=0.7)
    plt.axvline(mean_temp, color='r', linestyle='--', label=f'平均值: {mean_temp:.2f}°C')
    plt.title('第五批数据预测温度分布（炉渣特征版）')
    plt.xlabel('预测温度 (°C)')
    plt.ylabel('频次')
    plt.legend()
    plt.grid(alpha=0.3)
    plt.savefig('batch5_temperature_distribution_with_slag.png', dpi=300, bbox_inches='tight')
    logger.info("预测温度分布图已保存")

    # 如果有铁水温度，分析铁水温度与预测温度的关系
    if '铁水温度' in results.columns:
        plt.figure(figsize=(10, 6))
        plt.scatter(results['铁水温度'], results['综合预测温度'], alpha=0.5)
        plt.title('铁水温度与预测出钢温度关系（炉渣特征版）')
        plt.xlabel('铁水温度 (°C)')
        plt.ylabel('预测出钢温度 (°C)')
        plt.grid(alpha=0.3)
        plt.savefig('batch5_hotmetal_vs_prediction_with_slag.png', dpi=300, bbox_inches='tight')
        logger.info("铁水温度与预测温度关系图已保存")

def main():
    """主函数"""
    logger.info("开始处理第五批测试数据（炉渣特征版本）")

    # 第五批数据文件路径
    batch5_file = "4523-4905筛选后完整数据20250519（第五批测试数据）.xlsx"

    # 检查文件是否存在
    if not os.path.exists(batch5_file):
        logger.error(f"文件不存在: {batch5_file}")
        print(f"错误: 找不到文件 {batch5_file}")
        return

    # 加载第五批数据
    batch5_data = load_batch5_data(batch5_file)

    # 加载训练好的模型（排除Ridge和Lasso模型）
    models = load_trained_models()

    # 预测第五批数据
    results = predict_batch5(batch5_data, models)

    # 保存预测结果
    save_prediction_results(results)

    # 分析预测结果
    analyze_prediction_results(results)

    logger.info("第五批数据处理完成（炉渣特征版本）")
    print("第五批数据预测完成，结果已保存到 batch5_predictions_with_slag.xlsx")

if __name__ == "__main__":
    main()
